# 系统语言设置功能

## 功能概述

ADB进程监控工具新增了系统语言设置功能，可以通过adb命令快速更改Android设备的系统语言，支持多种常见语言的切换。

## 主要功能

### 1. 获取当前语言信息
- 检测设备当前的系统语言设置
- 显示支持的常见语言列表
- 提供语言代码和本地化名称对照

### 2. 设置系统语言
- 支持设置多种常见语言
- 自动处理语言代码和国家代码
- 多种设置方法确保兼容性
- 自动验证设置结果

### 3. 语言切换验证
- 实时验证语言设置是否成功
- 显示设置前后的语言对比
- 提供设置失败的详细错误信息

## 支持的语言

### 常见语言列表
| 语言代码 | 英文名称 | 本地名称 | 说明 |
|---------|---------|---------|------|
| en-US | English (United States) | English | 英语(美国) |
| en-GB | English (United Kingdom) | English (UK) | 英语(英国) |
| zh-CN | Chinese (Simplified) | 简体中文 | 简体中文 |
| zh-TW | Chinese (Traditional) | 繁體中文 | 繁体中文 |
| ja-JP | Japanese | 日本語 | 日语 |
| ko-KR | Korean | 한국어 | 韩语 |
| fr-FR | French | Français | 法语 |
| de-DE | German | Deutsch | 德语 |
| es-ES | Spanish | Español | 西班牙语 |
| it-IT | Italian | Italiano | 意大利语 |
| pt-BR | Portuguese (Brazil) | Português (Brasil) | 葡萄牙语(巴西) |
| ru-RU | Russian | Русский | 俄语 |
| ar-SA | Arabic | العربية | 阿拉伯语 |
| hi-IN | Hindi | हिन्दी | 印地语 |
| th-TH | Thai | ไทย | 泰语 |
| vi-VN | Vietnamese | Tiếng Việt | 越南语 |

## 使用方法

### 命令行使用

#### 1. 获取当前语言和支持的语言列表
```bash
python adb_process_monitor.py --get-languages
```

#### 2. 设置系统语言为英语
```bash
python adb_process_monitor.py --set-language en-US
```

#### 3. 设置系统语言为中文
```bash
python adb_process_monitor.py --set-language zh-CN
```

#### 4. 设置其他语言
```bash
# 日语
python adb_process_monitor.py --set-language ja-JP

# 韩语
python adb_process_monitor.py --set-language ko-KR

# 法语
python adb_process_monitor.py --set-language fr-FR

# 德语
python adb_process_monitor.py --set-language de-DE
```

#### 5. 简化语言代码（自动推断国家代码）
```bash
# 设置为英语（自动使用US）
python adb_process_monitor.py --set-language en

# 设置为中文（自动使用CN）
python adb_process_monitor.py --set-language zh
```

#### 6. 导出设置结果
```bash
python adb_process_monitor.py --set-language en-US --export language_result.json
python adb_process_monitor.py --get-languages --export languages_info.json
```

### 编程接口使用

```python
from adb_process_monitor import AdbProcessMonitor

# 创建监控器实例
monitor = AdbProcessMonitor()

# 1. 获取当前语言
current_locale = monitor._get_current_locale()
print(f"当前语言: {current_locale}")

# 2. 获取支持的语言信息
languages_result = monitor.get_supported_languages()
print(monitor.format_supported_languages(languages_result))

# 3. 设置系统语言为英语
result = monitor.set_system_language("en", "US")
print(monitor.format_language_result(result))

# 4. 设置系统语言为中文
result = monitor.set_system_language("zh", "CN")
if result.get('success'):
    print("语言设置成功！")
else:
    print(f"语言设置失败: {result.get('error')}")

# 5. 检查设置是否成功
new_locale = monitor._get_current_locale()
print(f"新的语言设置: {new_locale}")
```

## 输出示例

### 获取语言信息
```
🌍 设备语言信息
==================================================
📱 当前系统语言: en-US

🎯 常见语言列表 (16 种):
序号   代码       英文名称                      本地名称
------------------------------------------------------------
1    <USER>    <GROUP> (United States)   English         ✅
2    en-GB    English (United Kingdom)  English (UK)    
3    zh-CN    Chinese (Simplified)      简体中文
4    zh-TW    Chinese (Traditional)     繁體中文
5    ja-JP    Japanese                  日本語
...

💡 使用方法: --set-language <语言代码>
   示例: --set-language en-US
```

### 语言设置结果
```
🌍 系统语言设置结果
==================================================
✅ 设置状态: 成功
📝 结果消息: 成功将系统语言设置为 zh-CN
🔄 语言更改: en-US → zh-CN
```

## 技术实现

### 设置方法
1. **setprop命令**: 设置系统属性
   - `persist.sys.locale`
   - `persist.sys.language`
   - `persist.sys.country`

2. **am命令**: 发送系统广播
   - `android.intent.action.LOCALE_CHANGED`
   - 启动语言设置页面

3. **settings命令**: 修改系统设置
   - `system.system_locales`
   - `global.device_locales`

### 验证机制
- 多种方法获取当前语言设置
- 实时验证设置结果
- 错误处理和回退机制

## 注意事项

1. **权限要求**: 
   - 需要通过USB连接Android设备并启用USB调试
   - 某些设备可能需要root权限才能完全更改系统语言

2. **兼容性**: 
   - 不同厂商的系统可能对语言设置有不同的实现
   - 某些定制系统可能需要额外的权限

3. **生效时间**: 
   - 语言更改通常立即生效
   - 某些应用可能需要重启才能显示新语言
   - 建议重启设备以确保所有更改完全生效

4. **安全性**: 
   - 语言设置会影响整个系统界面
   - 请确保了解目标语言以便后续操作

## 测试脚本

可以使用提供的测试脚本来验证功能：

```bash
python tools/test_language_settings.py
```

该脚本会自动测试：
- 获取当前语言信息
- 设置不同语言
- 验证设置结果
- 处理无效语言代码

## 故障排除

### 常见问题

1. **设置失败**: 
   - 检查设备是否已启用USB调试
   - 确认adb连接正常
   - 尝试使用root权限

2. **部分生效**: 
   - 重启设备: `adb reboot`
   - 清除系统缓存
   - 检查系统版本兼容性

3. **无法恢复**: 
   - 使用设备的设置应用手动更改
   - 恢复出厂设置（最后手段）
