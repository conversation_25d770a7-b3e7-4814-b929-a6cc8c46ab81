# 录屏应用音频权限管理功能

## 功能概述

ADB进程监控工具新增了录屏应用音频权限管理功能，可以通过adb命令自动检测录屏应用并授予音频录制权限，解决录屏时无法录制音频的问题。

## 主要功能

### 1. 自动检测录屏应用
- 支持检测系统自带录屏应用（如Transsion、小米、华为、三星等）
- 支持检测第三方录屏应用（如AZ Screen Recorder、Mobizen等）
- 按优先级自动选择最合适的录屏应用

### 2. 权限状态检查
- 检查录屏应用的音频相关权限状态
- 显示详细的权限授予情况
- 识别缺失的权限并提供建议

### 3. 权限自动授予
- 批量授予音频录制相关权限
- 支持多种权限类型：
  - `RECORD_AUDIO` - 录制音频权限
  - `CAPTURE_AUDIO_OUTPUT` - 捕获音频输出权限
  - `MODIFY_AUDIO_SETTINGS` - 修改音频设置权限
  - `WRITE_EXTERNAL_STORAGE` - 写入外部存储权限
  - `READ_EXTERNAL_STORAGE` - 读取外部存储权限

## 使用方法

### 命令行使用

#### 1. 自动检测并授予权限
```bash
python adb_process_monitor.py --grant-audio-permission
```

#### 2. 给指定录屏应用授予权限
```bash
python adb_process_monitor.py --grant-audio-permission com.transsion.screenrecorder
```

#### 3. 检查录屏应用权限状态
```bash
python adb_process_monitor.py --check-audio-permission
```

#### 4. 检查指定应用权限状态
```bash
python adb_process_monitor.py --check-audio-permission com.transsion.screenrecorder
```

#### 5. 导出权限操作结果
```bash
python adb_process_monitor.py --grant-audio-permission --export permission_result.json
python adb_process_monitor.py --check-audio-permission --export permission_check.json
```

### 编程接口使用

```python
from adb_process_monitor import AdbProcessMonitor

# 创建监控器实例
monitor = AdbProcessMonitor()

# 1. 自动检测录屏应用
detected_app = monitor._detect_screen_recorder_app()
print(f"检测到录屏应用: {detected_app}")

# 2. 检查权限状态
check_result = monitor.check_screen_recorder_permissions()
print(monitor.format_permission_check_result(check_result))

# 3. 授予权限
grant_result = monitor.grant_screen_recorder_audio_permission()
print(monitor.format_permission_result(grant_result))

# 4. 检查指定应用权限
check_result = monitor.check_screen_recorder_permissions("com.transsion.screenrecorder")
has_audio_permission = check_result.get('has_audio_permission', False)
print(f"音频权限状态: {'已授予' if has_audio_permission else '未授予'}")
```

## 支持的录屏应用

### 系统自带录屏应用
- Transsion系统录屏: `com.transsion.screenrecorder`
- Android系统录屏: `com.android.systemui.screenrecord`
- 小米录屏: `com.miui.screenrecorder`
- 华为录屏: `com.huawei.screenrecorder`
- 三星录屏: `com.samsung.android.app.screenrecorder`
- OPPO录屏: `com.oppo.screenrecorder`
- vivo录屏: `com.vivo.screenrecorder`
- 一加录屏: `com.oneplus.screenrecorder`

### 第三方录屏应用
- AZ Screen Recorder: `com.hecorat.screenrecorder.free`
- Mobizen Screen Recorder: `com.mobizen.mirroring.uimode`
- DU Recorder: `com.duapps.recorder`
- Screen Recorder: `com.kimcy929.screenrecorder`

## 输出示例

### 权限检查结果
```
🔍 录屏应用音频权限检查结果
==================================================
📱 应用包名: com.transsion.screenrecorder
✅ 音频录制权限: 已授予

📋 权限详细状态:
  ✅ RECORD_AUDIO: 已授予
  ❌ CAPTURE_AUDIO_OUTPUT: 未授予
  ✅ MODIFY_AUDIO_SETTINGS: 已授予
  ✅ WRITE_EXTERNAL_STORAGE: 已授予
  ✅ READ_EXTERNAL_STORAGE: 已授予

⚠️ 缺失的权限 (1 个):
  1. CAPTURE_AUDIO_OUTPUT

💡 建议: 使用 --grant-audio-permission 参数授予缺失的权限
```

### 权限授予结果
```
🎙️ 录屏应用音频权限授予结果
==================================================
📱 应用包名: com.transsion.screenrecorder
✅ 授予状态: 成功
📝 结果消息: 成功授予 4/5 个权限

🎯 已授予的权限 (4 个):
  1. RECORD_AUDIO
  2. MODIFY_AUDIO_SETTINGS
  3. WRITE_EXTERNAL_STORAGE
  4. READ_EXTERNAL_STORAGE

❌ 授予失败的权限 (1 个):
  1. CAPTURE_AUDIO_OUTPUT
```

## 注意事项

1. **设备要求**: 需要通过USB连接Android设备并启用USB调试
2. **权限限制**: 某些系统级权限可能需要root权限才能授予
3. **兼容性**: 不同厂商的系统可能对权限管理有不同的实现
4. **安全性**: 权限授予操作会修改应用的权限状态，请确保操作的必要性

## 测试脚本

可以使用提供的测试脚本来验证功能：

```bash
python tools/test_screen_recorder_permissions.py
```

该脚本会自动检测录屏应用、检查权限状态，并在需要时授予权限。
