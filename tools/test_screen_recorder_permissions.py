#!/usr/bin/env python3
"""
录屏应用音频权限测试脚本
演示如何使用 AdbProcessMonitor 的录屏音频权限功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from adb_process_monitor import AdbProcessMonitor

def test_screen_recorder_permissions():
    """测试录屏应用音频权限功能"""
    print("🎙️ 录屏应用音频权限测试")
    print("=" * 50)
    
    # 创建监控器实例
    monitor = AdbProcessMonitor()
    
    print("\n1️⃣ 检测已安装的录屏应用...")
    detected_app = monitor._detect_screen_recorder_app()
    if detected_app:
        print(f"✅ 检测到录屏应用: {detected_app}")
    else:
        print("❌ 未检测到录屏应用")
        return
    
    print("\n2️⃣ 检查当前音频权限状态...")
    check_result = monitor.check_screen_recorder_permissions(detected_app)
    print(monitor.format_permission_check_result(check_result))
    
    # 如果缺少权限，询问是否授予
    if not check_result.get('has_audio_permission', False):
        print("\n3️⃣ 检测到缺少音频权限，正在授予权限...")
        grant_result = monitor.grant_screen_recorder_audio_permission(detected_app)
        print(monitor.format_permission_result(grant_result))
        
        if grant_result.get('success'):
            print("\n4️⃣ 重新检查权限状态...")
            final_check = monitor.check_screen_recorder_permissions(detected_app)
            print(monitor.format_permission_check_result(final_check))
    else:
        print("\n✅ 录屏应用已具有音频录制权限，无需额外操作")

def test_specific_package():
    """测试指定包名的权限操作"""
    print("\n🎯 指定包名权限测试")
    print("=" * 50)
    
    # 测试常见的录屏应用包名
    test_packages = [
        "com.transsion.screenrecorder",
        "com.android.systemui.screenrecord",
        "com.miui.screenrecorder"
    ]
    
    monitor = AdbProcessMonitor()
    
    for package_name in test_packages:
        print(f"\n📱 测试包名: {package_name}")
        
        # 检查是否已安装
        if monitor._is_package_installed(package_name):
            print(f"✅ 应用已安装")
            
            # 检查权限
            check_result = monitor.check_screen_recorder_permissions(package_name)
            has_audio = check_result.get('has_audio_permission', False)
            print(f"🎙️ 音频权限状态: {'已授予' if has_audio else '未授予'}")
            
        else:
            print(f"❌ 应用未安装")

def main():
    """主函数"""
    print("🚀 开始录屏应用音频权限测试")
    
    try:
        # 基本功能测试
        test_screen_recorder_permissions()
        
        # 指定包名测试
        test_specific_package()
        
        print("\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == '__main__':
    main()
