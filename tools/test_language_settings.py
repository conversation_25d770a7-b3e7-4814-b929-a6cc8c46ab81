#!/usr/bin/env python3
"""
系统语言设置测试脚本
演示如何使用 AdbProcessMonitor 的系统语言设置功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from adb_process_monitor import AdbProcessMonitor

def test_get_current_language():
    """测试获取当前语言功能"""
    print("🌍 测试获取当前语言功能")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 获取当前语言
    current_locale = monitor._get_current_locale()
    print(f"📱 当前系统语言: {current_locale}")
    
    # 获取支持的语言列表
    languages_result = monitor.get_supported_languages()
    print(monitor.format_supported_languages(languages_result))

def test_set_language_to_english():
    """测试设置语言为英语"""
    print("\n🇺🇸 测试设置语言为英语")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 设置为英语
    result = monitor.set_system_language("en", "US")
    print(monitor.format_language_result(result))
    
    return result.get('success', False)

def test_set_language_to_chinese():
    """测试设置语言为中文"""
    print("\n🇨🇳 测试设置语言为中文")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 设置为中文
    result = monitor.set_system_language("zh", "CN")
    print(monitor.format_language_result(result))
    
    return result.get('success', False)

def test_language_switching():
    """测试语言切换功能"""
    print("\n🔄 测试语言切换功能")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 获取初始语言
    initial_locale = monitor._get_current_locale()
    print(f"🏁 初始语言: {initial_locale}")
    
    # 测试语言列表
    test_languages = [
        ("en", "US", "English (US)"),
        ("zh", "CN", "简体中文"),
        ("ja", "JP", "日本語"),
        ("ko", "KR", "한국어")
    ]
    
    successful_changes = 0
    
    for language, country, display_name in test_languages:
        print(f"\n🎯 正在测试设置为: {display_name} ({language}-{country})")
        
        result = monitor.set_system_language(language, country)
        
        if result.get('success'):
            successful_changes += 1
            print(f"✅ 成功设置为 {language}-{country}")
            
            # 等待系统应用更改
            time.sleep(2)
            
            # 验证更改
            new_locale = monitor._get_current_locale()
            print(f"🔍 验证结果: {new_locale}")
        else:
            print(f"❌ 设置失败: {result.get('error', '未知错误')}")
        
        # 短暂等待
        time.sleep(1)
    
    print(f"\n📊 测试结果: {successful_changes}/{len(test_languages)} 个语言设置成功")
    
    # 恢复到初始语言
    if initial_locale and initial_locale != "unknown":
        print(f"\n🔄 恢复到初始语言: {initial_locale}")
        if '-' in initial_locale:
            lang, country = initial_locale.split('-', 1)
            restore_result = monitor.set_system_language(lang, country)
            if restore_result.get('success'):
                print("✅ 成功恢复到初始语言")
            else:
                print("❌ 恢复初始语言失败")

def test_invalid_language_codes():
    """测试无效语言代码的处理"""
    print("\n⚠️ 测试无效语言代码处理")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 测试无效的语言代码
    invalid_codes = [
        ("xx", "YY", "无效语言代码"),
        ("", "", "空语言代码"),
        ("123", "456", "数字语言代码")
    ]
    
    for language, country, description in invalid_codes:
        print(f"\n🧪 测试: {description} ({language}-{country})")
        
        result = monitor.set_system_language(language, country)
        
        if result.get('success'):
            print(f"⚠️ 意外成功: {result.get('message')}")
        else:
            print(f"✅ 正确处理无效代码: {result.get('error', '设置失败')}")

def main():
    """主函数"""
    print("🚀 开始系统语言设置功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试获取当前语言
        test_get_current_language()
        
        # 2. 测试设置为英语
        english_success = test_set_language_to_english()
        
        # 3. 测试设置为中文
        chinese_success = test_set_language_to_chinese()
        
        # 4. 测试语言切换
        test_language_switching()
        
        # 5. 测试无效语言代码
        test_invalid_language_codes()
        
        # 总结
        print("\n🎉 测试完成！")
        print("=" * 60)
        
        if english_success and chinese_success:
            print("✅ 所有基本语言设置测试通过")
        else:
            print("⚠️ 部分语言设置测试失败，请检查设备权限")
        
        print("\n💡 提示:")
        print("- 某些设备可能需要root权限才能完全更改系统语言")
        print("- 语言更改可能需要重启设备才能在所有应用中生效")
        print("- 使用 'adb reboot' 命令重启设备以确保更改完全生效")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == '__main__':
    main()
