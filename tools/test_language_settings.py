#!/usr/bin/env python3
"""
系统语言设置测试脚本
演示如何使用 AdbProcessMonitor 的系统语言设置功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from adb_process_monitor import AdbProcessMonitor

def test_get_current_language():
    """测试获取当前语言功能"""
    print("🌍 测试获取当前语言功能")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 获取当前语言
    current_locale = monitor._get_current_locale()
    print(f"📱 当前系统语言: {current_locale}")
    
    # 获取支持的语言列表
    languages_result = monitor.get_supported_languages()
    print(monitor.format_supported_languages(languages_result))

def test_set_language_to_english():
    """测试设置语言为英语"""
    print("\n🇺🇸 测试设置语言为英语")
    print("=" * 50)

    monitor = AdbProcessMonitor()

    # 先检查当前语言
    current_locale = monitor._get_current_locale()
    print(f"📱 当前系统语言: {current_locale}")

    # 如果当前已经是英语，则显示提示
    if current_locale and ('en' in current_locale.lower() or current_locale.startswith('en')):
        print("✅ 系统语言已经是英语，无需更改")
        return True

    # 设置为英语
    print("🔄 正在将系统语言设置为英语...")
    result = monitor.set_system_language("en", "US")
    print(monitor.format_language_result(result))

    # 验证设置结果
    if result.get('success'):
        # 等待系统应用更改
        time.sleep(2)
        new_locale = monitor._get_current_locale()
        print(f"🔍 设置后的语言: {new_locale}")

        if 'en' in new_locale.lower():
            print("✅ 英语设置验证成功")
            return True
        else:
            print("⚠️ 语言设置可能需要重启设备才能完全生效")
            return True  # 仍然认为设置成功，只是需要重启

    return result.get('success', False)

def test_set_language_to_chinese():
    """测试设置语言为中文"""
    print("\n🇨🇳 测试设置语言为中文")
    print("=" * 50)

    monitor = AdbProcessMonitor()

    # 先检查当前语言
    current_locale = monitor._get_current_locale()
    print(f"📱 当前系统语言: {current_locale}")

    # 如果当前已经是中文，则显示提示
    if current_locale and ('zh' in current_locale.lower() or 'hans' in current_locale.lower() or 'hant' in current_locale.lower()):
        print("✅ 系统语言已经是中文，无需更改")
        return True

    # 设置为中文
    print("🔄 正在将系统语言设置为中文...")
    result = monitor.set_system_language("zh", "CN")
    print(monitor.format_language_result(result))

    # 验证设置结果
    if result.get('success'):
        # 等待系统应用更改
        time.sleep(2)
        new_locale = monitor._get_current_locale()
        print(f"🔍 设置后的语言: {new_locale}")

        if 'zh' in new_locale.lower() or 'hans' in new_locale.lower():
            print("✅ 中文设置验证成功")
            return True
        else:
            print("⚠️ 语言设置可能需要重启设备才能完全生效")
            return True  # 仍然认为设置成功，只是需要重启

    return result.get('success', False)

def test_language_switching():
    """测试语言切换功能"""
    print("\n🔄 测试语言切换功能")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 获取初始语言
    initial_locale = monitor._get_current_locale()
    print(f"🏁 初始语言: {initial_locale}")
    
    # 测试语言列表
    test_languages = [
        ("en", "US", "English (US)"),
        ("zh", "CN", "简体中文"),
        ("ja", "JP", "日本語"),
        ("ko", "KR", "한국어")
    ]
    
    successful_changes = 0
    
    for language, country, display_name in test_languages:
        print(f"\n🎯 正在测试设置为: {display_name} ({language}-{country})")
        
        result = monitor.set_system_language(language, country)
        
        if result.get('success'):
            successful_changes += 1
            print(f"✅ 成功设置为 {language}-{country}")
            
            # 等待系统应用更改
            time.sleep(2)
            
            # 验证更改
            new_locale = monitor._get_current_locale()
            print(f"🔍 验证结果: {new_locale}")
        else:
            print(f"❌ 设置失败: {result.get('error', '未知错误')}")
        
        # 短暂等待
        time.sleep(1)
    
    print(f"\n📊 测试结果: {successful_changes}/{len(test_languages)} 个语言设置成功")
    
    # 恢复到初始语言
    if initial_locale and initial_locale != "unknown":
        print(f"\n🔄 恢复到初始语言: {initial_locale}")
        if '-' in initial_locale:
            lang, country = initial_locale.split('-', 1)
            restore_result = monitor.set_system_language(lang, country)
            if restore_result.get('success'):
                print("✅ 成功恢复到初始语言")
            else:
                print("❌ 恢复初始语言失败")

def test_invalid_language_codes():
    """测试无效语言代码的处理"""
    print("\n⚠️ 测试无效语言代码处理")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 测试无效的语言代码
    invalid_codes = [
        ("xx", "YY", "无效语言代码"),
        ("", "", "空语言代码"),
        ("123", "456", "数字语言代码")
    ]
    
    for language, country, description in invalid_codes:
        print(f"\n🧪 测试: {description} ({language}-{country})")
        
        result = monitor.set_system_language(language, country)
        
        if result.get('success'):
            print(f"⚠️ 意外成功: {result.get('message')}")
        else:
            print(f"✅ 正确处理无效代码: {result.get('error', '设置失败')}")

def test_current_language_scenario():
    """根据当前语言情况进行智能测试"""
    print("\n🎯 智能语言测试（基于当前语言）")
    print("=" * 50)

    monitor = AdbProcessMonitor()
    current_locale = monitor._get_current_locale()
    print(f"📱 检测到当前语言: {current_locale}")

    # 根据当前语言决定测试策略
    if 'zh' in current_locale.lower() or 'hans' in current_locale.lower():
        print("🇨🇳 当前是中文系统，测试切换到英语")
        return test_set_language_to_english()
    elif 'en' in current_locale.lower():
        print("🇺🇸 当前是英语系统，测试切换到中文")
        return test_set_language_to_chinese()
    else:
        print(f"🌍 当前是其他语言系统({current_locale})，测试切换到英语")
        return test_set_language_to_english()

def main():
    """主函数"""
    print("🚀 开始系统语言设置功能测试")
    print("=" * 60)

    try:
        # 1. 测试获取当前语言
        test_get_current_language()

        # 2. 智能语言测试（根据当前语言选择测试方案）
        smart_test_success = test_current_language_scenario()

        # 3. 测试语言切换（如果智能测试成功）
        if smart_test_success:
            print("\n✅ 基本语言切换测试成功，继续进行高级测试...")
            test_language_switching()
        else:
            print("\n⚠️ 基本语言切换测试失败，跳过高级测试")

        # 4. 测试无效语言代码
        test_invalid_language_codes()

        # 总结
        print("\n🎉 测试完成！")
        print("=" * 60)

        if smart_test_success:
            print("✅ 语言设置功能测试通过")
            print("💡 建议: 可以尝试重启设备以确保所有更改完全生效")
        else:
            print("⚠️ 语言设置测试失败，可能的原因:")
            print("  - 设备需要root权限")
            print("  - 系统版本不兼容")
            print("  - USB调试权限不足")

        print("\n📋 使用说明:")
        print("- 某些设备可能需要root权限才能完全更改系统语言")
        print("- 语言更改可能需要重启设备才能在所有应用中生效")
        print("- 使用 'adb reboot' 命令重启设备以确保更改完全生效")
        print("- 如果设置失败，可以手动在设备设置中更改语言")

    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        print("💡 建议检查:")
        print("  - adb连接是否正常")
        print("  - 设备是否启用USB调试")
        print("  - 是否有足够的权限")

if __name__ == '__main__':
    main()
