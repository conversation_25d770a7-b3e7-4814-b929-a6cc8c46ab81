{"uid": "5178d8d9d1b3b607", "name": "测试find a restaurant near me能正常执行", "fullName": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me.TestEllaFindRestaurantNearMe#test_find_a_restaurant_near_me", "historyId": "918c52f1eb9803594ff76c724b43d5f8", "time": {"start": 1756128848099, "stop": 1756128884099, "duration": 36000}, "description": "find a restaurant near me", "descriptionHtml": "<p>find a restaurant near me</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128835302, "stop": 1756128848097, "duration": 12795}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128848097, "stop": 1756128848097, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "find a restaurant near me", "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1756128848099, "stop": 1756128883838, "duration": 35739}, "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1756128848099, "stop": 1756128883575, "duration": 35476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128883575, "stop": 1756128883838, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "56086184827d97c1", "name": "测试总结", "source": "56086184827d97c1.txt", "type": "text/plain", "size": 736}, {"uid": "8878916378f04f2b", "name": "test_completed", "source": "8878916378f04f2b.png", "type": "image/png", "size": 161835}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证已打开", "time": {"start": 1756128883838, "stop": 1756128883838, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128883838, "stop": 1756128884099, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "749d8ca80e9bcc42", "name": "测试总结", "source": "749d8ca80e9bcc42.txt", "type": "text/plain", "size": 736}, {"uid": "f9e18121fb70945a", "name": "test_completed", "source": "f9e18121fb70945a.png", "type": "image/png", "size": 161835}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "79547095a291bf10", "name": "stdout", "source": "79547095a291bf10.txt", "type": "text/plain", "size": 16588}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128884100, "stop": 1756128884100, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128884101, "stop": 1756128885553, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_find_a_restaurant_near_me"}, {"name": "subSuite", "value": "TestEllaFindRestaurantNearMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5178d8d9d1b3b607.json", "parameterValues": []}