{"uid": "69f48050ac533a7", "name": "测试call mom through whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_call_mom_through_whatsapp.TestEllaCallMomThroughWhatsapp#test_call_mom_through_whatsapp", "historyId": "bd9c64dabd06671b98d60748492be267", "time": {"start": 1756119346119, "stop": 1756119380367, "duration": 34248}, "description": "call mom through whatsapp", "descriptionHtml": "<p>call mom through whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119333261, "stop": 1756119346117, "duration": 12856}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119346117, "stop": 1756119346117, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "call mom through whatsapp", "status": "passed", "steps": [{"name": "执行命令: call mom through whatsapp", "time": {"start": 1756119346119, "stop": 1756119380151, "duration": 34032}, "status": "passed", "steps": [{"name": "执行命令: call mom through whatsapp", "time": {"start": 1756119346119, "stop": 1756119379864, "duration": 33745}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119379864, "stop": 1756119380151, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "32b4a8f5c58926d8", "name": "测试总结", "source": "32b4a8f5c58926d8.txt", "type": "text/plain", "size": 323}, {"uid": "3c58d3585f38049b", "name": "test_completed", "source": "3c58d3585f38049b.png", "type": "image/png", "size": 181229}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119380151, "stop": 1756119380154, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119380154, "stop": 1756119380366, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "1156e27f11e36c33", "name": "测试总结", "source": "1156e27f11e36c33.txt", "type": "text/plain", "size": 323}, {"uid": "ae2e462a739aefa3", "name": "test_completed", "source": "ae2e462a739aefa3.png", "type": "image/png", "size": 181229}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d44793c46802ef0b", "name": "stdout", "source": "d44793c46802ef0b.txt", "type": "text/plain", "size": 13141}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119380368, "stop": 1756119380368, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119380369, "stop": 1756119381746, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_call_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaCallMomThroughWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_call_mom_through_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "69f48050ac533a7.json", "parameterValues": []}