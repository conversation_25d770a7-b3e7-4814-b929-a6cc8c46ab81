{"uid": "765d41de54f593b8", "name": "测试disable touch optimization返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization.TestEllaDisableTouchOptimization#test_disable_touch_optimization", "historyId": "0b659537bc9c9b47c2c23f702fadd56b", "time": {"start": 1756131126232, "stop": 1756131152655, "duration": 26423}, "description": "验证disable touch optimization指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable touch optimization指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131113288, "stop": 1756131126160, "duration": 12872}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131126160, "stop": 1756131126160, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable touch optimization指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "time": {"start": 1756131126232, "stop": 1756131152447, "duration": 26215}, "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "time": {"start": 1756131126232, "stop": 1756131152178, "duration": 25946}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131152178, "stop": 1756131152447, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "2ea495e9705a1fbd", "name": "测试总结", "source": "2ea495e9705a1fbd.txt", "type": "text/plain", "size": 349}, {"uid": "3086a339303e0753", "name": "test_completed", "source": "3086a339303e0753.png", "type": "image/png", "size": 185960}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131152447, "stop": 1756131152448, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131152449, "stop": 1756131152655, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "bdb1baa9c5aec896", "name": "测试总结", "source": "bdb1baa9c5aec896.txt", "type": "text/plain", "size": 349}, {"uid": "1d91f7f1274c02ec", "name": "test_completed", "source": "1d91f7f1274c02ec.png", "type": "image/png", "size": 185960}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "39974898c94be1b2", "name": "stdout", "source": "39974898c94be1b2.txt", "type": "text/plain", "size": 12659}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131152658, "stop": 1756131152658, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131152659, "stop": 1756131154087, "duration": 1428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaDisableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "765d41de54f593b8.json", "parameterValues": []}