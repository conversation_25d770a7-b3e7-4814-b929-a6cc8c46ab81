{"uid": "ef0f1871c66bc00c", "name": "测试disable brightness locking返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_brightness_locking.TestEllaDisableBrightnessLocking#test_disable_brightness_locking", "historyId": "edbb2ef8b0440e0325be2bfae4eb0bee", "time": {"start": 1756130854723, "stop": 1756130880421, "duration": 25698}, "description": "验证disable brightness locking指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable brightness locking指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130841969, "stop": 1756130854721, "duration": 12752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130854722, "stop": 1756130854722, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable brightness locking指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable brightness locking", "time": {"start": 1756130854724, "stop": 1756130880199, "duration": 25475}, "status": "passed", "steps": [{"name": "执行命令: disable brightness locking", "time": {"start": 1756130854724, "stop": 1756130879919, "duration": 25195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130879919, "stop": 1756130880198, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "f3f544a1ec8cecb6", "name": "测试总结", "source": "f3f544a1ec8cecb6.txt", "type": "text/plain", "size": 351}, {"uid": "36fcb77215051258", "name": "test_completed", "source": "36fcb77215051258.png", "type": "image/png", "size": 190501}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130880199, "stop": 1756130880201, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130880201, "stop": 1756130880419, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "8914fceddf5e5486", "name": "测试总结", "source": "8914fceddf5e5486.txt", "type": "text/plain", "size": 351}, {"uid": "f7b8892f01e5df0a", "name": "test_completed", "source": "f7b8892f01e5df0a.png", "type": "image/png", "size": 190501}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1256f59be73b810b", "name": "stdout", "source": "1256f59be73b810b.txt", "type": "text/plain", "size": 12594}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756130880421, "stop": 1756130881849, "duration": 1428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756130880421, "stop": 1756130880421, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_brightness_locking"}, {"name": "subSuite", "value": "TestEllaDisableBrightnessLocking"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_brightness_locking"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ef0f1871c66bc00c.json", "parameterValues": []}