{"uid": "318bede631c79316", "name": "测试switched to data mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switched_to_data_mode.TestEllaSwitchedDataMode#test_switched_to_data_mode", "historyId": "063471c2e7f2d00ecd08e780860e0cf2", "time": {"start": 1756126967849, "stop": 1756126995461, "duration": 27612}, "description": "switched to data mode", "descriptionHtml": "<p>switched to data mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126955151, "stop": 1756126967848, "duration": 12697}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126967848, "stop": 1756126967848, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switched to data mode", "status": "passed", "steps": [{"name": "执行命令: switched to data mode", "time": {"start": 1756126967849, "stop": 1756126995257, "duration": 27408}, "status": "passed", "steps": [{"name": "执行命令: switched to data mode", "time": {"start": 1756126967849, "stop": 1756126995002, "duration": 27153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126995002, "stop": 1756126995256, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "2b75d8739e4efeda", "name": "测试总结", "source": "2b75d8739e4efeda.txt", "type": "text/plain", "size": 232}, {"uid": "5606ad4e2ac2c9a0", "name": "test_completed", "source": "5606ad4e2ac2c9a0.png", "type": "image/png", "size": 161685}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756126995257, "stop": 1756126995258, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756126995258, "stop": 1756126995258, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126995258, "stop": 1756126995461, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "1063581d0557d23e", "name": "测试总结", "source": "1063581d0557d23e.txt", "type": "text/plain", "size": 232}, {"uid": "21134ec993f117c4", "name": "test_completed", "source": "21134ec993f117c4.png", "type": "image/png", "size": 161491}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8836035ecc6c3a9b", "name": "stdout", "source": "8836035ecc6c3a9b.txt", "type": "text/plain", "size": 14466}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126995462, "stop": 1756126995462, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126995464, "stop": 1756126996862, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switched_to_data_mode"}, {"name": "subSuite", "value": "TestEllaSwitchedDataMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switched_to_data_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "318bede631c79316.json", "parameterValues": []}