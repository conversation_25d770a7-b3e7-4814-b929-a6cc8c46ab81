{"uid": "9ed28d09f479cd76", "name": "测试Summarize what I'm reading能正常执行", "fullName": "testcases.test_ella.self_function.test_summarize_what_i_m_reading.TestEllaSummarizeWhatIMReading#test_summarize_what_i_m_reading", "historyId": "981b71ad744b9a603c31ab4832ff9439", "time": {"start": 1756123582959, "stop": 1756123654748, "duration": 71789}, "description": "Summarize what I'm reading", "descriptionHtml": "<p>Summarize what I'm reading</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123570239, "stop": 1756123582958, "duration": 12719}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123582958, "stop": 1756123582958, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Summarize what I'm reading", "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "time": {"start": 1756123582981, "stop": 1756123654477, "duration": 71496}, "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "time": {"start": 1756123582981, "stop": 1756123654217, "duration": 71236}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123654218, "stop": 1756123654476, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "eb76a04707fee70c", "name": "测试总结", "source": "eb76a04707fee70c.txt", "type": "text/plain", "size": 1348}, {"uid": "1ff0815796c38da0", "name": "test_completed", "source": "1ff0815796c38da0.png", "type": "image/png", "size": 337639}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123654477, "stop": 1756123654480, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123654480, "stop": 1756123654748, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "b92a8922e8f9efcc", "name": "测试总结", "source": "b92a8922e8f9efcc.txt", "type": "text/plain", "size": 1301}, {"uid": "6846b3d6b671e4ac", "name": "test_completed", "source": "6846b3d6b671e4ac.png", "type": "image/png", "size": 337828}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9718c5f294fdbaa8", "name": "stdout", "source": "9718c5f294fdbaa8.txt", "type": "text/plain", "size": 21751}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123654754, "stop": 1756123654754, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123654756, "stop": 1756123656131, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaSummarizeWhatIMReading"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_summarize_what_i_m_reading"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9ed28d09f479cd76.json", "parameterValues": []}