{"uid": "36fb58ef33fc53d3", "name": "测试boost phone能正常执行", "fullName": "testcases.test_ella.system_coupling.test_boost_phone.TestEllaBoostPhone#test_boost_phone", "historyId": "29390733aaf67e070f7c061b70bad8a5", "time": {"start": 1756123791329, "stop": 1756123817363, "duration": 26034}, "description": "boost phone", "descriptionHtml": "<p>boost phone</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123778843, "stop": 1756123791327, "duration": 12484}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123791328, "stop": 1756123791328, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "boost phone", "status": "passed", "steps": [{"name": "执行命令: boost phone", "time": {"start": 1756123791329, "stop": 1756123817103, "duration": 25774}, "status": "passed", "steps": [{"name": "执行命令: boost phone", "time": {"start": 1756123791329, "stop": 1756123816851, "duration": 25522}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123816851, "stop": 1756123817103, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "cbebd1290e51ab3d", "name": "测试总结", "source": "cbebd1290e51ab3d.txt", "type": "text/plain", "size": 258}, {"uid": "3308d58c13d8661", "name": "test_completed", "source": "3308d58c13d8661.png", "type": "image/png", "size": 174569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123817103, "stop": 1756123817105, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123817105, "stop": 1756123817363, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "1021741127061c22", "name": "测试总结", "source": "1021741127061c22.txt", "type": "text/plain", "size": 258}, {"uid": "a8d23f4aad6e92ed", "name": "test_completed", "source": "a8d23f4aad6e92ed.png", "type": "image/png", "size": 174569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "98bcf5fa766fd002", "name": "stdout", "source": "98bcf5fa766fd002.txt", "type": "text/plain", "size": 12312}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123817364, "stop": 1756123817365, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123817418, "stop": 1756123818737, "duration": 1319}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_boost_phone"}, {"name": "subSuite", "value": "TestEllaBoostPhone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_boost_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "36fb58ef33fc53d3.json", "parameterValues": []}