{"uid": "8829620b7d22014", "name": "测试turn off auto rotate screen能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_auto_rotate_screen.TestEllaTurnOffAutoRotateScreen#test_turn_off_auto_rotate_screen", "historyId": "7af47ccbdaf69e5292e05041f822c0c7", "time": {"start": 1756127416702, "stop": 1756127442005, "duration": 25303}, "description": "turn off auto rotate screen", "descriptionHtml": "<p>turn off auto rotate screen</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127404027, "stop": 1756127416699, "duration": 12672}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127416700, "stop": 1756127416700, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn off auto rotate screen", "status": "passed", "steps": [{"name": "执行命令: turn off auto rotate screen", "time": {"start": 1756127416702, "stop": 1756127441786, "duration": 25084}, "status": "passed", "steps": [{"name": "执行命令: turn off auto rotate screen", "time": {"start": 1756127416702, "stop": 1756127441538, "duration": 24836}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127441538, "stop": 1756127441786, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "4f4020ba5ea37d94", "name": "测试总结", "source": "4f4020ba5ea37d94.txt", "type": "text/plain", "size": 330}, {"uid": "44b010d9815f272c", "name": "test_completed", "source": "44b010d9815f272c.png", "type": "image/png", "size": 169092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127441786, "stop": 1756127441788, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127441788, "stop": 1756127441788, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127441788, "stop": 1756127442004, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "feaed2f3eb2107bf", "name": "测试总结", "source": "feaed2f3eb2107bf.txt", "type": "text/plain", "size": 330}, {"uid": "1e2e2fce7f92d9f5", "name": "test_completed", "source": "1e2e2fce7f92d9f5.png", "type": "image/png", "size": 169092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8b7302d43c95155f", "name": "stdout", "source": "8b7302d43c95155f.txt", "type": "text/plain", "size": 13174}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127442006, "stop": 1756127442006, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127442009, "stop": 1756127443368, "duration": 1359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_auto_rotate_screen"}, {"name": "subSuite", "value": "TestEllaTurnOffAutoRotateScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_auto_rotate_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8829620b7d22014.json", "parameterValues": []}