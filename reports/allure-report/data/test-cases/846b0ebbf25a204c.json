{"uid": "846b0ebbf25a204c", "name": "测试set phone number返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber#test_set_phone_number", "historyId": "8e367b8da758818b9a0fe21deca7ec48", "time": {"start": 1756137124198, "stop": 1756137162525, "duration": 38327}, "description": "验证set phone number指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phone number指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set phone number', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] My Phone | TECNO CAMON 40 Premier 5G | Model | TECNO CM8 | Dimensity 8350 Ultimate | 12.00 GB***** GB | Rear Camera | 50M OIS+50M OIS+50M | Front Camera | 50M | Battery | 5100mAh | Screen | 1080x2400 | Android Version | Version Info | Phone Information | E-Warranty Card']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber object at 0x000002920577B9D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A86ED50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_phone_number(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set phone number', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] My Phone | TECNO CAMON 40 Premier 5G | Model | TECNO CM8 | Dimensity 8350 Ultimate | 12.00 GB***** GB | Rear Camera | 50M OIS+50M OIS+50M | Front Camera | 50M | Battery | 5100mAh | Screen | 1080x2400 | Android Version | Version Info | Phone Information | E-Warranty Card']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_phone_number.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137110736, "stop": 1756137124174, "duration": 13438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137124175, "stop": 1756137124175, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set phone number指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set phone number', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] My Phone | TECNO CAMON 40 Premier 5G | Model | TECNO CM8 | Dimensity 8350 Ultimate | 12.00 GB***** GB | Rear Camera | 50M OIS+50M OIS+50M | Front Camera | 50M | Battery | 5100mAh | Screen | 1080x2400 | Android Version | Version Info | Phone Information | E-Warranty Card']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber object at 0x000002920577B9D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A86ED50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_phone_number(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set phone number', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] My Phone | TECNO CAMON 40 Premier 5G | Model | TECNO CM8 | Dimensity 8350 Ultimate | 12.00 GB***** GB | Rear Camera | 50M OIS+50M OIS+50M | Front Camera | 50M | Battery | 5100mAh | Screen | 1080x2400 | Android Version | Version Info | Phone Information | E-Warranty Card']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_phone_number.py:33: AssertionError", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1756137124198, "stop": 1756137162519, "duration": 38321}, "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1756137124198, "stop": 1756137162261, "duration": 38063}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137162261, "stop": 1756137162519, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "78d933208c8d1255", "name": "测试总结", "source": "78d933208c8d1255.txt", "type": "text/plain", "size": 577}, {"uid": "e99539c87e372a6d", "name": "test_completed", "source": "e99539c87e372a6d.png", "type": "image/png", "size": 166625}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137162519, "stop": 1756137162524, "duration": 5}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set phone number', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] My Phone | TECNO CAMON 40 Premier 5G | Model | TECNO CM8 | Dimensity 8350 Ultimate | 12.00 GB***** GB | Rear Camera | 50M OIS+50M OIS+50M | Front Camera | 50M | Battery | 5100mAh | Screen | 1080x2400 | Android Version | Version Info | Phone Information | E-Warranty Card']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_phone_number.py\", line 33, in test_set_phone_number\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "da326601e3ce3c67", "name": "stdout", "source": "da326601e3ce3c67.txt", "type": "text/plain", "size": 16159}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137162529, "stop": 1756137162780, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "9e4fd2c85e16057d", "name": "失败截图-TestEllaSetPhoneNumber", "source": "9e4fd2c85e16057d.png", "type": "image/png", "size": 166387}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137162782, "stop": 1756137164293, "duration": 1511}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phone_number"}, {"name": "subSuite", "value": "TestEllaSetPhoneNumber"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phone_number"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "846b0ebbf25a204c.json", "parameterValues": []}