{"uid": "fd73d027647e7f80", "name": "测试check my balance of sim1返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim.TestEllaCheckMyBalanceSim#test_check_my_balance_of_sim", "historyId": "8bcc4c0c2b314e79a7177168f7d787b8", "time": {"start": 1756130281917, "stop": 1756130307652, "duration": 25735}, "description": "验证check my balance of sim1指令返回预期的不支持响应", "descriptionHtml": "<p>验证check my balance of sim1指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130269305, "stop": 1756130281916, "duration": 12611}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130281916, "stop": 1756130281916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证check my balance of sim1指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "time": {"start": 1756130281917, "stop": 1756130307433, "duration": 25516}, "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "time": {"start": 1756130281917, "stop": 1756130307143, "duration": 25226}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130307143, "stop": 1756130307432, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "f1645f5668c65e6d", "name": "测试总结", "source": "f1645f5668c65e6d.txt", "type": "text/plain", "size": 318}, {"uid": "717028eb1b9cb772", "name": "test_completed", "source": "717028eb1b9cb772.png", "type": "image/png", "size": 182667}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130307433, "stop": 1756130307435, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130307435, "stop": 1756130307652, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "645a1013cee3777b", "name": "测试总结", "source": "645a1013cee3777b.txt", "type": "text/plain", "size": 318}, {"uid": "fa6e8c22abf9d153", "name": "test_completed", "source": "fa6e8c22abf9d153.png", "type": "image/png", "size": 182670}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "287ce0233c0dbca1", "name": "stdout", "source": "287ce0233c0dbca1.txt", "type": "text/plain", "size": 12579}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130307653, "stop": 1756130307653, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130307654, "stop": 1756130309078, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMyBalanceSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fd73d027647e7f80.json", "parameterValues": []}