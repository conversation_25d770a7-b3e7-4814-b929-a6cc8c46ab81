{"uid": "b664e0083c0e52a1", "name": "测试open notification ringtone settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings.TestEllaOpenSettings#test_open_notification_ringtone_settings", "historyId": "ff8706df57207971727cf6e1326d4a26", "time": {"start": 1756134696856, "stop": 1756134733453, "duration": 36597}, "description": "验证open notification ringtone settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证open notification ringtone settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open notification ringtone settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can | Notification Ringtone | Gentle | Vibration intensity']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings.TestEllaOpenSettings object at 0x00000292054A7290>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209761D90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_open_notification_ringtone_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['open notification ringtone settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can | Notification Ringtone | Gentle | Vibration intensity']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_notification_ringtone_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134683699, "stop": 1756134696853, "duration": 13154}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134696853, "stop": 1756134696853, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证open notification ringtone settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open notification ringtone settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can | Notification Ringtone | Gentle | Vibration intensity']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings.TestEllaOpenSettings object at 0x00000292054A7290>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209761D90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_open_notification_ringtone_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['open notification ringtone settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can | Notification Ringtone | Gentle | Vibration intensity']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_notification_ringtone_settings.py:33: AssertionError", "steps": [{"name": "执行命令: open notification ringtone settings", "time": {"start": 1756134696856, "stop": 1756134733448, "duration": 36592}, "status": "passed", "steps": [{"name": "执行命令: open notification ringtone settings", "time": {"start": 1756134696856, "stop": 1756134733181, "duration": 36325}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134733181, "stop": 1756134733448, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "5bfeb5a3cc6d54cf", "name": "测试总结", "source": "5bfeb5a3cc6d54cf.txt", "type": "text/plain", "size": 551}, {"uid": "7492ef624dd18e43", "name": "test_completed", "source": "7492ef624dd18e43.png", "type": "image/png", "size": 179604}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134733448, "stop": 1756134733452, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open notification ringtone settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can | Notification Ringtone | Gentle | Vibration intensity']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_open_notification_ringtone_settings.py\", line 33, in test_open_notification_ringtone_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c27c289436ed8d5c", "name": "stdout", "source": "c27c289436ed8d5c.txt", "type": "text/plain", "size": 16701}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134733461, "stop": 1756134733680, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "e846d296f5207f5e", "name": "失败截图-TestEllaOpenSettings", "source": "e846d296f5207f5e.png", "type": "image/png", "size": 179604}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134733681, "stop": 1756134735052, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_notification_ringtone_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b664e0083c0e52a1.json", "parameterValues": []}