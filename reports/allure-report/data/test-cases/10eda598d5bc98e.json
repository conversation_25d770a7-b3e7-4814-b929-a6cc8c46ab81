{"uid": "10eda598d5bc98e", "name": "测试A photo of a transparent glass cup ", "fullName": "testcases.test_ella.unsupported_commands.test_a_photo_of_a_transparent_glass_cup.TestEllaOpenPlayPoliticalNews#test_a_photo_of_a_transparent_glass_cup", "historyId": "d4409d015660212a7021f4aa7f849f30", "time": {"start": 1756129744298, "stop": 1756129774164, "duration": 29866}, "description": "测试A photo of a transparent glass cup 指令", "descriptionHtml": "<p>测试A photo of a transparent glass cup 指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129731265, "stop": 1756129744296, "duration": 13031}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129744297, "stop": 1756129744297, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试A photo of a transparent glass cup 指令", "status": "passed", "steps": [{"name": "执行命令: A photo of a transparent glass cup ", "time": {"start": 1756129744298, "stop": 1756129773760, "duration": 29462}, "status": "passed", "steps": [{"name": "执行命令: A photo of a transparent glass cup ", "time": {"start": 1756129744298, "stop": 1756129773301, "duration": 29003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129773301, "stop": 1756129773760, "duration": 459}, "status": "passed", "steps": [], "attachments": [{"uid": "65c000233254a9d6", "name": "测试总结", "source": "65c000233254a9d6.txt", "type": "text/plain", "size": 489}, {"uid": "14a91a0e3cc8b5d4", "name": "test_completed", "source": "14a91a0e3cc8b5d4.png", "type": "image/png", "size": 564323}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129773760, "stop": 1756129773763, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129773763, "stop": 1756129774164, "duration": 401}, "status": "passed", "steps": [], "attachments": [{"uid": "3deb89d6529dc2fd", "name": "测试总结", "source": "3deb89d6529dc2fd.txt", "type": "text/plain", "size": 489}, {"uid": "2922fb0dd7f36ad2", "name": "test_completed", "source": "2922fb0dd7f36ad2.png", "type": "image/png", "size": 565095}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ae5fa92f0ebe41e", "name": "stdout", "source": "ae5fa92f0ebe41e.txt", "type": "text/plain", "size": 14891}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129774165, "stop": 1756129774165, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129774165, "stop": 1756129775554, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_photo_of_a_transparent_glass_cup"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_photo_of_a_transparent_glass_cup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "10eda598d5bc98e.json", "parameterValues": []}