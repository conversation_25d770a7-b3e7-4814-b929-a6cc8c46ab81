{"uid": "2f5b63039121eceb", "name": "测试set timezone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_timezone.TestEllaSetTimezone#test_set_timezone", "historyId": "dbd7a7f96e1740fa05f50ed6fa7becfb", "time": {"start": 1756137710299, "stop": 1756137748739, "duration": 38440}, "description": "验证set timezone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set timezone指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set timezone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Date & Time | Time | Auto-Set Time | Date | August 26, 2025 | Time | 12:02 AM | Time Format | Use Locale Default | Use Locale Default | Use 24-Hour Format | 1:00 PM | Time Zone | Set Time Zone Automatically | Use network-provided time zone | Select Time Zone | GMT+08:00 China Standard Time | Dual Clock | System Dual Clock']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_timezone.TestEllaSetTimezone object at 0x000002920580E150>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A97A410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_timezone(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set timezone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Date & Time | Time | Auto-Set Time | Date | August 26, 2025 | Time | 12:02 AM | Time Format | Use Locale Default | Use Locale Default | Use 24-Hour Format | 1:00 PM | Time Zone | Set Time Zone Automatically | Use network-provided time zone | Select Time Zone | GMT+08:00 China Standard Time | Dual Clock | System Dual Clock']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_timezone.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137697244, "stop": 1756137710297, "duration": 13053}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137710297, "stop": 1756137710297, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set timezone指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set timezone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Date & Time | Time | Auto-Set Time | Date | August 26, 2025 | Time | 12:02 AM | Time Format | Use Locale Default | Use Locale Default | Use 24-Hour Format | 1:00 PM | Time Zone | Set Time Zone Automatically | Use network-provided time zone | Select Time Zone | GMT+08:00 China Standard Time | Dual Clock | System Dual Clock']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_timezone.TestEllaSetTimezone object at 0x000002920580E150>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A97A410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_timezone(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set timezone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Date & Time | Time | Auto-Set Time | Date | August 26, 2025 | Time | 12:02 AM | Time Format | Use Locale Default | Use Locale Default | Use 24-Hour Format | 1:00 PM | Time Zone | Set Time Zone Automatically | Use network-provided time zone | Select Time Zone | GMT+08:00 China Standard Time | Dual Clock | System Dual Clock']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_timezone.py:33: AssertionError", "steps": [{"name": "执行命令: set timezone", "time": {"start": 1756137710299, "stop": 1756137748735, "duration": 38436}, "status": "passed", "steps": [{"name": "执行命令: set timezone", "time": {"start": 1756137710299, "stop": 1756137748435, "duration": 38136}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137748435, "stop": 1756137748735, "duration": 300}, "status": "passed", "steps": [], "attachments": [{"uid": "6891afaab8b97894", "name": "测试总结", "source": "6891afaab8b97894.txt", "type": "text/plain", "size": 622}, {"uid": "e35e8b9b750db057", "name": "test_completed", "source": "e35e8b9b750db057.png", "type": "image/png", "size": 164513}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137748735, "stop": 1756137748738, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set timezone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Date & Time | Time | Auto-Set Time | Date | August 26, 2025 | Time | 12:02 AM | Time Format | Use Locale Default | Use Locale Default | Use 24-Hour Format | 1:00 PM | Time Zone | Set Time Zone Automatically | Use network-provided time zone | Select Time Zone | GMT+08:00 China Standard Time | Dual Clock | System Dual Clock']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_timezone.py\", line 33, in test_set_timezone\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "85ad4a8ef98e2bb1", "name": "stdout", "source": "85ad4a8ef98e2bb1.txt", "type": "text/plain", "size": 16261}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137748745, "stop": 1756137748978, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "f0905ef42a0cf6da", "name": "失败截图-TestEllaSetTimezone", "source": "f0905ef42a0cf6da.png", "type": "image/png", "size": 164555}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137748980, "stop": 1756137750455, "duration": 1475}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_timezone"}, {"name": "subSuite", "value": "TestEllaSetTimezone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_timezone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "2f5b63039121eceb.json", "parameterValues": []}