{"uid": "2e52d868d4d64fc", "name": "测试search my gallery for food pictures能正常执行", "fullName": "testcases.test_ella.dialogue.test_search_my_gallery_for_food_pictures.TestEllaHelloHello#test_search_my_gallery_for_food_pictures", "historyId": "eda6bef994b1b0ef78f60f433fb1d4f8", "time": {"start": 1756121337838, "stop": 1756121367581, "duration": 29743}, "description": "search my gallery for food pictures", "descriptionHtml": "<p>search my gallery for food pictures</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121324979, "stop": 1756121337837, "duration": 12858}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121337837, "stop": 1756121337837, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "search my gallery for food pictures", "status": "passed", "steps": [{"name": "执行命令: search my gallery for food pictures", "time": {"start": 1756121337838, "stop": 1756121367350, "duration": 29512}, "status": "passed", "steps": [{"name": "执行命令: search my gallery for food pictures", "time": {"start": 1756121337838, "stop": 1756121367092, "duration": 29254}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121367092, "stop": 1756121367349, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "6d63cdee09656977", "name": "测试总结", "source": "6d63cdee09656977.txt", "type": "text/plain", "size": 386}, {"uid": "8e4ece707727609e", "name": "test_completed", "source": "8e4ece707727609e.png", "type": "image/png", "size": 176258}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121367350, "stop": 1756121367353, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121367353, "stop": 1756121367581, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "ae46c4798b8287f9", "name": "测试总结", "source": "ae46c4798b8287f9.txt", "type": "text/plain", "size": 386}, {"uid": "691a1adcc36f7605", "name": "test_completed", "source": "691a1adcc36f7605.png", "type": "image/png", "size": 176054}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "516a0b51098d9367", "name": "stdout", "source": "516a0b51098d9367.txt", "type": "text/plain", "size": 13342}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121367582, "stop": 1756121367582, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121367582, "stop": 1756121369011, "duration": 1429}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_search_my_gallery_for_food_pictures"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_search_my_gallery_for_food_pictures"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2e52d868d4d64fc.json", "parameterValues": []}