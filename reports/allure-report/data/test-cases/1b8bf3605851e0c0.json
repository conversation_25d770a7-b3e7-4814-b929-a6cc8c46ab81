{"uid": "1b8bf3605851e0c0", "name": "测试there is a colorful butterfly beside it", "fullName": "testcases.test_ella.unsupported_commands.test_there_is_a_colorful_butterfly_beside_it.TestEllaOpenPlayPoliticalNews#test_there_is_a_colorful_butterfly_beside_it", "historyId": "cc158f7c05891fbac271a86692bc57a6", "time": {"start": 1756138442115, "stop": 1756138469232, "duration": 27117}, "description": "测试there is a colorful butterfly beside it指令", "descriptionHtml": "<p>测试there is a colorful butterfly beside it指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138429414, "stop": 1756138442114, "duration": 12700}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138442114, "stop": 1756138442114, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试there is a colorful butterfly beside it指令", "status": "passed", "steps": [{"name": "执行命令: there is a colorful butterfly beside it", "time": {"start": 1756138442115, "stop": 1756138468998, "duration": 26883}, "status": "passed", "steps": [{"name": "执行命令: there is a colorful butterfly beside it", "time": {"start": 1756138442115, "stop": 1756138468732, "duration": 26617}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138468732, "stop": 1756138468997, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "ff1fb4160e65afc2", "name": "测试总结", "source": "ff1fb4160e65afc2.txt", "type": "text/plain", "size": 986}, {"uid": "19fbc947465f78f7", "name": "test_completed", "source": "19fbc947465f78f7.png", "type": "image/png", "size": 200574}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138468998, "stop": 1756138469002, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138469002, "stop": 1756138469232, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "62ef868df130827e", "name": "测试总结", "source": "62ef868df130827e.txt", "type": "text/plain", "size": 986}, {"uid": "5b0ae94c3fb77a72", "name": "test_completed", "source": "5b0ae94c3fb77a72.png", "type": "image/png", "size": 200574}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7d82f2fb58c18703", "name": "stdout", "source": "7d82f2fb58c18703.txt", "type": "text/plain", "size": 15989}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138469233, "stop": 1756138469233, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138469234, "stop": 1756138470630, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_is_a_colorful_butterfly_beside_it"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_is_a_colorful_butterfly_beside_it"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1b8bf3605851e0c0.json", "parameterValues": []}