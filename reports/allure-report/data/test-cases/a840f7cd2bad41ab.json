{"uid": "a840f7cd2bad41ab", "name": "测试disable accelerate dialogue返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue.TestEllaDisableAccelerateDialogue#test_disable_accelerate_dialogue", "historyId": "4cfe8e55b2a91a62bbf1141ffc0cc530", "time": {"start": 1756130726527, "stop": 1756130752126, "duration": 25599}, "description": "验证disable accelerate dialogue指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable accelerate dialogue指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable accelerate dialogue', \"Sorry, I couldn't locate the setting option(s) for speech output.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue.TestEllaDisableAccelerateDialogue object at 0x000002920503BC10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292081C7590>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_disable_accelerate_dialogue(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['disable accelerate dialogue', \"Sorry, I couldn't locate the setting option(s) for speech output.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_disable_accelerate_dialogue.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130713749, "stop": 1756130726526, "duration": 12777}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130726526, "stop": 1756130726526, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable accelerate dialogue指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable accelerate dialogue', \"Sorry, I couldn't locate the setting option(s) for speech output.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue.TestEllaDisableAccelerateDialogue object at 0x000002920503BC10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292081C7590>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_disable_accelerate_dialogue(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['disable accelerate dialogue', \"Sorry, I couldn't locate the setting option(s) for speech output.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_disable_accelerate_dialogue.py:34: AssertionError", "steps": [{"name": "执行命令: disable accelerate dialogue", "time": {"start": 1756130726527, "stop": 1756130752122, "duration": 25595}, "status": "passed", "steps": [{"name": "执行命令: disable accelerate dialogue", "time": {"start": 1756130726527, "stop": 1756130751871, "duration": 25344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130751871, "stop": 1756130752121, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "3b36822373e26eb2", "name": "测试总结", "source": "3b36822373e26eb2.txt", "type": "text/plain", "size": 350}, {"uid": "9eb9531bc0bdf4d9", "name": "test_completed", "source": "9eb9531bc0bdf4d9.png", "type": "image/png", "size": 194673}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130752122, "stop": 1756130752125, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable accelerate dialogue', \"Sorry, I couldn't locate the setting option(s) for speech output.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_disable_accelerate_dialogue.py\", line 34, in test_disable_accelerate_dialogue\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2c2f7346614344ae", "name": "stdout", "source": "2c2f7346614344ae.txt", "type": "text/plain", "size": 13522}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130752145, "stop": 1756130752360, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "c95dabce76722559", "name": "失败截图-TestEllaDisableAccelerateDialogue", "source": "c95dabce76722559.png", "type": "image/png", "size": 194673}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756130752361, "stop": 1756130753740, "duration": 1379}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaDisableAccelerateDialogue"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a840f7cd2bad41ab.json", "parameterValues": []}