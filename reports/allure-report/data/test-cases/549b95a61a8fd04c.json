{"uid": "549b95a61a8fd04c", "name": "测试what time is it now能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_time_is_it_now.TestEllaWhatTimeIsItNow#test_what_time_is_it_now", "historyId": "519d11d818a361bc75d5af94c6a68b28", "time": {"start": 1756122507445, "stop": 1756122535501, "duration": 28056}, "description": "what time is it now", "descriptionHtml": "<p>what time is it now</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122493890, "stop": 1756122507443, "duration": 13553}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122507444, "stop": 1756122507444, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what time is it now", "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1756122507446, "stop": 1756122535242, "duration": 27796}, "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1756122507446, "stop": 1756122534975, "duration": 27529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122534975, "stop": 1756122535241, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "956124e41dcabce6", "name": "测试总结", "source": "956124e41dcabce6.txt", "type": "text/plain", "size": 284}, {"uid": "52ade9697ff94474", "name": "test_completed", "source": "52ade9697ff94474.png", "type": "image/png", "size": 168124}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122535242, "stop": 1756122535245, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122535245, "stop": 1756122535500, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "c5a538092da5383b", "name": "测试总结", "source": "c5a538092da5383b.txt", "type": "text/plain", "size": 284}, {"uid": "5cfa9eecc3370375", "name": "test_completed", "source": "5cfa9eecc3370375.png", "type": "image/png", "size": 167983}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "13719990d8b739d1", "name": "stdout", "source": "13719990d8b739d1.txt", "type": "text/plain", "size": 12490}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122535503, "stop": 1756122535503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122535504, "stop": 1756122536875, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_time_is_it_now"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItNow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_time_is_it_now"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "549b95a61a8fd04c.json", "parameterValues": []}