{"uid": "8174edea3b7a8574", "name": "测试hello hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello#test_hello_hello", "historyId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "time": {"start": 1756119809998, "stop": 1756119837521, "duration": 27523}, "description": "hello hello", "descriptionHtml": "<p>hello hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119797078, "stop": 1756119809997, "duration": 12919}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119809997, "stop": 1756119809997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1756119809998, "stop": 1756119837279, "duration": 27281}, "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1756119809998, "stop": 1756119836995, "duration": 26997}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119836995, "stop": 1756119837279, "duration": 284}, "status": "passed", "steps": [], "attachments": [{"uid": "9e1354bca103d04b", "name": "测试总结", "source": "9e1354bca103d04b.txt", "type": "text/plain", "size": 729}, {"uid": "665dc68227146da", "name": "test_completed", "source": "665dc68227146da.png", "type": "image/png", "size": 179430}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119837279, "stop": 1756119837281, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119837281, "stop": 1756119837520, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "38f719a70ed0ea6e", "name": "测试总结", "source": "38f719a70ed0ea6e.txt", "type": "text/plain", "size": 729}, {"uid": "16233172d7a98766", "name": "test_completed", "source": "16233172d7a98766.png", "type": "image/png", "size": 179331}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "725659df8bb68675", "name": "stdout", "source": "725659df8bb68675.txt", "type": "text/plain", "size": 15220}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119837523, "stop": 1756119837523, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119837525, "stop": 1756119838888, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hello_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8174edea3b7a8574.json", "parameterValues": []}