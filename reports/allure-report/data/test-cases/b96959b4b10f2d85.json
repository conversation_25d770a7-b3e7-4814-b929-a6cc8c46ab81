{"uid": "b96959b4b10f2d85", "name": "测试set my fonts返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts#test_set_my_fonts", "historyId": "7d3b4e67344145885187c529ee88a9aa", "time": {"start": 1756136805060, "stop": 1756136838848, "duration": 33788}, "description": "验证set my fonts指令返回预期的不支持响应", "descriptionHtml": "<p>验证set my fonts指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my fonts', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts object at 0x000002920571D310>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A91C810>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_my_fonts(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my fonts', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_my_fonts.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136792068, "stop": 1756136805058, "duration": 12990}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136805058, "stop": 1756136805058, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set my fonts指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my fonts', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts object at 0x000002920571D310>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A91C810>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_my_fonts(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my fonts', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_my_fonts.py:33: AssertionError", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1756136805060, "stop": 1756136838841, "duration": 33781}, "status": "passed", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1756136805060, "stop": 1756136838573, "duration": 33513}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136838573, "stop": 1756136838840, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "a5ea490e84550ecc", "name": "测试总结", "source": "a5ea490e84550ecc.txt", "type": "text/plain", "size": 549}, {"uid": "40a6deee0a123feb", "name": "test_completed", "source": "40a6deee0a123feb.png", "type": "image/png", "size": 170370}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136838841, "stop": 1756136838847, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my fonts', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_my_fonts.py\", line 33, in test_set_my_fonts\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "374b5f5623ecf76", "name": "stdout", "source": "374b5f5623ecf76.txt", "type": "text/plain", "size": 16033}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136838855, "stop": 1756136839094, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "7667a96177d94a2e", "name": "失败截图-TestEllaSetMyFonts", "source": "7667a96177d94a2e.png", "type": "image/png", "size": 170326}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136839095, "stop": 1756136840551, "duration": 1456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_fonts"}, {"name": "subSuite", "value": "TestEllaSetMyFonts"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_fonts"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b96959b4b10f2d85.json", "parameterValues": []}