{"uid": "180a974deb749413", "name": "测试stop playing", "fullName": "testcases.test_ella.component_coupling.test_stop_playing.TestEllaOpenYoutube#test_stop_playing", "historyId": "329fa4b06eb0b0d769c2c418ed03dab7", "time": {"start": 1756118775920, "stop": 1756118803224, "duration": 27304}, "description": "测试stop playing指令", "descriptionHtml": "<p>测试stop playing指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118762980, "stop": 1756118775919, "duration": 12939}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118775919, "stop": 1756118775919, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试stop playing指令", "status": "passed", "steps": [{"name": "执行命令: stop playing", "time": {"start": 1756118775920, "stop": 1756118802951, "duration": 27031}, "status": "passed", "steps": [{"name": "执行命令: stop playing", "time": {"start": 1756118775920, "stop": 1756118802677, "duration": 26757}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118802677, "stop": 1756118802951, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "f441d21a3898f4cf", "name": "测试总结", "source": "f441d21a3898f4cf.txt", "type": "text/plain", "size": 292}, {"uid": "edb2d90cbf7fbe87", "name": "test_completed", "source": "edb2d90cbf7fbe87.png", "type": "image/png", "size": 177757}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118802951, "stop": 1756118802955, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118802955, "stop": 1756118803224, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "a5e7b37f602d85e9", "name": "测试总结", "source": "a5e7b37f602d85e9.txt", "type": "text/plain", "size": 292}, {"uid": "4f7ca4110bd579bc", "name": "test_completed", "source": "4f7ca4110bd579bc.png", "type": "image/png", "size": 177778}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "32bdce2a885c4c2a", "name": "stdout", "source": "32bdce2a885c4c2a.txt", "type": "text/plain", "size": 12983}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118803225, "stop": 1756118803225, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118803226, "stop": 1756118804594, "duration": 1368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_stop_playing"}, {"name": "subSuite", "value": "TestEllaOpenYoutube"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_stop_playing"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "180a974deb749413.json", "parameterValues": []}