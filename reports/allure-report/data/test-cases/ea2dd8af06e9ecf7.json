{"uid": "ea2dd8af06e9ecf7", "name": "测试stop workout能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_workout.TestEllaStopWorkout#test_stop_workout", "historyId": "95e68fb1d20b8d7ff190c67b0bbc2ee8", "time": {"start": 1756121782700, "stop": 1756121808706, "duration": 26006}, "description": "stop workout", "descriptionHtml": "<p>stop workout</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121769192, "stop": 1756121782698, "duration": 13506}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121782698, "stop": 1756121782698, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "stop workout", "status": "passed", "steps": [{"name": "执行命令: stop workout", "time": {"start": 1756121782701, "stop": 1756121808460, "duration": 25759}, "status": "passed", "steps": [{"name": "执行命令: stop workout", "time": {"start": 1756121782701, "stop": 1756121808180, "duration": 25479}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121808180, "stop": 1756121808458, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "4af8c6ce3f307851", "name": "测试总结", "source": "4af8c6ce3f307851.txt", "type": "text/plain", "size": 290}, {"uid": "3664c1dfa52517ff", "name": "test_completed", "source": "3664c1dfa52517ff.png", "type": "image/png", "size": 177550}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121808460, "stop": 1756121808465, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121808465, "stop": 1756121808705, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "a8b00939c0390721", "name": "测试总结", "source": "a8b00939c0390721.txt", "type": "text/plain", "size": 290}, {"uid": "9852e5f1e1b1bcfd", "name": "test_completed", "source": "9852e5f1e1b1bcfd.png", "type": "image/png", "size": 177550}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "99b69e4ff4c32323", "name": "stdout", "source": "99b69e4ff4c32323.txt", "type": "text/plain", "size": 12444}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121808708, "stop": 1756121808708, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121808711, "stop": 1756121810109, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_workout"}, {"name": "subSuite", "value": "TestEllaStopWorkout"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_workout"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ea2dd8af06e9ecf7.json", "parameterValues": []}