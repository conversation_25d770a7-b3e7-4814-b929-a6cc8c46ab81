{"uid": "2f84eb53ee7d4a79", "name": "测试switch to equilibrium mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode.TestEllaSwitchToEquilibriumMode#test_switch_to_equilibrium_mode", "historyId": "45b57073b776ed5666f2f20a47a4638f", "time": {"start": 1756126717879, "stop": 1756126752262, "duration": 34383}, "description": "switch to equilibrium mode", "descriptionHtml": "<p>switch to equilibrium mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126705301, "stop": 1756126717876, "duration": 12575}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126717877, "stop": 1756126717877, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch to equilibrium mode", "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1756126717880, "stop": 1756126752027, "duration": 34147}, "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1756126717880, "stop": 1756126751725, "duration": 33845}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126751725, "stop": 1756126752026, "duration": 301}, "status": "passed", "steps": [], "attachments": [{"uid": "4ca0355880d248e1", "name": "测试总结", "source": "4ca0355880d248e1.txt", "type": "text/plain", "size": 668}, {"uid": "6979e364a17ea396", "name": "test_completed", "source": "6979e364a17ea396.png", "type": "image/png", "size": 172083}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126752027, "stop": 1756126752028, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126752028, "stop": 1756126752261, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "bdb5be1444c4548d", "name": "测试总结", "source": "bdb5be1444c4548d.txt", "type": "text/plain", "size": 668}, {"uid": "4c63165d669465c8", "name": "test_completed", "source": "4c63165d669465c8.png", "type": "image/png", "size": 171531}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6d31bb536b8b1bb8", "name": "stdout", "source": "6d31bb536b8b1bb8.txt", "type": "text/plain", "size": 15477}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126752262, "stop": 1756126752262, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126752265, "stop": 1756126753649, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToEquilibriumMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2f84eb53ee7d4a79.json", "parameterValues": []}