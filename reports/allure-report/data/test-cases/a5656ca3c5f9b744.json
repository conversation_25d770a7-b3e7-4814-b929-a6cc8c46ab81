{"uid": "a5656ca3c5f9b744", "name": "测试Add the images and text on the screen to the note", "fullName": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews#test_Add_the_images_and_text_on_the_screen_to_the_note", "historyId": "5cc849d46714fff99c626e94dc28932d", "time": {"start": 1756129341837, "stop": 1756129374943, "duration": 33106}, "description": "测试Add the images and text on the screen to the note指令", "descriptionHtml": "<p>测试Add the images and text on the screen to the note指令</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Add the images and text on the screen to the note', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Digital Assistant App | Default digital assistant app | Ella | Use text from screen | Allow the assist app to access the screen contents as text | Use screenshot | Allow the assist app to access an image of the screen | Flash screen | Flash edges of screen when assist app accesses text from screen or screenshot | Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews object at 0x0000029204EC6B90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208552410>\n\n    @allure.title(\"测试Add the images and text on the screen to the note\")\n    @allure.description(\"测试Add the images and text on the screen to the note指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_Add_the_images_and_text_on_the_screen_to_the_note(self, ella_app):\n        \"\"\"测试Add the images and text on the screen to the note命令\"\"\"\n        command = \"Add the images and text on the screen to the note\"\n        expected_text = ['Sorry', 'Oops', 'out of my reach']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Add the images and text on the screen to the note', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Digital Assistant App | Default digital assistant app | Ella | Use text from screen | Allow the assist app to access the screen contents as text | Use screenshot | Allow the assist app to access an image of the screen | Flash screen | Flash edges of screen when assist app accesses text from screen or screenshot | Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py:31: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129329041, "stop": 1756129341835, "duration": 12794}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129341835, "stop": 1756129341835, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Add the images and text on the screen to the note指令", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Add the images and text on the screen to the note', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Digital Assistant App | Default digital assistant app | Ella | Use text from screen | Allow the assist app to access the screen contents as text | Use screenshot | Allow the assist app to access an image of the screen | Flash screen | Flash edges of screen when assist app accesses text from screen or screenshot | Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews object at 0x0000029204EC6B90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208552410>\n\n    @allure.title(\"测试Add the images and text on the screen to the note\")\n    @allure.description(\"测试Add the images and text on the screen to the note指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_Add_the_images_and_text_on_the_screen_to_the_note(self, ella_app):\n        \"\"\"测试Add the images and text on the screen to the note命令\"\"\"\n        command = \"Add the images and text on the screen to the note\"\n        expected_text = ['Sorry', 'Oops', 'out of my reach']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Add the images and text on the screen to the note', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Digital Assistant App | Default digital assistant app | Ella | Use text from screen | Allow the assist app to access the screen contents as text | Use screenshot | Allow the assist app to access an image of the screen | Flash screen | Flash edges of screen when assist app accesses text from screen or screenshot | Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py:31: AssertionError", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "time": {"start": 1756129341838, "stop": 1756129374933, "duration": 33095}, "status": "passed", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "time": {"start": 1756129341838, "stop": 1756129374635, "duration": 32797}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129374635, "stop": 1756129374932, "duration": 297}, "status": "passed", "steps": [], "attachments": [{"uid": "d73d1ad63a5399c9", "name": "测试总结", "source": "d73d1ad63a5399c9.txt", "type": "text/plain", "size": 859}, {"uid": "9a0844bc4a45c048", "name": "test_completed", "source": "9a0844bc4a45c048.png", "type": "image/png", "size": 177747}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129374933, "stop": 1756129374939, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Add the images and text on the screen to the note', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Digital Assistant App | Default digital assistant app | Ella | Use text from screen | Allow the assist app to access the screen contents as text | Use screenshot | Allow the assist app to access an image of the screen | Flash screen | Flash edges of screen when assist app accesses text from screen or screenshot | Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py\", line 31, in test_Add_the_images_and_text_on_the_screen_to_the_note\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a911f0e363fd2273", "name": "stdout", "source": "a911f0e363fd2273.txt", "type": "text/plain", "size": 16941}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129374953, "stop": 1756129375233, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "b8f3b341ca5b0d0", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "b8f3b341ca5b0d0.png", "type": "image/png", "size": 177573}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756129375235, "stop": 1756129376645, "duration": 1410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a5656ca3c5f9b744.json", "parameterValues": []}