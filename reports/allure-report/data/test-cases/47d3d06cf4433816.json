{"uid": "47d3d06cf4433816", "name": "测试last channel能正常执行", "fullName": "testcases.test_ella.dialogue.test_last_channel.TestEllaHowIsWeatherToday#test_last_channel", "historyId": "a0ea006ce61aacded2720f8d2a03ba5b", "time": {"start": 1756120420698, "stop": 1756120446484, "duration": 25786}, "description": "last channel", "descriptionHtml": "<p>last channel</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120407748, "stop": 1756120420696, "duration": 12948}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120420697, "stop": 1756120420697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "last channel", "status": "passed", "steps": [{"name": "执行命令: last channel", "time": {"start": 1756120420698, "stop": 1756120446270, "duration": 25572}, "status": "passed", "steps": [{"name": "执行命令: last channel", "time": {"start": 1756120420698, "stop": 1756120446008, "duration": 25310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120446008, "stop": 1756120446269, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "86de1d3e67dcd517", "name": "测试总结", "source": "86de1d3e67dcd517.txt", "type": "text/plain", "size": 313}, {"uid": "5fa1108b91218adf", "name": "test_completed", "source": "5fa1108b91218adf.png", "type": "image/png", "size": 179450}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120446270, "stop": 1756120446273, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120446273, "stop": 1756120446483, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "822101ce5c352da7", "name": "测试总结", "source": "822101ce5c352da7.txt", "type": "text/plain", "size": 313}, {"uid": "fbeee7335a557098", "name": "test_completed", "source": "fbeee7335a557098.png", "type": "image/png", "size": 179450}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bf3a5b90c8d7318d", "name": "stdout", "source": "bf3a5b90c8d7318d.txt", "type": "text/plain", "size": 12548}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120446484, "stop": 1756120446484, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120446485, "stop": 1756120447832, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_last_channel"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_last_channel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "47d3d06cf4433816.json", "parameterValues": []}