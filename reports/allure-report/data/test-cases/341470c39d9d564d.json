{"uid": "341470c39d9d564d", "name": "测试open whatsapp", "fullName": "testcases.test_ella.third_coupling.test_open_whatsapp.TestEllaOpenWhatsapp#test_open_whatsapp", "historyId": "fcabf101e08450542157f8740eeec9a7", "time": {"start": 1756129136862, "stop": 1756129163936, "duration": 27074}, "description": "测试open whatsapp指令", "descriptionHtml": "<p>测试open whatsapp指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129123669, "stop": 1756129136860, "duration": 13191}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129136860, "stop": 1756129136860, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open whatsapp指令", "status": "passed", "steps": [{"name": "执行命令: open whatsapp", "time": {"start": 1756129136862, "stop": 1756129163684, "duration": 26822}, "status": "passed", "steps": [{"name": "执行命令: open whatsapp", "time": {"start": 1756129136862, "stop": 1756129163428, "duration": 26566}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129163428, "stop": 1756129163684, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "302c669ef65bcb10", "name": "测试总结", "source": "302c669ef65bcb10.txt", "type": "text/plain", "size": 309}, {"uid": "cda7bc9d604f5815", "name": "test_completed", "source": "cda7bc9d604f5815.png", "type": "image/png", "size": 187260}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129163684, "stop": 1756129163687, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129163687, "stop": 1756129163935, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "bd2ade9c3e210582", "name": "测试总结", "source": "bd2ade9c3e210582.txt", "type": "text/plain", "size": 309}, {"uid": "f77034986557c2d4", "name": "test_completed", "source": "f77034986557c2d4.png", "type": "image/png", "size": 187269}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bf4fd846077fe588", "name": "stdout", "source": "bf4fd846077fe588.txt", "type": "text/plain", "size": 13199}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129163936, "stop": 1756129163936, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129163938, "stop": 1756129165334, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_open_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_open_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "341470c39d9d564d.json", "parameterValues": []}