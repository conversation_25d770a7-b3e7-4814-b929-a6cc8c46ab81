{"uid": "8e7f06be4bdf7360", "name": "测试set phantom v pen返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen.TestEllaSetPhantomVPen#test_set_phantom_v_pen", "historyId": "ca9dd7f70b2888aafceb94247d7986f0", "time": {"start": 1756137083456, "stop": 1756137109277, "duration": 25821}, "description": "验证set phantom v pen指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phantom v pen指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137070209, "stop": 1756137083456, "duration": 13247}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137083456, "stop": 1756137083456, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set phantom v pen指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set phantom v pen", "time": {"start": 1756137083457, "stop": 1756137109028, "duration": 25571}, "status": "passed", "steps": [{"name": "执行命令: set phantom v pen", "time": {"start": 1756137083457, "stop": 1756137108742, "duration": 25285}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137108742, "stop": 1756137109027, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "4815e9ed644c1420", "name": "测试总结", "source": "4815e9ed644c1420.txt", "type": "text/plain", "size": 330}, {"uid": "b0d1460127cdb11a", "name": "test_completed", "source": "b0d1460127cdb11a.png", "type": "image/png", "size": 174999}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137109028, "stop": 1756137109029, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137109029, "stop": 1756137109277, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "f95b9332b1efad5c", "name": "测试总结", "source": "f95b9332b1efad5c.txt", "type": "text/plain", "size": 330}, {"uid": "fb168fd8335bd4fc", "name": "test_completed", "source": "fb168fd8335bd4fc.png", "type": "image/png", "size": 174999}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e36a9ca06f492e62", "name": "stdout", "source": "e36a9ca06f492e62.txt", "type": "text/plain", "size": 12555}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137109279, "stop": 1756137109279, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756137109280, "stop": 1756137110730, "duration": 1450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phantom_v_pen"}, {"name": "subSuite", "value": "TestEllaSetPhantomVPen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8e7f06be4bdf7360.json", "parameterValues": []}