{"uid": "1cf1529923ed0ba3", "name": "测试navigation to the lucky能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "historyId": "75c56947a7b1061f7ec858fb20919b50", "time": {"start": 1756129042055, "stop": 1756129074072, "duration": 32017}, "description": "navigation to the lucky", "descriptionHtml": "<p>navigation to the lucky</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129029122, "stop": 1756129042053, "duration": 12931}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129042053, "stop": 1756129042053, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1756129042055, "stop": 1756129073840, "duration": 31785}, "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1756129042056, "stop": 1756129073584, "duration": 31528}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129073584, "stop": 1756129073839, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "95579dc4a0d48be2", "name": "测试总结", "source": "95579dc4a0d48be2.txt", "type": "text/plain", "size": 756}, {"uid": "d615bb3d77cbb00a", "name": "test_completed", "source": "d615bb3d77cbb00a.png", "type": "image/png", "size": 190442}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756129073840, "stop": 1756129073843, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129073843, "stop": 1756129074071, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "2c95ac23b708b35c", "name": "测试总结", "source": "2c95ac23b708b35c.txt", "type": "text/plain", "size": 756}, {"uid": "327e05f8528bd51b", "name": "test_completed", "source": "327e05f8528bd51b.png", "type": "image/png", "size": 190442}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b7acb82584f36ae9", "name": "stdout", "source": "b7acb82584f36ae9.txt", "type": "text/plain", "size": 15338}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129074073, "stop": 1756129074073, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129074073, "stop": 1756129075509, "duration": 1436}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1cf1529923ed0ba3.json", "parameterValues": []}