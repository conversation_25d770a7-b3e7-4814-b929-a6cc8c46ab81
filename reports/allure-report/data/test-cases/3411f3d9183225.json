{"uid": "3411f3d9183225", "name": "测试summarize content on this page能正常执行", "fullName": "testcases.test_ella.dialogue.test_summarize_content_on_this_page.TestEllaSummarizeContentThisPage#test_summarize_content_on_this_page", "historyId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "time": {"start": 1756121823355, "stop": 1756121849620, "duration": 26265}, "description": "summarize content on this page", "descriptionHtml": "<p>summarize content on this page</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121810120, "stop": 1756121823353, "duration": 13233}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121823353, "stop": 1756121823353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1756121823356, "stop": 1756121849336, "duration": 25980}, "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1756121823356, "stop": 1756121849057, "duration": 25701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121849057, "stop": 1756121849333, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "d9edee4351d2549a", "name": "测试总结", "source": "d9edee4351d2549a.txt", "type": "text/plain", "size": 386}, {"uid": "f379b77020879315", "name": "test_completed", "source": "f379b77020879315.png", "type": "image/png", "size": 195171}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121849336, "stop": 1756121849343, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121849343, "stop": 1756121849619, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "71b9e24eb3a72755", "name": "测试总结", "source": "71b9e24eb3a72755.txt", "type": "text/plain", "size": 386}, {"uid": "3aaeece8b95a3768", "name": "test_completed", "source": "3aaeece8b95a3768.png", "type": "image/png", "size": 195171}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8e1b4ec22d7dc71d", "name": "stdout", "source": "8e1b4ec22d7dc71d.txt", "type": "text/plain", "size": 12824}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121849621, "stop": 1756121849622, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121849624, "stop": 1756121851019, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaSummarizeContentThisPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_content_on_this_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3411f3d9183225.json", "parameterValues": []}