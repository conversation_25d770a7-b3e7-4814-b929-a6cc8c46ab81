{"uid": "ab1ca4ebb36cec89", "name": "测试set cover screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps.TestEllaSetCoverScreenApps#test_set_cover_screen_apps", "historyId": "154a720f41d8f5a908552e8c7cf8e781", "time": {"start": 1756136269499, "stop": 1756136295533, "duration": 26034}, "description": "验证set cover screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set cover screen apps指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136256372, "stop": 1756136269498, "duration": 13126}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136269498, "stop": 1756136269498, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set cover screen apps指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1756136269499, "stop": 1756136295325, "duration": 25826}, "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1756136269499, "stop": 1756136295074, "duration": 25575}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136295074, "stop": 1756136295324, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "cfbaaa62589010ac", "name": "测试总结", "source": "cfbaaa62589010ac.txt", "type": "text/plain", "size": 342}, {"uid": "30436e58cb6f329d", "name": "test_completed", "source": "30436e58cb6f329d.png", "type": "image/png", "size": 182790}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136295325, "stop": 1756136295326, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136295326, "stop": 1756136295533, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "225930d5e9b5e5e1", "name": "测试总结", "source": "225930d5e9b5e5e1.txt", "type": "text/plain", "size": 342}, {"uid": "13e40be4a78ca994", "name": "test_completed", "source": "13e40be4a78ca994.png", "type": "image/png", "size": 182790}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1407605c7a899a4a", "name": "stdout", "source": "1407605c7a899a4a.txt", "type": "text/plain", "size": 12611}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136295533, "stop": 1756136295533, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136295536, "stop": 1756136297001, "duration": 1465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_cover_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetCoverScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ab1ca4ebb36cec89.json", "parameterValues": []}