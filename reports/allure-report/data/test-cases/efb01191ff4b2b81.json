{"uid": "efb01191ff4b2b81", "name": "测试running on the grass", "fullName": "testcases.test_ella.unsupported_commands.test_running_on_the_grass.TestEllaOpenPlayPoliticalNews#test_running_on_the_grass", "historyId": "5eaa5a3015ce02f75c4c021fdbd2f78d", "time": {"start": 1756135732883, "stop": 1756135762568, "duration": 29685}, "description": "测试running on the grass指令", "descriptionHtml": "<p>测试running on the grass指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135720123, "stop": 1756135732882, "duration": 12759}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135732883, "stop": 1756135732883, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试running on the grass指令", "status": "passed", "steps": [{"name": "执行命令: running on the grass", "time": {"start": 1756135732884, "stop": 1756135762311, "duration": 29427}, "status": "passed", "steps": [{"name": "执行命令: running on the grass", "time": {"start": 1756135732884, "stop": 1756135762061, "duration": 29177}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135762061, "stop": 1756135762311, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "a403199cbb931282", "name": "测试总结", "source": "a403199cbb931282.txt", "type": "text/plain", "size": 1025}, {"uid": "de9e2afc119d061b", "name": "test_completed", "source": "de9e2afc119d061b.png", "type": "image/png", "size": 210241}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135762311, "stop": 1756135762315, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135762315, "stop": 1756135762567, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "926233d1c6e81fa6", "name": "测试总结", "source": "926233d1c6e81fa6.txt", "type": "text/plain", "size": 1025}, {"uid": "dde01eacaa3f3b0a", "name": "test_completed", "source": "dde01eacaa3f3b0a.png", "type": "image/png", "size": 210308}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2ad9e6a2d48da00d", "name": "stdout", "source": "2ad9e6a2d48da00d.txt", "type": "text/plain", "size": 16427}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135762569, "stop": 1756135762569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135762570, "stop": 1756135764026, "duration": 1456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_running_on_the_grass"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_running_on_the_grass"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "efb01191ff4b2b81.json", "parameterValues": []}