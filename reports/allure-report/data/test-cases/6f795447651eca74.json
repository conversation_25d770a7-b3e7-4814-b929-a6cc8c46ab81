{"uid": "6f795447651eca74", "name": "测试turn on location services能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_location_services.TestEllaTurnLocationServices#test_turn_on_location_services", "historyId": "8a87a1d0f534afc4770901f3d1dfa316", "time": {"start": 1756128028199, "stop": 1756128054976, "duration": 26777}, "description": "turn on location services", "descriptionHtml": "<p>turn on location services</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128013366, "stop": 1756128028197, "duration": 14831}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128028197, "stop": 1756128028197, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on location services", "status": "passed", "steps": [{"name": "执行命令: turn on location services", "time": {"start": 1756128028199, "stop": 1756128054720, "duration": 26521}, "status": "passed", "steps": [{"name": "执行命令: turn on location services", "time": {"start": 1756128028200, "stop": 1756128054443, "duration": 26243}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128054443, "stop": 1756128054720, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "dcbc64ec37b97eed", "name": "测试总结", "source": "dcbc64ec37b97eed.txt", "type": "text/plain", "size": 234}, {"uid": "1d9b3792973bc25f", "name": "test_completed", "source": "1d9b3792973bc25f.png", "type": "image/png", "size": 157476}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128054720, "stop": 1756128054723, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128054723, "stop": 1756128054723, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128054723, "stop": 1756128054973, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "41586ee65653599b", "name": "测试总结", "source": "41586ee65653599b.txt", "type": "text/plain", "size": 234}, {"uid": "ca59cd6d5e4aceb8", "name": "test_completed", "source": "ca59cd6d5e4aceb8.png", "type": "image/png", "size": 157476}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d3f6f4322219f913", "name": "stdout", "source": "d3f6f4322219f913.txt", "type": "text/plain", "size": 13173}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128054978, "stop": 1756128054978, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128054981, "stop": 1756128056404, "duration": 1423}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_location_services"}, {"name": "subSuite", "value": "TestEllaTurnLocationServices"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_location_services"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6f795447651eca74.json", "parameterValues": []}