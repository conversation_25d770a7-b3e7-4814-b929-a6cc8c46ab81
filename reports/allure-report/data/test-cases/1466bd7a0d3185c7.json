{"uid": "1466bd7a0d3185c7", "name": "测试where is the carlcare service outlet能正常执行", "fullName": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet.TestEllaWhereIsCarlcareServiceOutlet#test_where_is_the_carlcare_service_outlet", "historyId": "4f84a6588e41dde581e4eef4fccd6344", "time": {"start": 1756128671418, "stop": 1756128702812, "duration": 31394}, "description": "where is the carlcare service outlet", "descriptionHtml": "<p>where is the carlcare service outlet</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128658834, "stop": 1756128671417, "duration": 12583}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128671417, "stop": 1756128671417, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "where is the carlcare service outlet", "status": "passed", "steps": [{"name": "执行命令: where is the carlcare service outlet", "time": {"start": 1756128671418, "stop": 1756128702583, "duration": 31165}, "status": "passed", "steps": [{"name": "执行命令: where is the carlcare service outlet", "time": {"start": 1756128671418, "stop": 1756128702284, "duration": 30866}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128702284, "stop": 1756128702582, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "be5d5ef6cc9946d2", "name": "测试总结", "source": "be5d5ef6cc9946d2.txt", "type": "text/plain", "size": 413}, {"uid": "dc29c7252a34f2d1", "name": "test_completed", "source": "dc29c7252a34f2d1.png", "type": "image/png", "size": 173568}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128702583, "stop": 1756128702585, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128702585, "stop": 1756128702585, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128702585, "stop": 1756128702811, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "aac9842520433783", "name": "测试总结", "source": "aac9842520433783.txt", "type": "text/plain", "size": 413}, {"uid": "4e759ddea17dff42", "name": "test_completed", "source": "4e759ddea17dff42.png", "type": "image/png", "size": 174571}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fe4321404fbdc4a7", "name": "stdout", "source": "fe4321404fbdc4a7.txt", "type": "text/plain", "size": 15618}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128702813, "stop": 1756128702813, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128702815, "stop": 1756128704234, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_where_is_the_carlcare_service_outlet"}, {"name": "subSuite", "value": "TestEllaWhereIsCarlcareServiceOutlet"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1466bd7a0d3185c7.json", "parameterValues": []}