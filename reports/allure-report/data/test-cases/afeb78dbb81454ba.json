{"uid": "afeb78dbb81454ba", "name": "测试set customized cover screen返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen.TestEllaSetCustomizedCoverScreen#test_set_customized_cover_screen", "historyId": "104cf8a7ef102b6850b6d14f4cb14052", "time": {"start": 1756136309823, "stop": 1756136336154, "duration": 26331}, "description": "验证set customized cover screen指令返回预期的不支持响应", "descriptionHtml": "<p>验证set customized cover screen指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136297008, "stop": 1756136309821, "duration": 12813}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136309822, "stop": 1756136309822, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set customized cover screen指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set customized cover screen", "time": {"start": 1756136309823, "stop": 1756136335942, "duration": 26119}, "status": "passed", "steps": [{"name": "执行命令: set customized cover screen", "time": {"start": 1756136309823, "stop": 1756136335692, "duration": 25869}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136335692, "stop": 1756136335942, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "2c95eea758b61fde", "name": "测试总结", "source": "2c95eea758b61fde.txt", "type": "text/plain", "size": 356}, {"uid": "da978401de00c852", "name": "test_completed", "source": "da978401de00c852.png", "type": "image/png", "size": 185967}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136335942, "stop": 1756136335943, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136335943, "stop": 1756136336153, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "f4d119dc6b3616ce", "name": "测试总结", "source": "f4d119dc6b3616ce.txt", "type": "text/plain", "size": 356}, {"uid": "395ab68f7e24cea3", "name": "test_completed", "source": "395ab68f7e24cea3.png", "type": "image/png", "size": 185967}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e1ca0107e16b146c", "name": "stdout", "source": "e1ca0107e16b146c.txt", "type": "text/plain", "size": 12683}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136336154, "stop": 1756136336154, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136336155, "stop": 1756136337669, "duration": 1514}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_customized_cover_screen"}, {"name": "subSuite", "value": "TestEllaSetCustomizedCoverScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "afeb78dbb81454ba.json", "parameterValues": []}