{"uid": "10a262e3364ae443", "name": "测试smart charge能正常执行", "fullName": "testcases.test_ella.system_coupling.test_smart_charge.TestEllaSmartCharge#test_smart_charge", "historyId": "7c74ed3e622ad29dd79be61222ad59bc", "time": {"start": 1756126134902, "stop": 1756126160743, "duration": 25841}, "description": "smart charge", "descriptionHtml": "<p>smart charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126122207, "stop": 1756126134901, "duration": 12694}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126134901, "stop": 1756126134901, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "smart charge", "status": "passed", "steps": [{"name": "执行命令: smart charge", "time": {"start": 1756126134902, "stop": 1756126160488, "duration": 25586}, "status": "passed", "steps": [{"name": "执行命令: smart charge", "time": {"start": 1756126134902, "stop": 1756126160183, "duration": 25281}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126160183, "stop": 1756126160488, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "a0120e8ae0ff16f4", "name": "测试总结", "source": "a0120e8ae0ff16f4.txt", "type": "text/plain", "size": 377}, {"uid": "b44b49e2a3b2b747", "name": "test_completed", "source": "b44b49e2a3b2b747.png", "type": "image/png", "size": 202043}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126160488, "stop": 1756126160489, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126160490, "stop": 1756126160742, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "f592dab658625239", "name": "测试总结", "source": "f592dab658625239.txt", "type": "text/plain", "size": 377}, {"uid": "f982486e35b86f4e", "name": "test_completed", "source": "f982486e35b86f4e.png", "type": "image/png", "size": 202043}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "67670233cba92851", "name": "stdout", "source": "67670233cba92851.txt", "type": "text/plain", "size": 12823}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126160743, "stop": 1756126160743, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126160745, "stop": 1756126162114, "duration": 1369}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_smart_charge"}, {"name": "subSuite", "value": "TestEllaSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_smart_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "10a262e3364ae443.json", "parameterValues": []}