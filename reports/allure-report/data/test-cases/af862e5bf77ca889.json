{"uid": "af862e5bf77ca889", "name": "测试There are transparent, glowing multicolored soap bubbles around it", "fullName": "testcases.test_ella.unsupported_commands.test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it.TestEllaOpenPlayPoliticalNews#test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "historyId": "a2c60aff2518d86c9e5686c943130f40", "time": {"start": 1756138401534, "stop": 1756138427953, "duration": 26419}, "description": "测试There are transparent, glowing multicolored soap bubbles around it指令", "descriptionHtml": "<p>测试There are transparent, glowing multicolored soap bubbles around it指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138388763, "stop": 1756138401532, "duration": 12769}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138401532, "stop": 1756138401532, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试There are transparent, glowing multicolored soap bubbles around it指令", "status": "passed", "steps": [{"name": "执行命令: There are transparent, glowing multicolored soap bubbles around it", "time": {"start": 1756138401534, "stop": 1756138427723, "duration": 26189}, "status": "passed", "steps": [{"name": "执行命令: There are transparent, glowing multicolored soap bubbles around it", "time": {"start": 1756138401534, "stop": 1756138427472, "duration": 25938}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138427472, "stop": 1756138427722, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "e06ef564230ac905", "name": "测试总结", "source": "e06ef564230ac905.txt", "type": "text/plain", "size": 1041}, {"uid": "9143c211ad7ebeee", "name": "test_completed", "source": "9143c211ad7ebeee.png", "type": "image/png", "size": 204828}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138427723, "stop": 1756138427723, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138427724, "stop": 1756138427952, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "a19886d2722cdb09", "name": "测试总结", "source": "a19886d2722cdb09.txt", "type": "text/plain", "size": 1041}, {"uid": "87e9ad3903cd0755", "name": "test_completed", "source": "87e9ad3903cd0755.png", "type": "image/png", "size": 204828}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "15a760c7b6bd2b09", "name": "stdout", "source": "15a760c7b6bd2b09.txt", "type": "text/plain", "size": 16033}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138427953, "stop": 1756138427953, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138428015, "stop": 1756138429404, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "af862e5bf77ca889.json", "parameterValues": []}