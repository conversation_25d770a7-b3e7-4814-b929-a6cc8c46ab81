{"uid": "7a4920c4f7d50314", "name": "测试increase notification volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_increase_notification_volume.TestEllaIncreaseNotificationVolume#test_increase_notification_volume", "historyId": "e5dc64184617a9497ec52a3b74103b55", "time": {"start": 1756124546670, "stop": 1756124573086, "duration": 26416}, "description": "increase notification volume", "descriptionHtml": "<p>increase notification volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124533573, "stop": 1756124546668, "duration": 13095}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124546668, "stop": 1756124546668, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "increase notification volume", "status": "passed", "steps": [{"name": "执行命令: increase notification volume", "time": {"start": 1756124546670, "stop": 1756124572820, "duration": 26150}, "status": "passed", "steps": [{"name": "执行命令: increase notification volume", "time": {"start": 1756124546670, "stop": 1756124572493, "duration": 25823}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124572493, "stop": 1756124572820, "duration": 327}, "status": "passed", "steps": [], "attachments": [{"uid": "7154b998c8e2db04", "name": "测试总结", "source": "7154b998c8e2db04.txt", "type": "text/plain", "size": 320}, {"uid": "ce73c87b3278cf5f", "name": "test_completed", "source": "ce73c87b3278cf5f.png", "type": "image/png", "size": 185933}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124572820, "stop": 1756124572827, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124572827, "stop": 1756124572827, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124572827, "stop": 1756124573084, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "ebab82481470ed9d", "name": "测试总结", "source": "ebab82481470ed9d.txt", "type": "text/plain", "size": 320}, {"uid": "203bc746dbb05334", "name": "test_completed", "source": "203bc746dbb05334.png", "type": "image/png", "size": 185933}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d30d7d2e1ea00c1b", "name": "stdout", "source": "d30d7d2e1ea00c1b.txt", "type": "text/plain", "size": 14025}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124573087, "stop": 1756124573087, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124573091, "stop": 1756124574539, "duration": 1448}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_notification_volume"}, {"name": "subSuite", "value": "TestEllaIncreaseNotificationVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_notification_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7a4920c4f7d50314.json", "parameterValues": []}