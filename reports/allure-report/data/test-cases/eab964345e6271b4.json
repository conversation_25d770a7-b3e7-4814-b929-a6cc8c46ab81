{"uid": "eab964345e6271b4", "name": "测试play music on boomplayer", "fullName": "testcases.test_ella.dialogue.test_play_music_on_boomplayer.TestEllaOpenPlayPoliticalNews#test_play_music_on_boomplayer", "historyId": "0e7fd56ff1d5c85e0ce1830d9899313d", "time": {"start": 1756120972964, "stop": 1756121001306, "duration": 28342}, "description": "测试play music on boomplayer指令", "descriptionHtml": "<p>测试play music on boomplayer指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120960149, "stop": 1756120972963, "duration": 12814}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120972963, "stop": 1756120972963, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music on boomplayer指令", "status": "passed", "steps": [{"name": "执行命令: play music on boomplayer", "time": {"start": 1756120972964, "stop": 1756121001048, "duration": 28084}, "status": "passed", "steps": [{"name": "执行命令: play music on boomplayer", "time": {"start": 1756120972964, "stop": 1756121000779, "duration": 27815}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121000779, "stop": 1756121001047, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "f5bbb00e44e816bc", "name": "测试总结", "source": "f5bbb00e44e816bc.txt", "type": "text/plain", "size": 319}, {"uid": "83c9be2b9d3003a4", "name": "test_completed", "source": "83c9be2b9d3003a4.png", "type": "image/png", "size": 188823}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121001048, "stop": 1756121001051, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121001051, "stop": 1756121001305, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "f68b8fd3dcad284", "name": "测试总结", "source": "f68b8fd3dcad284.txt", "type": "text/plain", "size": 319}, {"uid": "600200108b4a1627", "name": "test_completed", "source": "600200108b4a1627.png", "type": "image/png", "size": 189166}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "24119b755ba6ffb0", "name": "stdout", "source": "24119b755ba6ffb0.txt", "type": "text/plain", "size": 13314}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121001307, "stop": 1756121001307, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121001313, "stop": 1756121002719, "duration": 1406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_on_boomplayer"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_on_boomplayer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "eab964345e6271b4.json", "parameterValues": []}