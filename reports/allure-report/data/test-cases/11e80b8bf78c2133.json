{"uid": "11e80b8bf78c2133", "name": "测试turn up the brightness to the max能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_up_the_brightness_to_the_max.TestEllaTurnUpBrightnessMax#test_turn_up_the_brightness_to_the_max", "historyId": "1f9da5f1f2977bbbae31e0d3334eff82", "time": {"start": 1756128550567, "stop": 1756128577438, "duration": 26871}, "description": "turn up the brightness to the max", "descriptionHtml": "<p>turn up the brightness to the max</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128537905, "stop": 1756128550566, "duration": 12661}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128550567, "stop": 1756128550567, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn up the brightness to the max", "status": "passed", "steps": [{"name": "执行命令: turn up the brightness to the max", "time": {"start": 1756128550567, "stop": 1756128577219, "duration": 26652}, "status": "passed", "steps": [{"name": "执行命令: turn up the brightness to the max", "time": {"start": 1756128550567, "stop": 1756128576962, "duration": 26395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128576963, "stop": 1756128577217, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "e872c3fe00deefc0", "name": "测试总结", "source": "e872c3fe00deefc0.txt", "type": "text/plain", "size": 240}, {"uid": "d8fedffe55996a41", "name": "test_completed", "source": "d8fedffe55996a41.png", "type": "image/png", "size": 159568}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128577219, "stop": 1756128577220, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128577220, "stop": 1756128577220, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128577220, "stop": 1756128577436, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "e15f5540ec34881a", "name": "测试总结", "source": "e15f5540ec34881a.txt", "type": "text/plain", "size": 240}, {"uid": "9e8827f3df3925eb", "name": "test_completed", "source": "9e8827f3df3925eb.png", "type": "image/png", "size": 159568}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e331bbc58de6b314", "name": "stdout", "source": "e331bbc58de6b314.txt", "type": "text/plain", "size": 13261}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128577439, "stop": 1756128577439, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128577440, "stop": 1756128578878, "duration": 1438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_the_brightness_to_the_max"}, {"name": "subSuite", "value": "TestEllaTurnUpBrightnessMax"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_the_brightness_to_the_max"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "11e80b8bf78c2133.json", "parameterValues": []}