{"uid": "8f67f1b31b15f745", "name": "测试make a call on whatsapp to a能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a.TestEllaMakeCallWhatsapp#test_make_a_call_on_whatsapp_to_a", "historyId": "221d94649ab3a384e6bc24767dceba21", "time": {"start": 1756134145153, "stop": 1756134180248, "duration": 35095}, "description": "make a call on whatsapp to a", "descriptionHtml": "<p>make a call on whatsapp to a</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['make a call on whatsapp to a', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a.TestEllaMakeCallWhatsapp object at 0x00000292053FF9D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920960FB90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_make_a_call_on_whatsapp_to_a(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['make a call on whatsapp to a', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_make_a_call_on_whatsapp_to_a.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134132568, "stop": 1756134145151, "duration": 12583}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134145152, "stop": 1756134145152, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "make a call on whatsapp to a", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['make a call on whatsapp to a', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a.TestEllaMakeCallWhatsapp object at 0x00000292053FF9D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920960FB90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_make_a_call_on_whatsapp_to_a(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['make a call on whatsapp to a', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_make_a_call_on_whatsapp_to_a.py:34: AssertionError", "steps": [{"name": "执行命令: make a call on whatsapp to a", "time": {"start": 1756134145153, "stop": 1756134180235, "duration": 35082}, "status": "passed", "steps": [{"name": "执行命令: make a call on whatsapp to a", "time": {"start": 1756134145153, "stop": 1756134179960, "duration": 34807}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134179961, "stop": 1756134180234, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "98f8567224f35fbd", "name": "测试总结", "source": "98f8567224f35fbd.txt", "type": "text/plain", "size": 329}, {"uid": "8cff79249b0e93f3", "name": "test_completed", "source": "8cff79249b0e93f3.png", "type": "image/png", "size": 189564}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134180235, "stop": 1756134180243, "duration": 8}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['make a call on whatsapp to a', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_make_a_call_on_whatsapp_to_a.py\", line 34, in test_make_a_call_on_whatsapp_to_a\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "28cd3855c4a3e480", "name": "stdout", "source": "28cd3855c4a3e480.txt", "type": "text/plain", "size": 14073}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134180260, "stop": 1756134180496, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "bb61e0509a54bd88", "name": "失败截图-TestEllaMakeCallWhatsapp", "source": "bb61e0509a54bd88.png", "type": "image/png", "size": 189398}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134180497, "stop": 1756134181974, "duration": 1477}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_make_a_call_on_whatsapp_to_a"}, {"name": "subSuite", "value": "TestEllaMakeCallWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "8f67f1b31b15f745.json", "parameterValues": []}