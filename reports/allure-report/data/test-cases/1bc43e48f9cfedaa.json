{"uid": "1bc43e48f9cfedaa", "name": "测试open bt", "fullName": "testcases.test_ella.system_coupling.test_open_bt.TestEllaOpenBluetooth#test_open_bt", "historyId": "9511be8e6426d5078713c6e78f3b02e3", "time": {"start": 1756125313307, "stop": 1756125340439, "duration": 27132}, "description": "测试open bt指令", "descriptionHtml": "<p>测试open bt指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125300098, "stop": 1756125313306, "duration": 13208}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125313306, "stop": 1756125313306, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open bt指令", "status": "passed", "steps": [{"name": "执行命令: open bt", "time": {"start": 1756125313307, "stop": 1756125340185, "duration": 26878}, "status": "passed", "steps": [{"name": "执行命令: open bt", "time": {"start": 1756125313307, "stop": 1756125339933, "duration": 26626}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125339933, "stop": 1756125340185, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "e39f023b5d37f754", "name": "测试总结", "source": "e39f023b5d37f754.txt", "type": "text/plain", "size": 200}, {"uid": "851368351724f4d5", "name": "test_completed", "source": "851368351724f4d5.png", "type": "image/png", "size": 153438}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125340187, "stop": 1756125340192, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证bluetooth已打开", "time": {"start": 1756125340192, "stop": 1756125340192, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125340192, "stop": 1756125340438, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "910288244365ff5b", "name": "测试总结", "source": "910288244365ff5b.txt", "type": "text/plain", "size": 200}, {"uid": "60078d07a86cac8", "name": "test_completed", "source": "60078d07a86cac8.png", "type": "image/png", "size": 153438}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "722dff33ad549a96", "name": "stdout", "source": "722dff33ad549a96.txt", "type": "text/plain", "size": 13330}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125340440, "stop": 1756125340440, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125340442, "stop": 1756125341874, "duration": 1432}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bt"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bt"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1bc43e48f9cfedaa.json", "parameterValues": []}