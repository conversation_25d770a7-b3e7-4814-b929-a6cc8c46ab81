{"uid": "dc6b89449b488a56", "name": "测试turn on driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode.TestEllaTurnDrivingMode#test_turn_on_driving_mode", "historyId": "d8a3659601151a79f3b71ba4e47cafee", "time": {"start": 1756138603571, "stop": 1756138628624, "duration": 25053}, "description": "验证turn on driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138590961, "stop": 1756138603569, "duration": 12608}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138603569, "stop": 1756138603569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证turn on driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn on driving mode", "time": {"start": 1756138603571, "stop": 1756138628400, "duration": 24829}, "status": "passed", "steps": [{"name": "执行命令: turn on driving mode", "time": {"start": 1756138603571, "stop": 1756138628126, "duration": 24555}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138628126, "stop": 1756138628399, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "6af7aacafb247db2", "name": "测试总结", "source": "6af7aacafb247db2.txt", "type": "text/plain", "size": 339}, {"uid": "a10086747e06b9ab", "name": "test_completed", "source": "a10086747e06b9ab.png", "type": "image/png", "size": 184271}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138628400, "stop": 1756138628404, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138628404, "stop": 1756138628623, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "9792b347e1deae34", "name": "测试总结", "source": "9792b347e1deae34.txt", "type": "text/plain", "size": 339}, {"uid": "35dee16162648253", "name": "test_completed", "source": "35dee16162648253.png", "type": "image/png", "size": 184271}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1ffd814c534d0344", "name": "stdout", "source": "1ffd814c534d0344.txt", "type": "text/plain", "size": 12593}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138628624, "stop": 1756138628624, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138628626, "stop": 1756138630038, "duration": 1412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dc6b89449b488a56.json", "parameterValues": []}