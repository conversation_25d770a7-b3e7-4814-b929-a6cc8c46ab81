{"uid": "dde91288169d670b", "name": "测试disable running lock返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_running_lock.TestEllaDisableRunningLock#test_disable_running_lock", "historyId": "bb51fd67dac102de95e755be72996bd1", "time": {"start": 1756131075377, "stop": 1756131111841, "duration": 36464}, "description": "验证disable running lock指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable running lock指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131062394, "stop": 1756131075376, "duration": 12982}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131075376, "stop": 1756131075376, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable running lock指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable running lock", "time": {"start": 1756131075377, "stop": 1756131111594, "duration": 36217}, "status": "passed", "steps": [{"name": "执行命令: disable running lock", "time": {"start": 1756131075377, "stop": 1756131111297, "duration": 35920}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131111297, "stop": 1756131111593, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "c83c3cf22eb5097c", "name": "测试总结", "source": "c83c3cf22eb5097c.txt", "type": "text/plain", "size": 544}, {"uid": "826f6b465b7d0c1e", "name": "test_completed", "source": "826f6b465b7d0c1e.png", "type": "image/png", "size": 180155}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131111594, "stop": 1756131111597, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131111597, "stop": 1756131111840, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "1a51ff93d8ab2930", "name": "测试总结", "source": "1a51ff93d8ab2930.txt", "type": "text/plain", "size": 544}, {"uid": "9c5ffbc829893d91", "name": "test_completed", "source": "9c5ffbc829893d91.png", "type": "image/png", "size": 180158}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e43bdc06c6d4fde8", "name": "stdout", "source": "e43bdc06c6d4fde8.txt", "type": "text/plain", "size": 15497}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131111842, "stop": 1756131111842, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131111846, "stop": 1756131113280, "duration": 1434}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_running_lock"}, {"name": "subSuite", "value": "TestEllaDisableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_running_lock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dde91288169d670b.json", "parameterValues": []}