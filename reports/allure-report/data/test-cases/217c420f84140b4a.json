{"uid": "217c420f84140b4a", "name": "测试close airplane能正常执行", "fullName": "testcases.test_ella.system_coupling.test_close_airplane.TestEllaCloseAirplane#test_close_airplane", "historyId": "32ba476d46e86e963aa12ced39981955", "time": {"start": 1756123979383, "stop": 1756124005399, "duration": 26016}, "description": "close airplane", "descriptionHtml": "<p>close airplane</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123966611, "stop": 1756123979381, "duration": 12770}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123979381, "stop": 1756123979381, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close airplane", "status": "passed", "steps": [{"name": "执行命令: close airplane", "time": {"start": 1756123979383, "stop": 1756124005167, "duration": 25784}, "status": "passed", "steps": [{"name": "执行命令: close airplane", "time": {"start": 1756123979383, "stop": 1756124004896, "duration": 25513}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124004896, "stop": 1756124005167, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "a6aee3d0cb72fabf", "name": "测试总结", "source": "a6aee3d0cb72fabf.txt", "type": "text/plain", "size": 304}, {"uid": "2a9f714b0255d9af", "name": "test_completed", "source": "2a9f714b0255d9af.png", "type": "image/png", "size": 171569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124005167, "stop": 1756124005170, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124005170, "stop": 1756124005170, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124005170, "stop": 1756124005399, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "2b675278632096ee", "name": "测试总结", "source": "2b675278632096ee.txt", "type": "text/plain", "size": 304}, {"uid": "43a49124b05eed90", "name": "test_completed", "source": "43a49124b05eed90.png", "type": "image/png", "size": 171569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "29118530372b932f", "name": "stdout", "source": "29118530372b932f.txt", "type": "text/plain", "size": 12868}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124005400, "stop": 1756124005400, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124005401, "stop": 1756124006796, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_airplane"}, {"name": "subSuite", "value": "TestEllaCloseAirplane"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_airplane"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "217c420f84140b4a.json", "parameterValues": []}