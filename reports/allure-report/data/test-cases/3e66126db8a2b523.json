{"uid": "3e66126db8a2b523", "name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "fullName": "testcases.test_ella.unsupported_commands.test_generate_an_image_of_a_chubby_orange_cat_chef.TestEllaOpenPlayPoliticalNews#test_generate_an_image_of_a_chubby_orange_cat_chef", "historyId": "00495562396e9306113e5f37378ac991", "time": {"start": 1756132198771, "stop": 1756132224878, "duration": 26107}, "description": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance指令", "descriptionHtml": "<p>测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132186039, "stop": 1756132198770, "duration": 12731}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132198770, "stop": 1756132198770, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance指令", "status": "passed", "steps": [{"name": "执行命令: Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "time": {"start": 1756132198771, "stop": 1756132224631, "duration": 25860}, "status": "passed", "steps": [{"name": "执行命令: Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "time": {"start": 1756132198771, "stop": 1756132224355, "duration": 25584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132224355, "stop": 1756132224630, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "88a49d288fc7cb3a", "name": "测试总结", "source": "88a49d288fc7cb3a.txt", "type": "text/plain", "size": 465}, {"uid": "63602b84463b7a5b", "name": "test_completed", "source": "63602b84463b7a5b.png", "type": "image/png", "size": 196790}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132224631, "stop": 1756132224632, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132224632, "stop": 1756132224877, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "7a9c113a528cb58a", "name": "测试总结", "source": "7a9c113a528cb58a.txt", "type": "text/plain", "size": 465}, {"uid": "e85aa5a597f87cf2", "name": "test_completed", "source": "e85aa5a597f87cf2.png", "type": "image/png", "size": 196790}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4b2677eb98b8df4a", "name": "stdout", "source": "4b2677eb98b8df4a.txt", "type": "text/plain", "size": 13615}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132224879, "stop": 1756132224879, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132224880, "stop": 1756132226308, "duration": 1428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_an_image_of_a_chubby_orange_cat_chef"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_an_image_of_a_chubby_orange_cat_chef"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3e66126db8a2b523.json", "parameterValues": []}