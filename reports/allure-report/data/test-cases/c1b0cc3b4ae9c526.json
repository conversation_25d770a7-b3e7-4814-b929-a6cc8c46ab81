{"uid": "c1b0cc3b4ae9c526", "name": "测试go on playing fm能正常执行", "fullName": "testcases.test_ella.dialogue.test_go_on_playing_fm.TestEllaHelloHello#test_go_on_playing_fm", "historyId": "d60fcab377d9b5093e0f03ecf20f5d10", "time": {"start": 1756119768576, "stop": 1756119795676, "duration": 27100}, "description": "go on playing fm", "descriptionHtml": "<p>go on playing fm</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119755568, "stop": 1756119768575, "duration": 13007}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119768575, "stop": 1756119768575, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "go on playing fm", "status": "passed", "steps": [{"name": "执行命令: go on playing fm", "time": {"start": 1756119768577, "stop": 1756119795422, "duration": 26845}, "status": "passed", "steps": [{"name": "执行命令: go on playing fm", "time": {"start": 1756119768577, "stop": 1756119795165, "duration": 26588}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119795166, "stop": 1756119795421, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "e3e8c59013ae9f61", "name": "测试总结", "source": "e3e8c59013ae9f61.txt", "type": "text/plain", "size": 323}, {"uid": "5eadbdadefd25b8f", "name": "test_completed", "source": "5eadbdadefd25b8f.png", "type": "image/png", "size": 178240}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119795422, "stop": 1756119795425, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119795425, "stop": 1756119795675, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "238b4200f1cb42aa", "name": "测试总结", "source": "238b4200f1cb42aa.txt", "type": "text/plain", "size": 323}, {"uid": "d893f29689225b56", "name": "test_completed", "source": "d893f29689225b56.png", "type": "image/png", "size": 178240}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7637d4a146f56ccf", "name": "stdout", "source": "7637d4a146f56ccf.txt", "type": "text/plain", "size": 12979}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119795677, "stop": 1756119795677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119795682, "stop": 1756119797071, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_go_on_playing_fm"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_go_on_playing_fm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c1b0cc3b4ae9c526.json", "parameterValues": []}