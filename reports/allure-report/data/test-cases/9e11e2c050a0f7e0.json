{"uid": "9e11e2c050a0f7e0", "name": "测试Language List", "fullName": "testcases.test_ella.unsupported_commands.test_Language_List.TestEllaOpenPlayPoliticalNews#test_Language_List", "historyId": "f9558282973df5c72bd1c57fb0e19984", "time": {"start": 1756129389527, "stop": 1756129423090, "duration": 33563}, "description": "测试Language List指令", "descriptionHtml": "<p>测试Language List指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129376662, "stop": 1756129389525, "duration": 12863}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129389525, "stop": 1756129389525, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Language List指令", "status": "passed", "steps": [{"name": "执行命令: Language List", "time": {"start": 1756129389527, "stop": 1756129422794, "duration": 33267}, "status": "passed", "steps": [{"name": "执行命令: Language List", "time": {"start": 1756129389527, "stop": 1756129422539, "duration": 33012}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129422539, "stop": 1756129422793, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "69112bc4c5df45d2", "name": "测试总结", "source": "69112bc4c5df45d2.txt", "type": "text/plain", "size": 549}, {"uid": "a35891cdd10d5f0", "name": "test_completed", "source": "a35891cdd10d5f0.png", "type": "image/png", "size": 172402}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129422794, "stop": 1756129422795, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129422795, "stop": 1756129423089, "duration": 294}, "status": "passed", "steps": [], "attachments": [{"uid": "591abaff2d32e31b", "name": "测试总结", "source": "591abaff2d32e31b.txt", "type": "text/plain", "size": 549}, {"uid": "32b83160ba34742", "name": "test_completed", "source": "32b83160ba34742.png", "type": "image/png", "size": 172346}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "927ab232840d8b1e", "name": "stdout", "source": "927ab232840d8b1e.txt", "type": "text/plain", "size": 14860}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129423091, "stop": 1756129423091, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129423092, "stop": 1756129424502, "duration": 1410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Language_List"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Language_List"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9e11e2c050a0f7e0.json", "parameterValues": []}