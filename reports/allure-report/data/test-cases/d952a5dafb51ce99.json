{"uid": "d952a5dafb51ce99", "name": "测试increase settings for special functions返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions.TestEllaIncreaseSettingsSpecialFunctions#test_increase_settings_for_special_functions", "historyId": "5050d8dc816d181ca0c76dc56c8cb5f2", "time": {"start": 1756133344845, "stop": 1756133381406, "duration": 36561}, "description": "验证increase settings for special functions指令返回预期的不支持响应", "descriptionHtml": "<p>验证increase settings for special functions指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['increase settings for special functions', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions.TestEllaIncreaseSettingsSpecialFunctions object at 0x0000029205333150>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292084EBE50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_increase_settings_for_special_functions(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['increase settings for special functions', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_increase_settings_for_special_functions.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133331901, "stop": 1756133344844, "duration": 12943}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133344845, "stop": 1756133344845, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证increase settings for special functions指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['increase settings for special functions', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions.TestEllaIncreaseSettingsSpecialFunctions object at 0x0000029205333150>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292084EBE50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_increase_settings_for_special_functions(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['increase settings for special functions', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_increase_settings_for_special_functions.py:33: AssertionError", "steps": [{"name": "执行命令: increase settings for special functions", "time": {"start": 1756133344845, "stop": 1756133381395, "duration": 36550}, "status": "passed", "steps": [{"name": "执行命令: increase settings for special functions", "time": {"start": 1756133344845, "stop": 1756133381128, "duration": 36283}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133381128, "stop": 1756133381394, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "8abfef08613e639d", "name": "测试总结", "source": "8abfef08613e639d.txt", "type": "text/plain", "size": 901}, {"uid": "d7388be0f0444b75", "name": "test_completed", "source": "d7388be0f0444b75.png", "type": "image/png", "size": 175721}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133381395, "stop": 1756133381402, "duration": 7}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['increase settings for special functions', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_increase_settings_for_special_functions.py\", line 33, in test_increase_settings_for_special_functions\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "34f52e1ea062d84c", "name": "stdout", "source": "34f52e1ea062d84c.txt", "type": "text/plain", "size": 17826}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133381417, "stop": 1756133381645, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "ad09b5c11458669e", "name": "失败截图-TestEllaIncreaseSettingsSpecialFunctions", "source": "ad09b5c11458669e.png", "type": "image/png", "size": 175605}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133381646, "stop": 1756133383129, "duration": 1483}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_increase_settings_for_special_functions"}, {"name": "subSuite", "value": "TestEllaIncreaseSettingsSpecialFunctions"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "d952a5dafb51ce99.json", "parameterValues": []}