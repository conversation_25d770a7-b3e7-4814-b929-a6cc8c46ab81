{"uid": "15b8b89c57a3139e", "name": "测试set Battery Saver setting能正常执行", "fullName": "testcases.test_ella.system_coupling.test_set_battery_saver_setting.TestEllaSetBatterySaverSetting#test_set_battery_saver_setting", "historyId": "92c8c8e017b096314ffde2f610a6791e", "time": {"start": 1756125827307, "stop": 1756125862600, "duration": 35293}, "description": "set Battery Saver setting", "descriptionHtml": "<p>set Battery Saver setting</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125814295, "stop": 1756125827305, "duration": 13010}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125827305, "stop": 1756125827305, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "set Battery Saver setting", "status": "passed", "steps": [{"name": "执行命令: set Battery Saver setting", "time": {"start": 1756125827307, "stop": 1756125862375, "duration": 35068}, "status": "passed", "steps": [{"name": "执行命令: set Battery Saver setting", "time": {"start": 1756125827307, "stop": 1756125862051, "duration": 34744}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125862051, "stop": 1756125862374, "duration": 323}, "status": "passed", "steps": [], "attachments": [{"uid": "372edf3f41580f7c", "name": "测试总结", "source": "372edf3f41580f7c.txt", "type": "text/plain", "size": 1067}, {"uid": "9cf557611d942f4e", "name": "test_completed", "source": "9cf557611d942f4e.png", "type": "image/png", "size": 179613}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125862375, "stop": 1756125862377, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125862377, "stop": 1756125862600, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "c014677d64b7da01", "name": "测试总结", "source": "c014677d64b7da01.txt", "type": "text/plain", "size": 1067}, {"uid": "4f5d8b685ff4aaf", "name": "test_completed", "source": "4f5d8b685ff4aaf.png", "type": "image/png", "size": 178820}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "93000fbe41628032", "name": "stdout", "source": "93000fbe41628032.txt", "type": "text/plain", "size": 16825}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125862601, "stop": 1756125862601, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125862602, "stop": 1756125864002, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_battery_saver_setting"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSetting"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_battery_saver_setting"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "15b8b89c57a3139e.json", "parameterValues": []}