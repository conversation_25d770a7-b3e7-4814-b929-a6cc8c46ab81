{"uid": "5de3bc0790693775", "name": "测试what time is it能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_what_time_is_it.TestEllaWhatTimeIsIt#test_what_time_is_it", "historyId": "fa47cb0b4427dd62fb8f91c9e5e15ace", "time": {"start": 1756139030120, "stop": 1756139056677, "duration": 26557}, "description": "what time is it", "descriptionHtml": "<p>what time is it</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756139017274, "stop": 1756139030119, "duration": 12845}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756139030119, "stop": 1756139030119, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what time is it", "status": "passed", "steps": [{"name": "执行命令: what time is it", "time": {"start": 1756139030120, "stop": 1756139056465, "duration": 26345}, "status": "passed", "steps": [{"name": "执行命令: what time is it", "time": {"start": 1756139030120, "stop": 1756139056198, "duration": 26078}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139056198, "stop": 1756139056462, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "2064492e6f3d3f68", "name": "测试总结", "source": "2064492e6f3d3f68.txt", "type": "text/plain", "size": 276}, {"uid": "fb98b5e057a26a49", "name": "test_completed", "source": "fb98b5e057a26a49.png", "type": "image/png", "size": 170346}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756139056465, "stop": 1756139056468, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139056468, "stop": 1756139056677, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "fb019216aba1d54f", "name": "测试总结", "source": "fb019216aba1d54f.txt", "type": "text/plain", "size": 276}, {"uid": "c7a8b3631f6c9978", "name": "test_completed", "source": "c7a8b3631f6c9978.png", "type": "image/png", "size": 170346}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ddc4abea596fb34", "name": "stdout", "source": "ddc4abea596fb34.txt", "type": "text/plain", "size": 12447}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756139056678, "stop": 1756139056678, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756139056679, "stop": 1756139058101, "duration": 1422}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_time_is_it"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsIt"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_time_is_it"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5de3bc0790693775.json", "parameterValues": []}