{"uid": "81a50a2846cbcd71", "name": "测试set smart hub返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub#test_set_smart_hub", "historyId": "61e923bb9b35a687b231b0c27b5ec620", "time": {"start": 1756137475972, "stop": 1756137502629, "duration": 26657}, "description": "验证set smart hub指令返回预期的不支持响应", "descriptionHtml": "<p>验证set smart hub指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart hub', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub object at 0x00000292057D0B50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8AB510>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_smart_hub(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart hub', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_smart_hub.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137462864, "stop": 1756137475971, "duration": 13107}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137475971, "stop": 1756137475971, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set smart hub指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart hub', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub object at 0x00000292057D0B50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8AB510>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_smart_hub(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart hub', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_smart_hub.py:33: AssertionError", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1756137475972, "stop": 1756137502624, "duration": 26652}, "status": "passed", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1756137475972, "stop": 1756137502385, "duration": 26413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137502385, "stop": 1756137502624, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "2385cfdf1b9cde2c", "name": "测试总结", "source": "2385cfdf1b9cde2c.txt", "type": "text/plain", "size": 244}, {"uid": "bdee4f3e4202db0e", "name": "test_completed", "source": "bdee4f3e4202db0e.png", "type": "image/png", "size": 168455}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137502624, "stop": 1756137502627, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart hub', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_smart_hub.py\", line 33, in test_set_smart_hub\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3f189bdac53fa8bf", "name": "stdout", "source": "3f189bdac53fa8bf.txt", "type": "text/plain", "size": 13023}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137502634, "stop": 1756137502882, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "aba747bb4145bb49", "name": "失败截图-TestEllaSetSmartHub", "source": "aba747bb4145bb49.png", "type": "image/png", "size": 168455}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137502884, "stop": 1756137504356, "duration": 1472}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_hub"}, {"name": "subSuite", "value": "TestEllaSetSmartHub"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_hub"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "81a50a2846cbcd71.json", "parameterValues": []}