{"uid": "25d969dcc5e06115", "name": "测试turn on do not disturb mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_do_not_disturb_mode.TestEllaTurnDoNotDisturbMode#test_turn_on_do_not_disturb_mode", "historyId": "e32881dd9d54414fa74d523ef27b055c", "time": {"start": 1756127902698, "stop": 1756127929233, "duration": 26535}, "description": "turn on do not disturb mode", "descriptionHtml": "<p>turn on do not disturb mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127889769, "stop": 1756127902695, "duration": 12926}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127902696, "stop": 1756127902696, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on do not disturb mode", "status": "passed", "steps": [{"name": "执行命令: turn on do not disturb mode", "time": {"start": 1756127902698, "stop": 1756127929014, "duration": 26316}, "status": "passed", "steps": [{"name": "执行命令: turn on do not disturb mode", "time": {"start": 1756127902698, "stop": 1756127928752, "duration": 26054}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127928752, "stop": 1756127929013, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "95731487801c0a87", "name": "测试总结", "source": "95731487801c0a87.txt", "type": "text/plain", "size": 331}, {"uid": "166fcc0c63b299c", "name": "test_completed", "source": "166fcc0c63b299c.png", "type": "image/png", "size": 154804}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127929014, "stop": 1756127929015, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756127929015, "stop": 1756127929015, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127929015, "stop": 1756127929232, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "b97f3df8d81ce80e", "name": "测试总结", "source": "b97f3df8d81ce80e.txt", "type": "text/plain", "size": 331}, {"uid": "125e27e92d0ff9d0", "name": "test_completed", "source": "125e27e92d0ff9d0.png", "type": "image/png", "size": 154804}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e8a48b4966fc9ec7", "name": "stdout", "source": "e8a48b4966fc9ec7.txt", "type": "text/plain", "size": 13370}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127929233, "stop": 1756127929233, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127929234, "stop": 1756127930599, "duration": 1365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_do_not_disturb_mode"}, {"name": "subSuite", "value": "TestEllaTurnDoNotDisturbMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_do_not_disturb_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "25d969dcc5e06115.json", "parameterValues": []}