{"uid": "2f3315ba815eaf19", "name": "测试next song能正常执行", "fullName": "testcases.test_ella.dialogue.test_next_song.TestEllaHowIsWeatherToday#test_next_song", "historyId": "1d8131ec3deb65d953f2f16c259f261b", "time": {"start": 1756120669893, "stop": 1756120695564, "duration": 25671}, "description": "next song", "descriptionHtml": "<p>next song</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120657029, "stop": 1756120669891, "duration": 12862}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120669891, "stop": 1756120669891, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "next song", "status": "passed", "steps": [{"name": "执行命令: next song", "time": {"start": 1756120669893, "stop": 1756120695352, "duration": 25459}, "status": "passed", "steps": [{"name": "执行命令: next song", "time": {"start": 1756120669893, "stop": 1756120695071, "duration": 25178}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120695071, "stop": 1756120695351, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "b323dbcdbffaea43", "name": "测试总结", "source": "b323dbcdbffaea43.txt", "type": "text/plain", "size": 284}, {"uid": "6d7d7892d6f779c5", "name": "test_completed", "source": "6d7d7892d6f779c5.png", "type": "image/png", "size": 177398}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120695352, "stop": 1756120695353, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120695353, "stop": 1756120695563, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "5ffaa049702c7e59", "name": "测试总结", "source": "5ffaa049702c7e59.txt", "type": "text/plain", "size": 284}, {"uid": "669b1a710540be82", "name": "test_completed", "source": "669b1a710540be82.png", "type": "image/png", "size": 177398}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "675eadf0b8566155", "name": "stdout", "source": "675eadf0b8566155.txt", "type": "text/plain", "size": 12429}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120695566, "stop": 1756120695566, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120695568, "stop": 1756120696921, "duration": 1353}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_next_song"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_next_song"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2f3315ba815eaf19.json", "parameterValues": []}