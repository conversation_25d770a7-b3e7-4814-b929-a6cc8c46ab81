{"uid": "7f41065353cdeda6", "name": "测试max alarm clock volume", "fullName": "testcases.test_ella.system_coupling.test_max_alarm_clock_volume.TestEllaOpenClock#test_max_alarm_clock_volume", "historyId": "8f69a86b2d665eb6925fa007d973040e", "time": {"start": 1756124794963, "stop": 1756124820839, "duration": 25876}, "description": "测试max alarm clock volume指令", "descriptionHtml": "<p>测试max alarm clock volume指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124781701, "stop": 1756124794961, "duration": 13260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124794961, "stop": 1756124794961, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试max alarm clock volume指令", "status": "passed", "steps": [{"name": "执行命令: max alarm clock volume", "time": {"start": 1756124794964, "stop": 1756124820577, "duration": 25613}, "status": "passed", "steps": [{"name": "执行命令: max alarm clock volume", "time": {"start": 1756124794964, "stop": 1756124820342, "duration": 25378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124820342, "stop": 1756124820576, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "f25990d8cfa7eb40", "name": "测试总结", "source": "f25990d8cfa7eb40.txt", "type": "text/plain", "size": 311}, {"uid": "b6e17501b93ed527", "name": "test_completed", "source": "b6e17501b93ed527.png", "type": "image/png", "size": 176625}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124820577, "stop": 1756124820581, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证clock已打开", "time": {"start": 1756124820581, "stop": 1756124820581, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124820581, "stop": 1756124820838, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "2259c2c8d528c6ae", "name": "测试总结", "source": "2259c2c8d528c6ae.txt", "type": "text/plain", "size": 311}, {"uid": "e95d672945acdba6", "name": "test_completed", "source": "e95d672945acdba6.png", "type": "image/png", "size": 176469}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "db770f9838ff2f35", "name": "stdout", "source": "db770f9838ff2f35.txt", "type": "text/plain", "size": 13892}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124820840, "stop": 1756124820840, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124820841, "stop": 1756124822233, "duration": 1392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_alarm_clock_volume"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_alarm_clock_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7f41065353cdeda6.json", "parameterValues": []}