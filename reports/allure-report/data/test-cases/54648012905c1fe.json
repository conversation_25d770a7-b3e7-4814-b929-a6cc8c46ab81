{"uid": "54648012905c1fe", "name": "测试switch to performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode.TestEllaSwitchPerformanceMode#test_switch_to_performance_mode", "historyId": "34f3c9cc9098f792051e7099b7a9fdc1", "time": {"start": 1756138066798, "stop": 1756138092676, "duration": 25878}, "description": "验证switch to performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to performance mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138054030, "stop": 1756138066796, "duration": 12766}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138066796, "stop": 1756138066796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证switch to performance mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1756138066798, "stop": 1756138092448, "duration": 25650}, "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1756138066798, "stop": 1756138092181, "duration": 25383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138092181, "stop": 1756138092447, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "894ad21b3f2c161", "name": "测试总结", "source": "894ad21b3f2c161.txt", "type": "text/plain", "size": 356}, {"uid": "2c97aeebd6d4e924", "name": "test_completed", "source": "2c97aeebd6d4e924.png", "type": "image/png", "size": 188052}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138092448, "stop": 1756138092450, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138092450, "stop": 1756138092676, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "31da346c999fd957", "name": "测试总结", "source": "31da346c999fd957.txt", "type": "text/plain", "size": 356}, {"uid": "288d242fdb8e5f2", "name": "test_completed", "source": "288d242fdb8e5f2.png", "type": "image/png", "size": 188052}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2fb2b56c1dde03af", "name": "stdout", "source": "2fb2b56c1dde03af.txt", "type": "text/plain", "size": 12674}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138092677, "stop": 1756138092677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138092679, "stop": 1756138094045, "duration": 1366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_performance_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "54648012905c1fe.json", "parameterValues": []}