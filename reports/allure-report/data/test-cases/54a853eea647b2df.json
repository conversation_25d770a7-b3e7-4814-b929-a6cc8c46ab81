{"uid": "54a853eea647b2df", "name": "测试turn down the brightness to the min能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_down_the_brightness_to_the_min.TestEllaTurnDownBrightnessMin#test_turn_down_the_brightness_to_the_min", "historyId": "fdcf3737e32a4361e11902caf25fed5f", "time": {"start": 1756127336526, "stop": 1756127363134, "duration": 26608}, "description": "turn down the brightness to the min", "descriptionHtml": "<p>turn down the brightness to the min</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127323558, "stop": 1756127336525, "duration": 12967}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127336525, "stop": 1756127336525, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn down the brightness to the min", "status": "passed", "steps": [{"name": "执行命令: turn down the brightness to the min", "time": {"start": 1756127336526, "stop": 1756127362857, "duration": 26331}, "status": "passed", "steps": [{"name": "执行命令: turn down the brightness to the min", "time": {"start": 1756127336526, "stop": 1756127362631, "duration": 26105}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127362631, "stop": 1756127362857, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "fb040c62888d6ab4", "name": "测试总结", "source": "fb040c62888d6ab4.txt", "type": "text/plain", "size": 240}, {"uid": "ad4a78e382868bc5", "name": "test_completed", "source": "ad4a78e382868bc5.png", "type": "image/png", "size": 156141}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127362857, "stop": 1756127362858, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127362858, "stop": 1756127362858, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127362858, "stop": 1756127363133, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "9a8907d46a4848c2", "name": "测试总结", "source": "9a8907d46a4848c2.txt", "type": "text/plain", "size": 240}, {"uid": "34fb4113221152d4", "name": "test_completed", "source": "34fb4113221152d4.png", "type": "image/png", "size": 156141}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6906a40af0d6c68c", "name": "stdout", "source": "6906a40af0d6c68c.txt", "type": "text/plain", "size": 13268}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127363134, "stop": 1756127363134, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127363135, "stop": 1756127364468, "duration": 1333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_the_brightness_to_the_min"}, {"name": "subSuite", "value": "TestEllaTurnDownBrightnessMin"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_the_brightness_to_the_min"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "54a853eea647b2df.json", "parameterValues": []}