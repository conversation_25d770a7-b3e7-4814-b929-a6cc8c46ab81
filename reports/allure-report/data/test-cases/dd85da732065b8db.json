{"uid": "dd85da732065b8db", "name": "测试Generate a picture of a jungle stream for me", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_picture_of_a_jungle_stream_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_picture_of_a_jungle_stream_for_me", "historyId": "700a4f81d53d76c265778c230c99dd8c", "time": {"start": 1756132157922, "stop": 1756132184631, "duration": 26709}, "description": "测试Generate a picture of a jungle stream for me指令", "descriptionHtml": "<p>测试Generate a picture of a jungle stream for me指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132144463, "stop": 1756132157921, "duration": 13458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132157921, "stop": 1756132157921, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate a picture of a jungle stream for me指令", "status": "passed", "steps": [{"name": "执行命令: Generate a picture of a jungle stream for me", "time": {"start": 1756132157923, "stop": 1756132184374, "duration": 26451}, "status": "passed", "steps": [{"name": "执行命令: Generate a picture of a jungle stream for me", "time": {"start": 1756132157923, "stop": 1756132184129, "duration": 26206}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132184129, "stop": 1756132184373, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "6fefa11ff8f218eb", "name": "测试总结", "source": "6fefa11ff8f218eb.txt", "type": "text/plain", "size": 371}, {"uid": "3d39a06f3bfc2dad", "name": "test_completed", "source": "3d39a06f3bfc2dad.png", "type": "image/png", "size": 192409}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132184374, "stop": 1756132184378, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132184378, "stop": 1756132184630, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "b779ccd2539ad103", "name": "测试总结", "source": "b779ccd2539ad103.txt", "type": "text/plain", "size": 371}, {"uid": "3ad6031c97cb4cb6", "name": "test_completed", "source": "3ad6031c97cb4cb6.png", "type": "image/png", "size": 192409}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f620072a5cdb5216", "name": "stdout", "source": "f620072a5cdb5216.txt", "type": "text/plain", "size": 13239}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756132184631, "stop": 1756132186030, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756132184631, "stop": 1756132184631, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_picture_of_a_jungle_stream_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_picture_of_a_jungle_stream_for_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dd85da732065b8db.json", "parameterValues": []}