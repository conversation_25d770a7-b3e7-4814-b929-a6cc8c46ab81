{"uid": "20e5b9d7937d26f6", "name": "测试remember the parking space", "fullName": "testcases.test_ella.unsupported_commands.test_remember_the_parking_space.TestEllaOpenPlayPoliticalNews#test_remember_the_parking_space", "historyId": "bd4f9d0c0f70cf6b24bb9923810b25c1", "time": {"start": 1756135649972, "stop": 1756135677551, "duration": 27579}, "description": "测试remember the parking space指令", "descriptionHtml": "<p>测试remember the parking space指令</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_remember_the_parking_space.TestEllaOpenPlayPoliticalNews object at 0x00000292055C1490>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096DA410>\n\n    @allure.title(\"测试remember the parking space\")\n    @allure.description(\"测试remember the parking space指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_remember_the_parking_space(self, ella_app):\n        \"\"\"测试remember the parking space命令\"\"\"\n        command = \"remember the parking space\"\n        expected_text = [\"Please state your parking address?\"]\n        ''\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_remember_the_parking_space.py:31: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135637174, "stop": 1756135649969, "duration": 12795}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135649970, "stop": 1756135649970, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试remember the parking space指令", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_remember_the_parking_space.TestEllaOpenPlayPoliticalNews object at 0x00000292055C1490>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096DA410>\n\n    @allure.title(\"测试remember the parking space\")\n    @allure.description(\"测试remember the parking space指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_remember_the_parking_space(self, ella_app):\n        \"\"\"测试remember the parking space命令\"\"\"\n        command = \"remember the parking space\"\n        expected_text = [\"Please state your parking address?\"]\n        ''\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_remember_the_parking_space.py:31: AssertionError", "steps": [{"name": "执行命令: remember the parking space", "time": {"start": 1756135649972, "stop": 1756135677548, "duration": 27576}, "status": "passed", "steps": [{"name": "执行命令: remember the parking space", "time": {"start": 1756135649972, "stop": 1756135677295, "duration": 27323}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135677295, "stop": 1756135677548, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "381294b9904bba27", "name": "测试总结", "source": "381294b9904bba27.txt", "type": "text/plain", "size": 318}, {"uid": "a591724734f2f43d", "name": "test_completed", "source": "a591724734f2f43d.png", "type": "image/png", "size": 180575}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135677548, "stop": 1756135677550, "duration": 2}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_remember_the_parking_space.py\", line 31, in test_remember_the_parking_space\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "80b208460c3ed1a7", "name": "stdout", "source": "80b208460c3ed1a7.txt", "type": "text/plain", "size": 13370}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135677565, "stop": 1756135677773, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "cd1e56fbc3290b3", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "cd1e56fbc3290b3.png", "type": "image/png", "size": 180334}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756135677776, "stop": 1756135679223, "duration": 1447}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_remember_the_parking_space"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_remember_the_parking_space"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "20e5b9d7937d26f6.json", "parameterValues": []}