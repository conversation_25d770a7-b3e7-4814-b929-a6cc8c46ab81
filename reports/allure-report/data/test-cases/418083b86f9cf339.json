{"uid": "418083b86f9cf339", "name": "测试Voice setting page返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage#test_voice_setting_page", "historyId": "9c301cfc137fb94f119957b5f74291ec", "time": {"start": 1756138818255, "stop": 1756138849110, "duration": 30855}, "description": "验证Voice setting page指令返回预期的不支持响应", "descriptionHtml": "<p>验证Voice setting page指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138805363, "stop": 1756138818254, "duration": 12891}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138818254, "stop": 1756138818254, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证Voice setting page指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1756138818255, "stop": 1756138848849, "duration": 30594}, "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1756138818255, "stop": 1756138848568, "duration": 30313}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138848568, "stop": 1756138848848, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "e2fccc5d1d74fd", "name": "测试总结", "source": "e2fccc5d1d74fd.txt", "type": "text/plain", "size": 478}, {"uid": "50154f33f8a66281", "name": "test_completed", "source": "50154f33f8a66281.png", "type": "image/png", "size": 167892}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138848849, "stop": 1756138848851, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138848851, "stop": 1756138849109, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "6d449a0feb189a5d", "name": "测试总结", "source": "6d449a0feb189a5d.txt", "type": "text/plain", "size": 478}, {"uid": "7597181ab53b9324", "name": "test_completed", "source": "7597181ab53b9324.png", "type": "image/png", "size": 167451}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2653f51770077bf3", "name": "stdout", "source": "2653f51770077bf3.txt", "type": "text/plain", "size": 15560}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138849110, "stop": 1756138849110, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138849111, "stop": 1756138850511, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_voice_setting_page"}, {"name": "subSuite", "value": "TestEllaVoiceSettingPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_voice_setting_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "418083b86f9cf339.json", "parameterValues": []}