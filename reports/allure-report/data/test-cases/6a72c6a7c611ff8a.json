{"uid": "6a72c6a7c611ff8a", "name": "测试enable auto pickup返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup.TestEllaEnableAutoPickup#test_enable_auto_pickup", "historyId": "57acf2797af332487c1fdb9a53a30e4f", "time": {"start": 1756131503606, "stop": 1756131535488, "duration": 31882}, "description": "验证enable auto pickup指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable auto pickup指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131491115, "stop": 1756131503606, "duration": 12491}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131503606, "stop": 1756131503606, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable auto pickup指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1756131503606, "stop": 1756131535283, "duration": 31677}, "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1756131503606, "stop": 1756131535034, "duration": 31428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131535034, "stop": 1756131535283, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "456dffc93bfba267", "name": "测试总结", "source": "456dffc93bfba267.txt", "type": "text/plain", "size": 382}, {"uid": "7eed5fa75cd65af4", "name": "test_completed", "source": "7eed5fa75cd65af4.png", "type": "image/png", "size": 173327}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131535284, "stop": 1756131535286, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131535286, "stop": 1756131535487, "duration": 201}, "status": "passed", "steps": [], "attachments": [{"uid": "ff9eb6edc37b0050", "name": "测试总结", "source": "ff9eb6edc37b0050.txt", "type": "text/plain", "size": 382}, {"uid": "e3c041babd661f39", "name": "test_completed", "source": "e3c041babd661f39.png", "type": "image/png", "size": 173424}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9901b0f616cd85da", "name": "stdout", "source": "9901b0f616cd85da.txt", "type": "text/plain", "size": 14719}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131535489, "stop": 1756131535489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131535490, "stop": 1756131536869, "duration": 1379}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaEnableAutoPickup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6a72c6a7c611ff8a.json", "parameterValues": []}