{"uid": "28a35cc7c4a8a636", "name": "测试enable brightness locking返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking.TestEllaEnableBrightnessLocking#test_enable_brightness_locking", "historyId": "42bb23fa5566b20ae050e85bbee099ef", "time": {"start": 1756131549782, "stop": 1756131585119, "duration": 35337}, "description": "验证enable brightness locking指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable brightness locking指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131536910, "stop": 1756131549781, "duration": 12871}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131549781, "stop": 1756131549781, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable brightness locking指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable brightness locking", "time": {"start": 1756131549782, "stop": 1756131584862, "duration": 35080}, "status": "passed", "steps": [{"name": "执行命令: enable brightness locking", "time": {"start": 1756131549782, "stop": 1756131584581, "duration": 34799}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131584581, "stop": 1756131584862, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "bbab03f6825aeb29", "name": "测试总结", "source": "bbab03f6825aeb29.txt", "type": "text/plain", "size": 552}, {"uid": "e7b3cf0ac898740e", "name": "test_completed", "source": "e7b3cf0ac898740e.png", "type": "image/png", "size": 167992}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131584862, "stop": 1756131584870, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131584870, "stop": 1756131585118, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "a280821f72613cd7", "name": "测试总结", "source": "a280821f72613cd7.txt", "type": "text/plain", "size": 552}, {"uid": "3ab3c49b6262eb9d", "name": "test_completed", "source": "3ab3c49b6262eb9d.png", "type": "image/png", "size": 168148}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9b48a5e10677f3c3", "name": "stdout", "source": "9b48a5e10677f3c3.txt", "type": "text/plain", "size": 15145}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131585119, "stop": 1756131585120, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131585120, "stop": 1756131586473, "duration": 1353}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_brightness_locking"}, {"name": "subSuite", "value": "TestEllaEnableBrightnessLocking"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "28a35cc7c4a8a636.json", "parameterValues": []}