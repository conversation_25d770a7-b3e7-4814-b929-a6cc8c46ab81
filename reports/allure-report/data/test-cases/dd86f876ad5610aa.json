{"uid": "dd86f876ad5610aa", "name": "测试merry christmas", "fullName": "testcases.test_ella.unsupported_commands.test_merry_christmas.TestEllaOpenPlayPoliticalNews#test_merry_christmas", "historyId": "c3dde525f6a284fe4a3e4b670182329f", "time": {"start": 1756134194934, "stop": 1756134223921, "duration": 28987}, "description": "测试merry christmas指令", "descriptionHtml": "<p>测试merry christmas指令</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.Merry Christmas to you']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_merry_christmas.TestEllaOpenPlayPoliticalNews object at 0x000002920541C7D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292053D2410>\n\n    @allure.title(\"测试merry christmas\")\n    @allure.description(\"测试merry christmas指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_merry_christmas(self, ella_app):\n        \"\"\"测试merry christmas命令\"\"\"\n        command = \"merry christmas\"\n        expected_text = ['The following images are generated for you.'\n                         'Merry Christmas to you'\n                         ]\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['The following images are generated for you.Merry Christmas to you']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_merry_christmas.py:32: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134182011, "stop": 1756134194932, "duration": 12921}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134194932, "stop": 1756134194932, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试merry christmas指令", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.Merry Christmas to you']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_merry_christmas.TestEllaOpenPlayPoliticalNews object at 0x000002920541C7D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292053D2410>\n\n    @allure.title(\"测试merry christmas\")\n    @allure.description(\"测试merry christmas指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_merry_christmas(self, ella_app):\n        \"\"\"测试merry christmas命令\"\"\"\n        command = \"merry christmas\"\n        expected_text = ['The following images are generated for you.'\n                         'Merry Christmas to you'\n                         ]\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['The following images are generated for you.Merry Christmas to you']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_merry_christmas.py:32: AssertionError", "steps": [{"name": "执行命令: merry christmas", "time": {"start": 1756134194934, "stop": 1756134223919, "duration": 28985}, "status": "passed", "steps": [{"name": "执行命令: merry christmas", "time": {"start": 1756134194934, "stop": 1756134223620, "duration": 28686}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134223620, "stop": 1756134223918, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "b67e8033f56755bd", "name": "测试总结", "source": "b67e8033f56755bd.txt", "type": "text/plain", "size": 283}, {"uid": "c983e83b3fc86d0a", "name": "test_completed", "source": "c983e83b3fc86d0a.png", "type": "image/png", "size": 184830}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134223919, "stop": 1756134223920, "duration": 1}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.Merry Christmas to you']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_merry_christmas.py\", line 32, in test_merry_christmas\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1bd2b4bb26c48a47", "name": "stdout", "source": "1bd2b4bb26c48a47.txt", "type": "text/plain", "size": 13503}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134223926, "stop": 1756134224150, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "7dc07bbab73281b0", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "7dc07bbab73281b0.png", "type": "image/png", "size": 184487}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134224152, "stop": 1756134225604, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_merry_christmas"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_merry_christmas"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "dd86f876ad5610aa.json", "parameterValues": []}