{"uid": "6a423d06e8cab535", "name": "测试whatsapp能正常执行", "fullName": "testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp#test_whatsapp", "historyId": "fae56e9bcf9e0511ef4a7c93775731e3", "time": {"start": 1756129300422, "stop": 1756129327616, "duration": 27194}, "description": "whatsapp", "descriptionHtml": "<p>whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129287798, "stop": 1756129300421, "duration": 12623}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129300421, "stop": 1756129300421, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "whatsapp", "status": "passed", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1756129300422, "stop": 1756129327386, "duration": 26964}, "status": "passed", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1756129300422, "stop": 1756129327100, "duration": 26678}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129327100, "stop": 1756129327384, "duration": 284}, "status": "passed", "steps": [], "attachments": [{"uid": "171d90987124fce6", "name": "测试总结", "source": "171d90987124fce6.txt", "type": "text/plain", "size": 317}, {"uid": "120a7625552f4ea7", "name": "test_completed", "source": "120a7625552f4ea7.png", "type": "image/png", "size": 181697}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129327386, "stop": 1756129327387, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129327387, "stop": 1756129327616, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "467eba088aacda44", "name": "测试总结", "source": "467eba088aacda44.txt", "type": "text/plain", "size": 317}, {"uid": "431a86e2f80a1ab6", "name": "test_completed", "source": "431a86e2f80a1ab6.png", "type": "image/png", "size": 181697}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "80691cb0d13c83e1", "name": "stdout", "source": "80691cb0d13c83e1.txt", "type": "text/plain", "size": 13126}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129327617, "stop": 1756129327617, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129327618, "stop": 1756129329034, "duration": 1416}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_whatsapp"}, {"name": "subSuite", "value": "TestEllaW<PERSON>sapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6a423d06e8cab535.json", "parameterValues": []}