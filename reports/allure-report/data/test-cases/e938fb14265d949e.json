{"uid": "e938fb14265d949e", "name": "测试where is my car能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_where_is_my_car.TestEllaWhereIsMyCar#test_where_is_my_car", "historyId": "79ed3b173757e627534e24ad9289f338", "time": {"start": 1756139153438, "stop": 1756139178846, "duration": 25408}, "description": "where is my car", "descriptionHtml": "<p>where is my car</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756139140670, "stop": 1756139153435, "duration": 12765}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756139153435, "stop": 1756139153435, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "where is my car", "status": "passed", "steps": [{"name": "执行命令: where is my car", "time": {"start": 1756139153438, "stop": 1756139178609, "duration": 25171}, "status": "passed", "steps": [{"name": "执行命令: where is my car", "time": {"start": 1756139153438, "stop": 1756139178354, "duration": 24916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139178354, "stop": 1756139178609, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "6f3a7c9e7116bffb", "name": "测试总结", "source": "6f3a7c9e7116bffb.txt", "type": "text/plain", "size": 303}, {"uid": "538b04d1f55d07d8", "name": "test_completed", "source": "538b04d1f55d07d8.png", "type": "image/png", "size": 168769}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756139178609, "stop": 1756139178611, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139178611, "stop": 1756139178845, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "333c44e134ca7cdf", "name": "测试总结", "source": "333c44e134ca7cdf.txt", "type": "text/plain", "size": 303}, {"uid": "440b4297e4c74bd2", "name": "test_completed", "source": "440b4297e4c74bd2.png", "type": "image/png", "size": 168769}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2cc4bfc35dbad482", "name": "stdout", "source": "2cc4bfc35dbad482.txt", "type": "text/plain", "size": 12687}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756139178846, "stop": 1756139178846, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756139178847, "stop": 1756139180259, "duration": 1412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_where_is_my_car"}, {"name": "subSuite", "value": "TestEllaWhereIsMyCar"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_where_is_my_car"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e938fb14265d949e.json", "parameterValues": []}