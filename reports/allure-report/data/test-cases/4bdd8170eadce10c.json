{"uid": "4bdd8170eadce10c", "name": "测试close ella能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla#test_close_ella", "historyId": "54b47105d42d2a9f18eec071fba40c73", "time": {"start": 1756117206000, "stop": 1756117245846, "duration": 39846}, "description": "close ella", "descriptionHtml": "<p>close ella</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117193036, "stop": 1756117205998, "duration": 12962}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117205999, "stop": 1756117205999, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close ella", "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1756117206000, "stop": 1756117245572, "duration": 39572}, "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1756117206000, "stop": 1756117245337, "duration": 39337}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117245337, "stop": 1756117245572, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "938c2ffe51d37ac5", "name": "测试总结", "source": "938c2ffe51d37ac5.txt", "type": "text/plain", "size": 763}, {"uid": "b94c80c2868fbd7d", "name": "test_completed", "source": "b94c80c2868fbd7d.png", "type": "image/png", "size": 155881}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证已打开", "time": {"start": 1756117245573, "stop": 1756117245573, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117245573, "stop": 1756117245846, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "431f36bd359c15", "name": "测试总结", "source": "431f36bd359c15.txt", "type": "text/plain", "size": 763}, {"uid": "18f939b8a293b1a4", "name": "test_completed", "source": "18f939b8a293b1a4.png", "type": "image/png", "size": 155864}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "163a7d9768eb508a", "name": "stdout", "source": "163a7d9768eb508a.txt", "type": "text/plain", "size": 16052}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117245847, "stop": 1756117245847, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117245848, "stop": 1756117247275, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_ella"}, {"name": "subSuite", "value": "TestEllaClose<PERSON>lla"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_ella"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4bdd8170eadce10c.json", "parameterValues": []}