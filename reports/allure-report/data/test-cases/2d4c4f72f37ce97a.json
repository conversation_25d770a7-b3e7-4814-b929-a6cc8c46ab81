{"uid": "2d4c4f72f37ce97a", "name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "fullName": "testcases.test_ella.unsupported_commands.test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance.TestEllaOpenPlayPoliticalNews#test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "historyId": "eb6abc860fad339739076abacb13ac83", "time": {"start": 1756131957652, "stop": 1756131983385, "duration": 25733}, "description": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.指令", "descriptionHtml": "<p>测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131944604, "stop": 1756131957650, "duration": 13046}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131957650, "stop": 1756131957650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.指令", "status": "passed", "steps": [{"name": "执行命令: flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "time": {"start": 1756131957652, "stop": 1756131983140, "duration": 25488}, "status": "passed", "steps": [{"name": "执行命令: flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "time": {"start": 1756131957652, "stop": 1756131982885, "duration": 25233}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131982885, "stop": 1756131983140, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "69ec1a8ba38ce199", "name": "测试总结", "source": "69ec1a8ba38ce199.txt", "type": "text/plain", "size": 679}, {"uid": "ded9793de6251805", "name": "test_completed", "source": "ded9793de6251805.png", "type": "image/png", "size": 202612}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756131983140, "stop": 1756131983142, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131983142, "stop": 1756131983383, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "f41cad3dee71b3eb", "name": "测试总结", "source": "f41cad3dee71b3eb.txt", "type": "text/plain", "size": 679}, {"uid": "cbc17f2bf60d6659", "name": "test_completed", "source": "cbc17f2bf60d6659.png", "type": "image/png", "size": 202612}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f69bd1af115aec14", "name": "stdout", "source": "f69bd1af115aec14.txt", "type": "text/plain", "size": 14319}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131983386, "stop": 1756131983386, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131983387, "stop": 1756131984785, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2d4c4f72f37ce97a.json", "parameterValues": []}