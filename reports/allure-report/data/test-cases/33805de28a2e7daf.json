{"uid": "33805de28a2e7daf", "name": "测试view recent alarms能正常执行", "fullName": "testcases.test_ella.dialogue.test_view_recent_alarms.TestEllaHowIsWeatherToday#test_view_recent_alarms", "historyId": "d960192ea83ce0c13a534ec13ca1700e", "time": {"start": 1756122150772, "stop": 1756122230712, "duration": 79940}, "description": "view recent alarms", "descriptionHtml": "<p>view recent alarms</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122137696, "stop": 1756122150769, "duration": 13073}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122150769, "stop": 1756122150769, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "view recent alarms", "status": "passed", "steps": [{"name": "执行命令:  delete all the alarms", "time": {"start": 1756122150772, "stop": 1756122176751, "duration": 25979}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756122150772, "stop": 1756122176459, "duration": 25687}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122176459, "stop": 1756122176750, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "7153aa7503cb0c5a", "name": "测试总结", "source": "7153aa7503cb0c5a.txt", "type": "text/plain", "size": 303}, {"uid": "479196dfac885025", "name": "test_completed", "source": "479196dfac885025.png", "type": "image/png", "size": 181621}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令:  Set an alarm at 10 am tomorrow", "time": {"start": 1756122176751, "stop": 1756122203528, "duration": 26777}, "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "time": {"start": 1756122176751, "stop": 1756122203281, "duration": 26530}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122203281, "stop": 1756122203528, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "69cd2596b526f71f", "name": "测试总结", "source": "69cd2596b526f71f.txt", "type": "text/plain", "size": 254}, {"uid": "d57f652872f2db66", "name": "test_completed", "source": "d57f652872f2db66.png", "type": "image/png", "size": 148083}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: view recent alarms", "time": {"start": 1756122203528, "stop": 1756122230462, "duration": 26934}, "status": "passed", "steps": [{"name": "执行命令: view recent alarms", "time": {"start": 1756122203528, "stop": 1756122230203, "duration": 26675}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122230203, "stop": 1756122230460, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "df866d9ad6367a42", "name": "测试总结", "source": "df866d9ad6367a42.txt", "type": "text/plain", "size": 224}, {"uid": "f84766fd144ac3ea", "name": "test_completed", "source": "f84766fd144ac3ea.png", "type": "image/png", "size": 142684}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122230462, "stop": 1756122230467, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122230467, "stop": 1756122230711, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "a6142a41a249a54b", "name": "测试总结", "source": "a6142a41a249a54b.txt", "type": "text/plain", "size": 224}, {"uid": "e2f06fd0ae4e13db", "name": "test_completed", "source": "e2f06fd0ae4e13db.png", "type": "image/png", "size": 142684}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "33003c6417adcba3", "name": "stdout", "source": "33003c6417adcba3.txt", "type": "text/plain", "size": 31772}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 11, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122230714, "stop": 1756122230714, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122230717, "stop": 1756122232067, "duration": 1350}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_view_recent_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_view_recent_alarms"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "33805de28a2e7daf.json", "parameterValues": []}