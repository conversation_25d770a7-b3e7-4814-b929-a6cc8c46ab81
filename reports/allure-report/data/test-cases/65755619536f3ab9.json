{"uid": "65755619536f3ab9", "name": "测试please show me where i am能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am.TestEllaPleaseShowMeWhereIAm#test_please_show_me_where_i_am", "historyId": "fbd0b78d6e1c10d8dc70e2bd96aa5bdc", "time": {"start": 1756135431760, "stop": 1756135460312, "duration": 28552}, "description": "please show me where i am", "descriptionHtml": "<p>please show me where i am</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"11:24 Dialogue Explore 11:23 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON><PERSON><PERSON>: Isak Future Irrelevant vs Reds How to use Ask About Screen Family Funds School Bus After Son's Death please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am.TestEllaPleaseShowMeWhereIAm object at 0x0000029205556E10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209668510>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_please_show_me_where_i_am(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"11:24 Dialogue Explore 11:23 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Howe: Isak Future Irrelevant vs Reds How to use Ask About Screen Family Funds School Bus After Son's Death please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_please_show_me_where_i_am.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135418457, "stop": 1756135431757, "duration": 13300}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135431757, "stop": 1756135431758, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "please show me where i am", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"11:24 Dialogue Explore 11:23 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON><PERSON><PERSON>: Isak Future Irrelevant vs Reds How to use Ask About Screen Family Funds School Bus After Son's Death please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am.TestEllaPleaseShowMeWhereIAm object at 0x0000029205556E10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209668510>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_please_show_me_where_i_am(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"11:24 Dialogue Explore 11:23 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Howe: Isak Future Irrelevant vs Reds How to use Ask About Screen Family Funds School Bus After Son's Death please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_please_show_me_where_i_am.py:35: AssertionError", "steps": [{"name": "执行命令: please show me where i am", "time": {"start": 1756135431760, "stop": 1756135460305, "duration": 28545}, "status": "passed", "steps": [{"name": "执行命令: please show me where i am", "time": {"start": 1756135431761, "stop": 1756135460044, "duration": 28283}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135460044, "stop": 1756135460305, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "b3b13df1e3a75ade", "name": "测试总结", "source": "b3b13df1e3a75ade.txt", "type": "text/plain", "size": 638}, {"uid": "c8b9b99066de9f5e", "name": "test_completed", "source": "c8b9b99066de9f5e.png", "type": "image/png", "size": 176044}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135460305, "stop": 1756135460309, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"11:24 Dialogue Explore 11:23 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON><PERSON><PERSON>: Isak Future Irrelevant vs Reds How to use Ask About Screen Family Funds School Bus After Son's Death please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_please_show_me_where_i_am.py\", line 35, in test_please_show_me_where_i_am\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5eb69a9c4c38eaa0", "name": "stdout", "source": "5eb69a9c4c38eaa0.txt", "type": "text/plain", "size": 16140}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135460321, "stop": 1756135460557, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "44f2d3700d30dad6", "name": "失败截图-TestEllaPleaseShowMeWhereIAm", "source": "44f2d3700d30dad6.png", "type": "image/png", "size": 176171}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756135460558, "stop": 1756135462039, "duration": 1481}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_please_show_me_where_i_am"}, {"name": "subSuite", "value": "TestEllaPleaseShowMeWhereIAm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "65755619536f3ab9.json", "parameterValues": []}