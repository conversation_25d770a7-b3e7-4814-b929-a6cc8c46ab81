{"uid": "66095ae85ffb6536", "name": "测试order a burger返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger#test_order_a_burger", "historyId": "2b244b852ff1236f560ec792596ae556", "time": {"start": 1756134894660, "stop": 1756134921339, "duration": 26679}, "description": "验证order a burger指令返回预期的不支持响应", "descriptionHtml": "<p>验证order a burger指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger object at 0x00000292054DC510>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209682610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_order_a_burger(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_order_a_burger.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134882001, "stop": 1756134894660, "duration": 12659}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134894660, "stop": 1756134894660, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证order a burger指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger object at 0x00000292054DC510>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209682610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_order_a_burger(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_order_a_burger.py:33: AssertionError", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1756134894660, "stop": 1756134921335, "duration": 26675}, "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1756134894660, "stop": 1756134921057, "duration": 26397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134921058, "stop": 1756134921335, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "a93c574e92b47528", "name": "测试总结", "source": "a93c574e92b47528.txt", "type": "text/plain", "size": 302}, {"uid": "a880611239772976", "name": "test_completed", "source": "a880611239772976.png", "type": "image/png", "size": 182398}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134921335, "stop": 1756134921337, "duration": 2}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_order_a_burger.py\", line 33, in test_order_a_burger\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eb5cc747713e6d47", "name": "stdout", "source": "eb5cc747713e6d47.txt", "type": "text/plain", "size": 13239}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134921343, "stop": 1756134921588, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "67b781d2c0f72bfa", "name": "失败截图-TestEllaOrderBurger", "source": "67b781d2c0f72bfa.png", "type": "image/png", "size": 182398}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134921589, "stop": 1756134923008, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaOrderBurger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_order_a_burger"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "66095ae85ffb6536.json", "parameterValues": []}