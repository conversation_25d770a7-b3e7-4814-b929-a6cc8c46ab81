{"uid": "6ff6cc43db1c2aaa", "name": "测试increase screen brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_increase_screen_brightness.TestEllaIncreaseScreenBrightness#test_increase_screen_brightness", "historyId": "fcfaafeb2b49ed6f468b8db263c64a18", "time": {"start": 1756124587554, "stop": 1756124614670, "duration": 27116}, "description": "increase screen brightness", "descriptionHtml": "<p>increase screen brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124574556, "stop": 1756124587551, "duration": 12995}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124587551, "stop": 1756124587551, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "increase screen brightness", "status": "passed", "steps": [{"name": "执行命令: increase screen brightness", "time": {"start": 1756124587554, "stop": 1756124614390, "duration": 26836}, "status": "passed", "steps": [{"name": "执行命令: increase screen brightness", "time": {"start": 1756124587554, "stop": 1756124614128, "duration": 26574}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124614128, "stop": 1756124614387, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "ff2bc0e25d0cbef", "name": "测试总结", "source": "ff2bc0e25d0cbef.txt", "type": "text/plain", "size": 219}, {"uid": "fcd5bb78adbf2658", "name": "test_completed", "source": "fcd5bb78adbf2658.png", "type": "image/png", "size": 152064}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124614390, "stop": 1756124614398, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124614398, "stop": 1756124614398, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124614398, "stop": 1756124614669, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "d19d9091ab4700d9", "name": "测试总结", "source": "d19d9091ab4700d9.txt", "type": "text/plain", "size": 219}, {"uid": "fdd550cd81bbf8db", "name": "test_completed", "source": "fdd550cd81bbf8db.png", "type": "image/png", "size": 152064}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bda1f09911eeb94e", "name": "stdout", "source": "bda1f09911eeb94e.txt", "type": "text/plain", "size": 13219}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124614671, "stop": 1756124614671, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124614680, "stop": 1756124616044, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_screen_brightness"}, {"name": "subSuite", "value": "TestEllaIncreaseScreenBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_screen_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6ff6cc43db1c2aaa.json", "parameterValues": []}