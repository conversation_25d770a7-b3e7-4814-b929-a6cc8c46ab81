{"uid": "78c0fee795bbbdb4", "name": "测试countdown 5 min能正常执行", "fullName": "testcases.test_ella.system_coupling.test_countdown_min.TestEllaCountdownMin#test_countdown_min", "historyId": "ffd7dc86cbeda13ca78bbca09f06422a", "time": {"start": 1756124102625, "stop": 1756124163691, "duration": 61066}, "description": "countdown 5 min", "descriptionHtml": "<p>countdown 5 min</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124089707, "stop": 1756124102623, "duration": 12916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124102623, "stop": 1756124102623, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "countdown 5 min", "status": "passed", "steps": [{"name": "执行命令:  countdown 5 min", "time": {"start": 1756124102625, "stop": 1756124136475, "duration": 33850}, "status": "passed", "steps": [{"name": "执行命令: countdown 5 min", "time": {"start": 1756124102625, "stop": 1756124136205, "duration": 33580}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124136205, "stop": 1756124136474, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "5c0ef6eaacadeffa", "name": "测试总结", "source": "5c0ef6eaacadeffa.txt", "type": "text/plain", "size": 268}, {"uid": "3a6504e871f1496a", "name": "test_completed", "source": "3a6504e871f1496a.png", "type": "image/png", "size": 174819}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: stop the countdown", "time": {"start": 1756124136475, "stop": 1756124163459, "duration": 26984}, "status": "passed", "steps": [{"name": "执行命令: stop the countdown", "time": {"start": 1756124136475, "stop": 1756124163166, "duration": 26691}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124163166, "stop": 1756124163459, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "295efd5389884b80", "name": "测试总结", "source": "295efd5389884b80.txt", "type": "text/plain", "size": 292}, {"uid": "a73ffb366849bb7e", "name": "test_completed", "source": "a73ffb366849bb7e.png", "type": "image/png", "size": 181255}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124163459, "stop": 1756124163461, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124163461, "stop": 1756124163690, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "5530edd525c6826e", "name": "测试总结", "source": "5530edd525c6826e.txt", "type": "text/plain", "size": 289}, {"uid": "60ade090e3ac63c7", "name": "test_completed", "source": "60ade090e3ac63c7.png", "type": "image/png", "size": 181133}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6e56d340ac1e446c", "name": "stdout", "source": "6e56d340ac1e446c.txt", "type": "text/plain", "size": 22581}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 8, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124163692, "stop": 1756124163692, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124163693, "stop": 1756124165082, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_countdown_min"}, {"name": "subSuite", "value": "TestEllaCountdownMin"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_countdown_min"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "78c0fee795bbbdb4.json", "parameterValues": []}