{"uid": "ec7ee27e94b34f2a", "name": "测试enable all ai magic box features返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features.TestEllaEnableAllAiMagicBoxFeatures#test_enable_all_ai_magic_box_features", "historyId": "********************************", "time": {"start": 1756131463970, "stop": 1756131489736, "duration": 25766}, "description": "验证enable all ai magic box features指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable all ai magic box features指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131451418, "stop": 1756131463969, "duration": 12551}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131463969, "stop": 1756131463969, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable all ai magic box features指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "time": {"start": 1756131463970, "stop": 1756131489500, "duration": 25530}, "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "time": {"start": 1756131463970, "stop": 1756131489237, "duration": 25267}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131489237, "stop": 1756131489499, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "b4cd14459012926a", "name": "测试总结", "source": "b4cd14459012926a.txt", "type": "text/plain", "size": 359}, {"uid": "d6e5666b6cc68f94", "name": "test_completed", "source": "d6e5666b6cc68f94.png", "type": "image/png", "size": 191652}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131489500, "stop": 1756131489502, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131489502, "stop": 1756131489736, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "46761009fb29d246", "name": "测试总结", "source": "46761009fb29d246.txt", "type": "text/plain", "size": 359}, {"uid": "570204d166eb8814", "name": "test_completed", "source": "570204d166eb8814.png", "type": "image/png", "size": 191660}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c4d3318a86e1585e", "name": "stdout", "source": "c4d3318a86e1585e.txt", "type": "text/plain", "size": 12713}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131489737, "stop": 1756131489737, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131489738, "stop": 1756131491105, "duration": 1367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaEnableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ec7ee27e94b34f2a.json", "parameterValues": []}