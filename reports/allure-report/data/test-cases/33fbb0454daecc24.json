{"uid": "33fbb0454daecc24", "name": "测试navigation to the first address in the image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage#test_navigation_to_the_first_address_in_the_image", "historyId": "9c84b087eb7d9fde94ed5bb5370b275b", "time": {"start": 1756134415584, "stop": 1756134448086, "duration": 32502}, "description": "navigation to the first address in the image", "descriptionHtml": "<p>navigation to the first address in the image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134401901, "stop": 1756134415581, "duration": 13680}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134415582, "stop": 1756134415582, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigation to the first address in the image", "status": "passed", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1756134415584, "stop": 1756134447840, "duration": 32256}, "status": "passed", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1756134415584, "stop": 1756134447553, "duration": 31969}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134447553, "stop": 1756134447839, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "4b5b9e88951859ed", "name": "测试总结", "source": "4b5b9e88951859ed.txt", "type": "text/plain", "size": 758}, {"uid": "3792f95658c71ffe", "name": "test_completed", "source": "3792f95658c71ffe.png", "type": "image/png", "size": 184272}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134447840, "stop": 1756134447842, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134447842, "stop": 1756134448084, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "305957110539e640", "name": "测试总结", "source": "305957110539e640.txt", "type": "text/plain", "size": 758}, {"uid": "69f169eb10fadff8", "name": "test_completed", "source": "69f169eb10fadff8.png", "type": "image/png", "size": 184272}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4dc6f5e3d2aae172", "name": "stdout", "source": "4dc6f5e3d2aae172.txt", "type": "text/plain", "size": 15291}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134448087, "stop": 1756134448087, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134448089, "stop": 1756134449544, "duration": 1455}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_first_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationFirstAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "33fbb0454daecc24.json", "parameterValues": []}