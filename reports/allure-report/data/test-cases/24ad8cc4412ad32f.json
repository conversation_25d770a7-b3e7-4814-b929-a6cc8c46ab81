{"uid": "24ad8cc4412ad32f", "name": "测试play music by yandex music", "fullName": "testcases.test_ella.dialogue.test_play_music_by_yandex_music.TestEllaOpenPlayPoliticalNews#test_play_music_by_yandex_music", "historyId": "6c59bb8b6ba250c58da738ab8237ed3c", "time": {"start": 1756120931306, "stop": 1756120958815, "duration": 27509}, "description": "测试play music by yandex music指令", "descriptionHtml": "<p>测试play music by yandex music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120918671, "stop": 1756120931305, "duration": 12634}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120931306, "stop": 1756120931306, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music by yandex music指令", "status": "passed", "steps": [{"name": "执行命令: play music by yandex music", "time": {"start": 1756120931306, "stop": 1756120958590, "duration": 27284}, "status": "passed", "steps": [{"name": "执行命令: play music by yandex music", "time": {"start": 1756120931306, "stop": 1756120958337, "duration": 27031}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120958337, "stop": 1756120958590, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "76d186af9045302", "name": "测试总结", "source": "76d186af9045302.txt", "type": "text/plain", "size": 345}, {"uid": "91ac14753e95f83c", "name": "test_completed", "source": "91ac14753e95f83c.png", "type": "image/png", "size": 193735}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120958590, "stop": 1756120958591, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120958591, "stop": 1756120958815, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "a80b8187016516d1", "name": "测试总结", "source": "a80b8187016516d1.txt", "type": "text/plain", "size": 345}, {"uid": "4a434c6cee7ca307", "name": "test_completed", "source": "4a434c6cee7ca307.png", "type": "image/png", "size": 193762}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1d3919116480a14a", "name": "stdout", "source": "1d3919116480a14a.txt", "type": "text/plain", "size": 13283}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120958816, "stop": 1756120958816, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120958816, "stop": 1756120960141, "duration": 1325}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_yandex_music"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_yandex_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "24ad8cc4412ad32f.json", "parameterValues": []}