{"uid": "8f1b30b7b8a6ef", "name": "测试set edge mistouch prevention返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention.TestEllaSetEdgeMistouchPrevention#test_set_edge_mistouch_prevention", "historyId": "2b2dcc407b5c428f968f62d94fe8025c", "time": {"start": 1756136392342, "stop": 1756136418518, "duration": 26176}, "description": "验证set edge mistouch prevention指令返回预期的不支持响应", "descriptionHtml": "<p>验证set edge mistouch prevention指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136379207, "stop": 1756136392340, "duration": 13133}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136392340, "stop": 1756136392340, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set edge mistouch prevention指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set edge mistouch prevention", "time": {"start": 1756136392342, "stop": 1756136418295, "duration": 25953}, "status": "passed", "steps": [{"name": "执行命令: set edge mistouch prevention", "time": {"start": 1756136392342, "stop": 1756136418045, "duration": 25703}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136418045, "stop": 1756136418295, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "50a353d2f7072266", "name": "测试总结", "source": "50a353d2f7072266.txt", "type": "text/plain", "size": 363}, {"uid": "2fbcdfc1072d5e86", "name": "test_completed", "source": "2fbcdfc1072d5e86.png", "type": "image/png", "size": 187627}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136418295, "stop": 1756136418298, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136418298, "stop": 1756136418517, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "538c78e366b20d88", "name": "测试总结", "source": "538c78e366b20d88.txt", "type": "text/plain", "size": 363}, {"uid": "d03cd9256ed02825", "name": "test_completed", "source": "d03cd9256ed02825.png", "type": "image/png", "size": 187627}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "37e2aad620ab9919", "name": "stdout", "source": "37e2aad620ab9919.txt", "type": "text/plain", "size": 12709}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136418521, "stop": 1756136418521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136418523, "stop": 1756136419982, "duration": 1459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_edge_mistouch_prevention"}, {"name": "subSuite", "value": "TestEllaSetEdgeMistouchPrevention"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8f1b30b7b8a6ef.json", "parameterValues": []}