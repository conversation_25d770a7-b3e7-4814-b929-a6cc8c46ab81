{"uid": "3217184a2d1784f4", "name": "测试measure blood oxygen", "fullName": "testcases.test_ella.dialogue.test_measure_blood_oxygen.TestEllaOpenPlayPoliticalNews#test_measure_blood_oxygen", "historyId": "7ec382c77bede0ad015817770cd9e1eb", "time": {"start": 1756120549891, "stop": 1756120575559, "duration": 25668}, "description": "测试measure blood oxygen指令", "descriptionHtml": "<p>测试measure blood oxygen指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120536775, "stop": 1756120549889, "duration": 13114}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120549889, "stop": 1756120549889, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试measure blood oxygen指令", "status": "passed", "steps": [{"name": "执行命令: measure blood oxygen", "time": {"start": 1756120549891, "stop": 1756120575317, "duration": 25426}, "status": "passed", "steps": [{"name": "执行命令: measure blood oxygen", "time": {"start": 1756120549891, "stop": 1756120575082, "duration": 25191}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120575082, "stop": 1756120575316, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "9148e031f7976f05", "name": "测试总结", "source": "9148e031f7976f05.txt", "type": "text/plain", "size": 298}, {"uid": "a67f2851a1398daa", "name": "test_completed", "source": "a67f2851a1398daa.png", "type": "image/png", "size": 179374}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120575317, "stop": 1756120575320, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120575320, "stop": 1756120575559, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "9d0972a0f853d324", "name": "测试总结", "source": "9d0972a0f853d324.txt", "type": "text/plain", "size": 298}, {"uid": "73d3434a17d68cd7", "name": "test_completed", "source": "73d3434a17d68cd7.png", "type": "image/png", "size": 179374}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d4097162a35b4f7c", "name": "stdout", "source": "d4097162a35b4f7c.txt", "type": "text/plain", "size": 12633}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120575560, "stop": 1756120575560, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120575566, "stop": 1756120576938, "duration": 1372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_measure_blood_oxygen"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_measure_blood_oxygen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3217184a2d1784f4.json", "parameterValues": []}