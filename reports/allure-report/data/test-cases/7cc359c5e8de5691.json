{"uid": "7cc359c5e8de5691", "name": "测试pause song能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_song.TestEllaPauseSong#test_pause_song", "historyId": "f09a8375806e200073a99f1cbabdc35c", "time": {"start": 1756118142365, "stop": 1756118168282, "duration": 25917}, "description": "pause song", "descriptionHtml": "<p>pause song</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118129196, "stop": 1756118142363, "duration": 13167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118142364, "stop": 1756118142364, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "pause song", "status": "passed", "steps": [{"name": "执行命令: pause song", "time": {"start": 1756118142365, "stop": 1756118168055, "duration": 25690}, "status": "passed", "steps": [{"name": "执行命令: pause song", "time": {"start": 1756118142365, "stop": 1756118167817, "duration": 25452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118167817, "stop": 1756118168054, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "8e35f287a33be3e", "name": "测试总结", "source": "8e35f287a33be3e.txt", "type": "text/plain", "size": 286}, {"uid": "c5c3b9866eabce2d", "name": "test_completed", "source": "c5c3b9866eabce2d.png", "type": "image/png", "size": 173303}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118168055, "stop": 1756118168057, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118168057, "stop": 1756118168281, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "67431b02c8befb5d", "name": "测试总结", "source": "67431b02c8befb5d.txt", "type": "text/plain", "size": 286}, {"uid": "8eca533ca696c745", "name": "test_completed", "source": "8eca533ca696c745.png", "type": "image/png", "size": 173303}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b323ce5557f0cec4", "name": "stdout", "source": "b323ce5557f0cec4.txt", "type": "text/plain", "size": 12421}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118168282, "stop": 1756118168282, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118168283, "stop": 1756118169664, "duration": 1381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_song"}, {"name": "subSuite", "value": "TestEllaPauseSong"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_song"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7cc359c5e8de5691.json", "parameterValues": []}