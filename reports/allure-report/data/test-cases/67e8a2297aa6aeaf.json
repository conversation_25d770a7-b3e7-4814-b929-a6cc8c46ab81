{"uid": "67e8a2297aa6aeaf", "name": "测试open font family settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings#test_open_font_family_settings", "historyId": "fd79b0d35f1f4639521f70b269d3aadc", "time": {"start": 1756134599016, "stop": 1756134633179, "duration": 34163}, "description": "验证open font family settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证open font family settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open font family settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings object at 0x000002920548F110>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920963B0D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_open_font_family_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['open font family settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_font_family_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134585766, "stop": 1756134599015, "duration": 13249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134599015, "stop": 1756134599015, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证open font family settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open font family settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings object at 0x000002920548F110>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920963B0D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_open_font_family_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['open font family settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_font_family_settings.py:33: AssertionError", "steps": [{"name": "执行命令: open font family settings", "time": {"start": 1756134599016, "stop": 1756134633170, "duration": 34154}, "status": "passed", "steps": [{"name": "执行命令: open font family settings", "time": {"start": 1756134599017, "stop": 1756134632835, "duration": 33818}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134632835, "stop": 1756134633170, "duration": 335}, "status": "passed", "steps": [], "attachments": [{"uid": "b6e488e3db4addde", "name": "测试总结", "source": "b6e488e3db4addde.txt", "type": "text/plain", "size": 575}, {"uid": "fc23f091bc5d81ee", "name": "test_completed", "source": "fc23f091bc5d81ee.png", "type": "image/png", "size": 175472}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134633170, "stop": 1756134633176, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['open font family settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_open_font_family_settings.py\", line 33, in test_open_font_family_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f15d2bb9346e5a20", "name": "stdout", "source": "f15d2bb9346e5a20.txt", "type": "text/plain", "size": 16707}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134633184, "stop": 1756134633424, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "7066f88a78659275", "name": "失败截图-TestEllaOpenSettings", "source": "7066f88a78659275.png", "type": "image/png", "size": 175472}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134633427, "stop": 1756134634894, "duration": 1467}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_font_family_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_font_family_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "67e8a2297aa6aeaf.json", "parameterValues": []}