{"uid": "752e0f18acb4bdb", "name": "测试open maps", "fullName": "testcases.test_ella.unsupported_commands.test_open_maps.TestEllaOpenPlayPoliticalNews#test_open_maps", "historyId": "2b4b7555520a6b757239820871731d81", "time": {"start": 1756134647609, "stop": 1756134682197, "duration": 34588}, "description": "测试open maps指令", "descriptionHtml": "<p>测试open maps指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134634906, "stop": 1756134647587, "duration": 12681}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134647587, "stop": 1756134647587, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open maps指令", "status": "passed", "steps": [{"name": "执行命令: open maps", "time": {"start": 1756134647609, "stop": 1756134681948, "duration": 34339}, "status": "passed", "steps": [{"name": "执行命令: open maps", "time": {"start": 1756134647609, "stop": 1756134681681, "duration": 34072}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134681681, "stop": 1756134681947, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "54450b213ea04cf4", "name": "测试总结", "source": "54450b213ea04cf4.txt", "type": "text/plain", "size": 400}, {"uid": "e734060582a578cc", "name": "test_completed", "source": "e734060582a578cc.png", "type": "image/png", "size": 157830}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134681948, "stop": 1756134681950, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证google_maps已打开", "time": {"start": 1756134681950, "stop": 1756134681950, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134681950, "stop": 1756134682197, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "8b538bae81063118", "name": "测试总结", "source": "8b538bae81063118.txt", "type": "text/plain", "size": 400}, {"uid": "45eb3d48dc717a6e", "name": "test_completed", "source": "45eb3d48dc717a6e.png", "type": "image/png", "size": 157830}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "68be780bfb499a03", "name": "stdout", "source": "68be780bfb499a03.txt", "type": "text/plain", "size": 15373}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134682198, "stop": 1756134682198, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134682199, "stop": 1756134683693, "duration": 1494}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_maps"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_maps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "752e0f18acb4bdb.json", "parameterValues": []}