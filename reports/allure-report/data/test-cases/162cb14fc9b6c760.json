{"uid": "162cb14fc9b6c760", "name": "测试play football video by youtube", "fullName": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube.TestEllaOpenPlayPoliticalNews#test_play_football_video_by_youtube", "historyId": "ed94d8d97b63a623a1f6438b57774ff1", "time": {"start": 1756135062684, "stop": 1756135095251, "duration": 32567}, "description": "测试play football video by youtube指令", "descriptionHtml": "<p>测试play football video by youtube指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135049649, "stop": 1756135062683, "duration": 13034}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135062683, "stop": 1756135062683, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play football video by youtube指令", "status": "passed", "steps": [{"name": "执行命令: play football video by youtube", "time": {"start": 1756135062684, "stop": 1756135094945, "duration": 32261}, "status": "passed", "steps": [{"name": "执行命令: play football video by youtube", "time": {"start": 1756135062684, "stop": 1756135094667, "duration": 31983}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135094667, "stop": 1756135094945, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "62786ef97a9923b0", "name": "测试总结", "source": "62786ef97a9923b0.txt", "type": "text/plain", "size": 373}, {"uid": "24ee60e91507247c", "name": "test_completed", "source": "24ee60e91507247c.png", "type": "image/png", "size": 168497}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135094945, "stop": 1756135094947, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证youtube已打开", "time": {"start": 1756135094947, "stop": 1756135094947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135094947, "stop": 1756135095251, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "398ed11e989a36cc", "name": "测试总结", "source": "398ed11e989a36cc.txt", "type": "text/plain", "size": 373}, {"uid": "21c2807e962c5858", "name": "test_completed", "source": "21c2807e962c5858.png", "type": "image/png", "size": 168513}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "17a76c81e36d6aa2", "name": "stdout", "source": "17a76c81e36d6aa2.txt", "type": "text/plain", "size": 15426}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135095252, "stop": 1756135095252, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135095253, "stop": 1756135096764, "duration": 1511}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_football_video_by_youtube"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "162cb14fc9b6c760.json", "parameterValues": []}