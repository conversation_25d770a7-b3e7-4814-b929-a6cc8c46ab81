{"uid": "c8c8e57e05b36319", "name": "测试open whatsapp", "fullName": "testcases.test_ella.unsupported_commands.test_open_whatsapp.TestEllaOpenWhatsapp#test_open_whatsapp", "historyId": "51b4b46de04a8c1e37077a9f688cb490", "time": {"start": 1756134852295, "stop": 1756134880277, "duration": 27982}, "description": "测试open whatsapp指令", "descriptionHtml": "<p>测试open whatsapp指令</p>\n", "status": "failed", "statusMessage": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_whatsapp.TestEllaOpenWhatsapp object at 0x00000292054C7F50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097D1550>\n\n    @allure.title(\"测试open whatsapp\")\n    @allure.description(\"测试open whatsapp指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_whatsapp(self, ella_app):\n        \"\"\"测试open whatsapp命令\"\"\"\n        command = \"open whatsapp\"\n        # expected_text = ['Done']\n        expected_text = ['WhatsApp is not installed yet'\n                         'I need to download whatsapp',\n                         'I need to download WhatsApp']\n        app_name = 'whatsapp'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_whatsapp.py:38: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134839522, "stop": 1756134852294, "duration": 12772}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134852294, "stop": 1756134852294, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open whatsapp指令", "status": "failed", "statusMessage": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_open_whatsapp.TestEllaOpenWhatsapp object at 0x00000292054C7F50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097D1550>\n\n    @allure.title(\"测试open whatsapp\")\n    @allure.description(\"测试open whatsapp指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_whatsapp(self, ella_app):\n        \"\"\"测试open whatsapp命令\"\"\"\n        command = \"open whatsapp\"\n        # expected_text = ['Done']\n        expected_text = ['WhatsApp is not installed yet'\n                         'I need to download whatsapp',\n                         'I need to download WhatsApp']\n        app_name = 'whatsapp'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_whatsapp.py:38: AssertionError", "steps": [{"name": "执行命令: open whatsapp", "time": {"start": 1756134852295, "stop": 1756134880275, "duration": 27980}, "status": "passed", "steps": [{"name": "执行命令: open whatsapp", "time": {"start": 1756134852295, "stop": 1756134879995, "duration": 27700}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134879995, "stop": 1756134880273, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "d787f3c39e1c4272", "name": "测试总结", "source": "d787f3c39e1c4272.txt", "type": "text/plain", "size": 309}, {"uid": "90df1f77368a8a78", "name": "test_completed", "source": "90df1f77368a8a78.png", "type": "image/png", "size": 181955}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134880275, "stop": 1756134880276, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证whatsapp已打开", "time": {"start": 1756134880276, "stop": 1756134880276, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_open_whatsapp.py\", line 38, in test_open_whatsapp\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "23eca6fc781fa825", "name": "stdout", "source": "23eca6fc781fa825.txt", "type": "text/plain", "size": 13462}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134880280, "stop": 1756134880535, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "f99a676b5894a192", "name": "失败截图-TestEllaOpenWhatsapp", "source": "f99a676b5894a192.png", "type": "image/png", "size": 182258}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134880536, "stop": 1756134881997, "duration": 1461}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "c8c8e57e05b36319.json", "parameterValues": []}