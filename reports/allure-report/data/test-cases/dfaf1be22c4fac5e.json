{"uid": "dfaf1be22c4fac5e", "name": "测试enable touch optimization返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization.TestEllaEnableTouchOptimization#test_enable_touch_optimization", "historyId": "fc75b92fb4a100575b2c948dd6c5a008", "time": {"start": 1756131795868, "stop": 1756131821643, "duration": 25775}, "description": "验证enable touch optimization指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable touch optimization指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131783023, "stop": 1756131795825, "duration": 12802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131795825, "stop": 1756131795826, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable touch optimization指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "time": {"start": 1756131795868, "stop": 1756131821400, "duration": 25532}, "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "time": {"start": 1756131795868, "stop": 1756131821135, "duration": 25267}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131821135, "stop": 1756131821400, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "c620b27009b59216", "name": "测试总结", "source": "c620b27009b59216.txt", "type": "text/plain", "size": 345}, {"uid": "a32d3b0a97f6254e", "name": "test_completed", "source": "a32d3b0a97f6254e.png", "type": "image/png", "size": 191054}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131821400, "stop": 1756131821403, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131821403, "stop": 1756131821643, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "1ab0abf37cc67abf", "name": "测试总结", "source": "1ab0abf37cc67abf.txt", "type": "text/plain", "size": 345}, {"uid": "1da4f596334f50a1", "name": "test_completed", "source": "1da4f596334f50a1.png", "type": "image/png", "size": 191054}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f7195c75c93efa7e", "name": "stdout", "source": "f7195c75c93efa7e.txt", "type": "text/plain", "size": 12642}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131821644, "stop": 1756131821644, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131821645, "stop": 1756131823056, "duration": 1411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaEnableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dfaf1be22c4fac5e.json", "parameterValues": []}