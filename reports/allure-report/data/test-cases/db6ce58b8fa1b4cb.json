{"uid": "db6ce58b8fa1b4cb", "name": "测试play music by Audiomack", "fullName": "testcases.test_ella.unsupported_commands.test_play_music_by_Audiomack.TestEllaOpenPlayPoliticalNews#test_play_music_by_Audiomack", "historyId": "e9039096aff36ba6e4fae58e40eb8539", "time": {"start": 1756135174020, "stop": 1756135203007, "duration": 28987}, "description": "测试play music by Audiomack指令", "descriptionHtml": "<p>测试play music by Audiomack指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135161313, "stop": 1756135174018, "duration": 12705}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135174018, "stop": 1756135174018, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music by Audiomack指令", "status": "passed", "steps": [{"name": "执行命令: play music by Audiomack", "time": {"start": 1756135174020, "stop": 1756135202804, "duration": 28784}, "status": "passed", "steps": [{"name": "执行命令: play music by Audiomack", "time": {"start": 1756135174020, "stop": 1756135202552, "duration": 28532}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135202552, "stop": 1756135202802, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "3a79d0917c094782", "name": "测试总结", "source": "3a79d0917c094782.txt", "type": "text/plain", "size": 318}, {"uid": "b6fff547ee9cbedf", "name": "test_completed", "source": "b6fff547ee9cbedf.png", "type": "image/png", "size": 189638}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135202804, "stop": 1756135202805, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135202805, "stop": 1756135203005, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "ec9dc973bcfe9660", "name": "测试总结", "source": "ec9dc973bcfe9660.txt", "type": "text/plain", "size": 318}, {"uid": "b74979c1eb611e0f", "name": "test_completed", "source": "b74979c1eb611e0f.png", "type": "image/png", "size": 189276}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e35cb0dc6648ff3a", "name": "stdout", "source": "e35cb0dc6648ff3a.txt", "type": "text/plain", "size": 13350}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135203008, "stop": 1756135203008, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135203009, "stop": 1756135204479, "duration": 1470}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_music_by_Audiomack"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_music_by_Audiomack"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "db6ce58b8fa1b4cb.json", "parameterValues": []}