{"uid": "a6245e811e1f2cf4", "name": "测试what is apec?能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_is_apec.TestEllaWhatIsApec#test_what_is_apec", "historyId": "0c4bd81bf0dbac094265e3ac47550bbd", "time": {"start": 1756122245329, "stop": 1756122273542, "duration": 28213}, "description": "what is apec?", "descriptionHtml": "<p>what is apec?</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122232078, "stop": 1756122245326, "duration": 13248}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122245326, "stop": 1756122245327, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what is apec?", "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1756122245329, "stop": 1756122273290, "duration": 27961}, "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1756122245329, "stop": 1756122273017, "duration": 27688}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122273017, "stop": 1756122273289, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "289edc0c225b3dc4", "name": "测试总结", "source": "289edc0c225b3dc4.txt", "type": "text/plain", "size": 1173}, {"uid": "42d06458cc466098", "name": "test_completed", "source": "42d06458cc466098.png", "type": "image/png", "size": 227252}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122273290, "stop": 1756122273294, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122273294, "stop": 1756122273540, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "89705b1ea0299b40", "name": "测试总结", "source": "89705b1ea0299b40.txt", "type": "text/plain", "size": 1173}, {"uid": "4052a97f3a86f859", "name": "test_completed", "source": "4052a97f3a86f859.png", "type": "image/png", "size": 227528}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cc6679ed38d717d3", "name": "stdout", "source": "cc6679ed38d717d3.txt", "type": "text/plain", "size": 16747}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122273545, "stop": 1756122273545, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122273548, "stop": 1756122274946, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_is_apec"}, {"name": "subSuite", "value": "TestEllaWhatIsApec"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_is_apec"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a6245e811e1f2cf4.json", "parameterValues": []}