{"uid": "6069adf1c382d1b1", "name": "测试more settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_more_settings.TestEllaMoreSettings#test_more_settings", "historyId": "6d49aaf4a5e11b32b961aa0aa3dbbf6e", "time": {"start": 1756134279026, "stop": 1756134306618, "duration": 27592}, "description": "验证more settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证more settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134266279, "stop": 1756134279025, "duration": 12746}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134279025, "stop": 1756134279025, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证more settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: more settings", "time": {"start": 1756134279026, "stop": 1756134306343, "duration": 27317}, "status": "passed", "steps": [{"name": "执行命令: more settings", "time": {"start": 1756134279026, "stop": 1756134306038, "duration": 27012}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134306038, "stop": 1756134306343, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "77f6d2ac8d53898d", "name": "测试总结", "source": "77f6d2ac8d53898d.txt", "type": "text/plain", "size": 695}, {"uid": "eab5a5c544a2c74b", "name": "test_completed", "source": "eab5a5c544a2c74b.png", "type": "image/png", "size": 181230}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134306343, "stop": 1756134306362, "duration": 19}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134306362, "stop": 1756134306617, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "32b89f93c4dfae72", "name": "测试总结", "source": "32b89f93c4dfae72.txt", "type": "text/plain", "size": 695}, {"uid": "f71c323b13b16143", "name": "test_completed", "source": "f71c323b13b16143.png", "type": "image/png", "size": 180905}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "821b890480d92425", "name": "stdout", "source": "821b890480d92425.txt", "type": "text/plain", "size": 15095}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134306619, "stop": 1756134306619, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134306620, "stop": 1756134308044, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_more_settings"}, {"name": "subSuite", "value": "TestEllaMoreSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_more_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6069adf1c382d1b1.json", "parameterValues": []}