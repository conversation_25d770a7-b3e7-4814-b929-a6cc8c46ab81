{"uid": "6756eadf65ce027a", "name": "测试turn down alarm clock volume", "fullName": "testcases.test_ella.system_coupling.test_turn_down_alarm_clock_volume.TestEllaOpenClock#test_turn_down_alarm_clock_volume", "historyId": "57c5370b1a13de534ed16f0ce2ee85b9", "time": {"start": 1756127187289, "stop": 1756127214301, "duration": 27012}, "description": "测试turn down alarm clock volume指令", "descriptionHtml": "<p>测试turn down alarm clock volume指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127174391, "stop": 1756127187288, "duration": 12897}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127187288, "stop": 1756127187288, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn down alarm clock volume指令", "status": "passed", "steps": [{"name": "执行命令: turn down alarm clock volume", "time": {"start": 1756127187289, "stop": 1756127214034, "duration": 26745}, "status": "passed", "steps": [{"name": "执行命令: turn down alarm clock volume", "time": {"start": 1756127187289, "stop": 1756127213763, "duration": 26474}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127213763, "stop": 1756127214034, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "6b99edfd2ac9aa7e", "name": "测试总结", "source": "6b99edfd2ac9aa7e.txt", "type": "text/plain", "size": 315}, {"uid": "317d1247b4b4214f", "name": "test_completed", "source": "317d1247b4b4214f.png", "type": "image/png", "size": 184297}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127214034, "stop": 1756127214035, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127214035, "stop": 1756127214300, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "f56f79b2f59f3f93", "name": "测试总结", "source": "f56f79b2f59f3f93.txt", "type": "text/plain", "size": 315}, {"uid": "fd58acbff0d39235", "name": "test_completed", "source": "fd58acbff0d39235.png", "type": "image/png", "size": 184297}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "838642fb1056e80", "name": "stdout", "source": "838642fb1056e80.txt", "type": "text/plain", "size": 13749}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127214302, "stop": 1756127214302, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127214304, "stop": 1756127215643, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_alarm_clock_volume"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_alarm_clock_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6756eadf65ce027a.json", "parameterValues": []}