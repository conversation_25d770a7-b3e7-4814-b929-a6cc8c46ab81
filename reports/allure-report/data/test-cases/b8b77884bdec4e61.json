{"uid": "b8b77884bdec4e61", "name": "测试take notes能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_take_notes.TestEllaTakeNotes#test_take_notes", "historyId": "fcda536a017e05b2edb24a2e80ce1ec0", "time": {"start": 1756138193665, "stop": 1756138223059, "duration": 29394}, "description": "take notes", "descriptionHtml": "<p>take notes</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['What do you want it to say?', 'What would you like to note down']，实际响应: '['take notes', 'What would you like to note down?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_take_notes.TestEllaTakeNotes object at 0x0000029205899610>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A9F2410>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_notes(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['What do you want it to say?', 'What would you like to note down']，实际响应: '['take notes', 'What would you like to note down?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_take_notes.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138180701, "stop": 1756138193663, "duration": 12962}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138193663, "stop": 1756138193663, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take notes", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['What do you want it to say?', 'What would you like to note down']，实际响应: '['take notes', 'What would you like to note down?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_take_notes.TestEllaTakeNotes object at 0x0000029205899610>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A9F2410>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_notes(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['What do you want it to say?', 'What would you like to note down']，实际响应: '['take notes', 'What would you like to note down?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_take_notes.py:33: AssertionError", "steps": [{"name": "执行命令: take notes", "time": {"start": 1756138193665, "stop": 1756138223055, "duration": 29390}, "status": "passed", "steps": [{"name": "执行命令: take notes", "time": {"start": 1756138193665, "stop": 1756138222792, "duration": 29127}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138222792, "stop": 1756138223055, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "4719d4b7c8d2d55b", "name": "测试总结", "source": "4719d4b7c8d2d55b.txt", "type": "text/plain", "size": 286}, {"uid": "e4de876042ff9968", "name": "test_completed", "source": "e4de876042ff9968.png", "type": "image/png", "size": 171110}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138223055, "stop": 1756138223058, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['What do you want it to say?', 'What would you like to note down']，实际响应: '['take notes', 'What would you like to note down?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_take_notes.py\", line 33, in test_take_notes\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a19ded62c41f2ef5", "name": "stdout", "source": "a19ded62c41f2ef5.txt", "type": "text/plain", "size": 13776}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138223063, "stop": 1756138223293, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "eb6ce69071339a74", "name": "失败截图-TestEllaTakeNotes", "source": "eb6ce69071339a74.png", "type": "image/png", "size": 171631}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756138223294, "stop": 1756138224719, "duration": 1425}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_take_notes"}, {"name": "subSuite", "value": "TestEllaTakeNotes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_take_notes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b8b77884bdec4e61.json", "parameterValues": []}