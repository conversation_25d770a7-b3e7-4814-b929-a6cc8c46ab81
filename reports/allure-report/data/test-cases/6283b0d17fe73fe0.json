{"uid": "6283b0d17fe73fe0", "name": "测试power off my phone能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_power_off_my_phone.TestEllaPowerOffMyPhone#test_power_off_my_phone", "historyId": "0e1d64e0daaaf11983cdc488ea4b0993", "time": {"start": 1756135504309, "stop": 1756135504309, "duration": 0}, "description": "power off my phone", "descriptionHtml": "<p>power off my phone</p>\n", "status": "skipped", "statusMessage": "Skipped: power off 会导致设备断开，先跳过", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\unsupported_commands\\\\test_power_off_my_phone.py', 16, 'Skipped: power off 会导致设备断开，先跳过')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135517544, "stop": 1756135517544, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135552395, "stop": 1756135552653, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "61e2533137a6f88c", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "61e2533137a6f88c.png", "type": "image/png", "size": 169386}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_power_off_my_phone"}, {"name": "subSuite", "value": "TestEllaPowerOffMyPhone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_power_off_my_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}, "source": "6283b0d17fe73fe0.json", "parameterValues": []}