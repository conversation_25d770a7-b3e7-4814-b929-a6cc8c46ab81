{"uid": "fa916ba066ad7774", "name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_send_my_recent_photos_to_mom_through_whatsapp.TestEllaSendMyRecentPhotosMomThroughWhatsapp#test_send_my_recent_photos_to_mom_through_whatsapp", "historyId": "80dab16fde357aadc4387b5d440ed276", "time": {"start": 1756135948413, "stop": 1756135974910, "duration": 26497}, "description": "验证send my recent photos to mom through whatsapp指令返回预期的不支持响应", "descriptionHtml": "<p>验证send my recent photos to mom through whatsapp指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135935738, "stop": 1756135948411, "duration": 12673}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135948411, "stop": 1756135948412, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证send my recent photos to mom through whatsapp指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "time": {"start": 1756135948413, "stop": 1756135974645, "duration": 26232}, "status": "passed", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "time": {"start": 1756135948413, "stop": 1756135974364, "duration": 25951}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135974364, "stop": 1756135974645, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "65e4a68dbdfc164b", "name": "测试总结", "source": "65e4a68dbdfc164b.txt", "type": "text/plain", "size": 361}, {"uid": "2ac4867f83ee76f0", "name": "test_completed", "source": "2ac4867f83ee76f0.png", "type": "image/png", "size": 191418}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756135974645, "stop": 1756135974692, "duration": 47}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135974692, "stop": 1756135974909, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "b83112664697c882", "name": "测试总结", "source": "b83112664697c882.txt", "type": "text/plain", "size": 361}, {"uid": "b85afa1f202cb781", "name": "test_completed", "source": "b85afa1f202cb781.png", "type": "image/png", "size": 191418}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9cdcdacf7bd90394", "name": "stdout", "source": "9cdcdacf7bd90394.txt", "type": "text/plain", "size": 14051}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135974911, "stop": 1756135974911, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135974913, "stop": 1756135976415, "duration": 1502}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_send_my_recent_photos_to_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaSendMyRecentPhotosMomThroughWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_send_my_recent_photos_to_mom_through_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fa916ba066ad7774.json", "parameterValues": []}