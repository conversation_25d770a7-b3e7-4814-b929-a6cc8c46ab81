{"uid": "bbab0db63a3f7d1b", "name": "测试search whatsapp for me能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_whatsapp_for_me.TestEllaSearchWhatsappMe#test_search_whatsapp_for_me", "historyId": "f06396240414bb7ac3c5c049002eef1e", "time": {"start": 1756135860206, "stop": 1756135891703, "duration": 31497}, "description": "search whatsapp for me", "descriptionHtml": "<p>search whatsapp for me</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135847432, "stop": 1756135860202, "duration": 12770}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135860203, "stop": 1756135860203, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "search whatsapp for me", "status": "passed", "steps": [{"name": "执行命令: search whatsapp for me", "time": {"start": 1756135860206, "stop": 1756135891423, "duration": 31217}, "status": "passed", "steps": [{"name": "执行命令: search whatsapp for me", "time": {"start": 1756135860206, "stop": 1756135891162, "duration": 30956}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135891162, "stop": 1756135891422, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "29de52e1e1bcc056", "name": "测试总结", "source": "29de52e1e1bcc056.txt", "type": "text/plain", "size": 380}, {"uid": "b66943f16cc4f37f", "name": "test_completed", "source": "b66943f16cc4f37f.png", "type": "image/png", "size": 185747}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135891423, "stop": 1756135891425, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证<pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096C9610>已打开", "time": {"start": 1756135891425, "stop": 1756135891425, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135891425, "stop": 1756135891701, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "a4c0626baca3ff5b", "name": "测试总结", "source": "a4c0626baca3ff5b.txt", "type": "text/plain", "size": 380}, {"uid": "23aa111b587f504a", "name": "test_completed", "source": "23aa111b587f504a.png", "type": "image/png", "size": 185710}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cd944900eccefbc4", "name": "stdout", "source": "cd944900eccefbc4.txt", "type": "text/plain", "size": 15353}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135891703, "stop": 1756135891703, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135891705, "stop": 1756135893239, "duration": 1534}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_whatsapp_for_me"}, {"name": "subSuite", "value": "TestEllaSearchWhatsappMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_whatsapp_for_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bbab0db63a3f7d1b.json", "parameterValues": []}