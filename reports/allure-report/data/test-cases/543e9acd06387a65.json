{"uid": "543e9acd06387a65", "name": "测试kill whatsapp能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_kill_whatsapp.TestEllaKillWhatsapp#test_kill_whatsapp", "historyId": "18377608d977aa5cb5e2fc6b03b9ad05", "time": {"start": 1756134010316, "stop": 1756134038466, "duration": 28150}, "description": "kill whatsapp", "descriptionHtml": "<p>kill whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133996830, "stop": 1756134010313, "duration": 13483}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134010313, "stop": 1756134010313, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "kill whatsapp", "status": "passed", "steps": [{"name": "执行命令: kill whatsapp", "time": {"start": 1756134010317, "stop": 1756134038219, "duration": 27902}, "status": "passed", "steps": [{"name": "执行命令: kill whatsapp", "time": {"start": 1756134010317, "stop": 1756134037952, "duration": 27635}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134037952, "stop": 1756134038219, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "eda9cdd255cb9c27", "name": "测试总结", "source": "eda9cdd255cb9c27.txt", "type": "text/plain", "size": 261}, {"uid": "f561f7269f080c9a", "name": "test_completed", "source": "f561f7269f080c9a.png", "type": "image/png", "size": 172190}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134038219, "stop": 1756134038221, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134038221, "stop": 1756134038465, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "c903dd353bfa7a92", "name": "测试总结", "source": "c903dd353bfa7a92.txt", "type": "text/plain", "size": 261}, {"uid": "7ea57af863bd4d6f", "name": "test_completed", "source": "7ea57af863bd4d6f.png", "type": "image/png", "size": 172401}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ce1fc945a19ab646", "name": "stdout", "source": "ce1fc945a19ab646.txt", "type": "text/plain", "size": 12763}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134038467, "stop": 1756134038467, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134038472, "stop": 1756134039915, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_kill_whatsapp"}, {"name": "subSuite", "value": "TestEllaKillWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_kill_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "543e9acd06387a65.json", "parameterValues": []}