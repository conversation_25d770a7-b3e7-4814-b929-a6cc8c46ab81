{"uid": "5584ec7f2f918090", "name": "测试disable unfreeze返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_unfreeze.TestEllaDisableUnfreeze#test_disable_unfreeze", "historyId": "1695232002b2ad29ffa1faf52965470d", "time": {"start": 1756131167469, "stop": 1756131193692, "duration": 26223}, "description": "验证disable unfreeze指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable unfreeze指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131154095, "stop": 1756131167467, "duration": 13372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131167467, "stop": 1756131167467, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable unfreeze指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "time": {"start": 1756131167469, "stop": 1756131193440, "duration": 25971}, "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "time": {"start": 1756131167469, "stop": 1756131193165, "duration": 25696}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131193165, "stop": 1756131193439, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "65f7fc6052198298", "name": "测试总结", "source": "65f7fc6052198298.txt", "type": "text/plain", "size": 330}, {"uid": "74fc91c7c1b1048a", "name": "test_completed", "source": "74fc91c7c1b1048a.png", "type": "image/png", "size": 193988}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131193440, "stop": 1756131193442, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131193442, "stop": 1756131193691, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "5a5eef1c1fef84e2", "name": "测试总结", "source": "5a5eef1c1fef84e2.txt", "type": "text/plain", "size": 330}, {"uid": "ffabd69f63fdc69d", "name": "test_completed", "source": "ffabd69f63fdc69d.png", "type": "image/png", "size": 193988}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5d5dc18b9d6f35c3", "name": "stdout", "source": "5d5dc18b9d6f35c3.txt", "type": "text/plain", "size": 12554}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131193694, "stop": 1756131193694, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131193696, "stop": 1756131195073, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_unfreeze"}, {"name": "subSuite", "value": "TestEllaDisableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_unfreeze"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5584ec7f2f918090.json", "parameterValues": []}