{"uid": "7d906cdd207ac636", "name": "测试set gesture navigation返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation.TestEllaSetGestureNavigation#test_set_gesture_navigation", "historyId": "e14cd5a605f26d24de7f5f63d4667c68", "time": {"start": 1756136652178, "stop": 1756136688746, "duration": 36568}, "description": "验证set gesture navigation指令返回预期的不支持响应", "descriptionHtml": "<p>验证set gesture navigation指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set gesture navigation', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.microintelligence页面内容] System Navigation | Gestures | Three-Button | Learn Gestures | Circle to Search | Long press the navigation button, and circle, tap, or swipe to quickly search for the selected content. | Voice Assistant | To activate Voice Assistant, swipe up from either bottom corner. | Back Gesture Sensitivity']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_gesture_navigation.TestEllaSetGestureNavigation object at 0x00000292056FCA50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A863BD0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_gesture_navigation(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set gesture navigation', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.microintelligence页面内容] System Navigation | Gestures | Three-Button | Learn Gestures | Circle to Search | Long press the navigation button, and circle, tap, or swipe to quickly search for the selected content. | Voice Assistant | To activate Voice Assistant, swipe up from either bottom corner. | Back Gesture Sensitivity']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_gesture_navigation.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136638992, "stop": 1756136652177, "duration": 13185}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136652177, "stop": 1756136652177, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set gesture navigation指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set gesture navigation', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.microintelligence页面内容] System Navigation | Gestures | Three-Button | Learn Gestures | Circle to Search | Long press the navigation button, and circle, tap, or swipe to quickly search for the selected content. | Voice Assistant | To activate Voice Assistant, swipe up from either bottom corner. | Back Gesture Sensitivity']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_gesture_navigation.TestEllaSetGestureNavigation object at 0x00000292056FCA50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A863BD0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_gesture_navigation(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set gesture navigation', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.microintelligence页面内容] System Navigation | Gestures | Three-Button | Learn Gestures | Circle to Search | Long press the navigation button, and circle, tap, or swipe to quickly search for the selected content. | Voice Assistant | To activate Voice Assistant, swipe up from either bottom corner. | Back Gesture Sensitivity']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_gesture_navigation.py:33: AssertionError", "steps": [{"name": "执行命令: set gesture navigation", "time": {"start": 1756136652178, "stop": 1756136688741, "duration": 36563}, "status": "passed", "steps": [{"name": "执行命令: set gesture navigation", "time": {"start": 1756136652178, "stop": 1756136688488, "duration": 36310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136688488, "stop": 1756136688741, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "a0e6c48d139e0900", "name": "测试总结", "source": "a0e6c48d139e0900.txt", "type": "text/plain", "size": 628}, {"uid": "6cb4edd697ce9e40", "name": "test_completed", "source": "6cb4edd697ce9e40.png", "type": "image/png", "size": 175796}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136688741, "stop": 1756136688744, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set gesture navigation', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.microintelligence页面内容] System Navigation | Gestures | Three-Button | Learn Gestures | Circle to Search | Long press the navigation button, and circle, tap, or swipe to quickly search for the selected content. | Voice Assistant | To activate Voice Assistant, swipe up from either bottom corner. | Back Gesture Sensitivity']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_gesture_navigation.py\", line 33, in test_set_gesture_navigation\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3346285ab5134c49", "name": "stdout", "source": "3346285ab5134c49.txt", "type": "text/plain", "size": 16863}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136688749, "stop": 1756136689008, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "68473c41163a3e66", "name": "失败截图-TestEllaSetGestureNavigation", "source": "68473c41163a3e66.png", "type": "image/png", "size": 175874}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136689010, "stop": 1756136690492, "duration": 1482}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_gesture_navigation"}, {"name": "subSuite", "value": "TestEllaSetGestureNavigation"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "7d906cdd207ac636.json", "parameterValues": []}