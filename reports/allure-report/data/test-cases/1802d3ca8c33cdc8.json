{"uid": "1802d3ca8c33cdc8", "name": "测试close aivana能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "time": {"start": 1756117127225, "stop": 1756117191582, "duration": 64357}, "description": "close aivana", "descriptionHtml": "<p>close aivana</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117114458, "stop": 1756117127224, "duration": 12766}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117127224, "stop": 1756117127224, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close aivana", "status": "passed", "steps": [{"name": "执行命令: close aivana", "time": {"start": 1756117127225, "stop": 1756117191342, "duration": 64117}, "status": "passed", "steps": [{"name": "执行命令: close aivana", "time": {"start": 1756117127225, "stop": 1756117190855, "duration": 63630}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117190855, "stop": 1756117191341, "duration": 486}, "status": "passed", "steps": [], "attachments": [{"uid": "3a73b9908221984c", "name": "测试总结", "source": "3a73b9908221984c.txt", "type": "text/plain", "size": 569}, {"uid": "f56d0ab2925d3e60", "name": "test_completed", "source": "f56d0ab2925d3e60.png", "type": "image/png", "size": 173319}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证已打开", "time": {"start": 1756117191342, "stop": 1756117191342, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117191342, "stop": 1756117191582, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "d79b56632f25bd99", "name": "测试总结", "source": "d79b56632f25bd99.txt", "type": "text/plain", "size": 569}, {"uid": "6b287032c7af7480", "name": "test_completed", "source": "6b287032c7af7480.png", "type": "image/png", "size": 173723}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "139ed8cc32428a6d", "name": "stdout", "source": "139ed8cc32428a6d.txt", "type": "text/plain", "size": 15390}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117191583, "stop": 1756117191583, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117191584, "stop": 1756117193016, "duration": 1432}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1802d3ca8c33cdc8.json", "parameterValues": []}