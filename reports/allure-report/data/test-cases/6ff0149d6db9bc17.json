{"uid": "6ff0149d6db9bc17", "name": "测试delete the 8 o'clock alarm", "fullName": "testcases.test_ella.system_coupling.test_delete_the_o_clock_alarm.TestEllaOpenDeleteOClockAlarm#test_delete_the_o_clock_alarm", "historyId": "0c59a39b6298b394468a10532e127070", "time": {"start": 1756124287341, "stop": 1756124367502, "duration": 80161}, "description": "测试delete the 8 o'clock alarm指令", "descriptionHtml": "<p>测试delete the 8 o'clock alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124274342, "stop": 1756124287340, "duration": 12998}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124287340, "stop": 1756124287340, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试delete the 8 o'clock alarm指令", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756124287341, "stop": 1756124313055, "duration": 25714}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756124287343, "stop": 1756124312753, "duration": 25410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124312753, "stop": 1756124313053, "duration": 300}, "status": "passed", "steps": [], "attachments": [{"uid": "98cb5085512b8b75", "name": "测试总结", "source": "98cb5085512b8b75.txt", "type": "text/plain", "size": 241}, {"uid": "becc7d1de22315e9", "name": "test_completed", "source": "becc7d1de22315e9.png", "type": "image/png", "size": 150495}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1756124313055, "stop": 1756124340282, "duration": 27227}, "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1756124313055, "stop": 1756124339944, "duration": 26889}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124339944, "stop": 1756124340280, "duration": 336}, "status": "passed", "steps": [], "attachments": [{"uid": "38c8dbee21d18f99", "name": "测试总结", "source": "38c8dbee21d18f99.txt", "type": "text/plain", "size": 251}, {"uid": "d1af66730eb4ee84", "name": "test_completed", "source": "d1af66730eb4ee84.png", "type": "image/png", "size": 155104}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756124340282, "stop": 1756124367239, "duration": 26957}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756124340282, "stop": 1756124366932, "duration": 26650}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124366934, "stop": 1756124367237, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "64186a579196531e", "name": "测试总结", "source": "64186a579196531e.txt", "type": "text/plain", "size": 231}, {"uid": "c3b4b48e5d985a03", "name": "test_completed", "source": "c3b4b48e5d985a03.png", "type": "image/png", "size": 144158}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124367239, "stop": 1756124367245, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证响应不包含期望内容", "time": {"start": 1756124367245, "stop": 1756124367255, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124367255, "stop": 1756124367501, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "1f448bffa0fe800b", "name": "测试总结", "source": "1f448bffa0fe800b.txt", "type": "text/plain", "size": 239}, {"uid": "988527dfda74ff57", "name": "test_completed", "source": "988527dfda74ff57.png", "type": "image/png", "size": 144158}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "217ceaf5cd50c79f", "name": "stdout", "source": "217ceaf5cd50c79f.txt", "type": "text/plain", "size": 32351}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 12, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124367505, "stop": 1756124367505, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124367508, "stop": 1756124368874, "duration": 1366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_delete_the_o_clock_alarm"}, {"name": "subSuite", "value": "TestEllaOpenDeleteOClockAlarm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_delete_the_o_clock_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6ff0149d6db9bc17.json", "parameterValues": []}