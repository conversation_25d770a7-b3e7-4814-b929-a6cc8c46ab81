{"uid": "a56e3942da806e51", "name": "测试make a call by whatsapp能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_make_a_call_by_whatsapp.TestEllaMakeCallWhatsapp#test_make_a_call_by_whatsapp", "historyId": "a455fee07fb3c7d389380cb95d9a092c", "time": {"start": 1756134095942, "stop": 1756134131104, "duration": 35162}, "description": "make a call by whatsapp", "descriptionHtml": "<p>make a call by whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134082643, "stop": 1756134095940, "duration": 13297}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134095940, "stop": 1756134095940, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "make a call by whatsapp", "status": "passed", "steps": [{"name": "执行命令: make a call by whatsapp", "time": {"start": 1756134095942, "stop": 1756134130854, "duration": 34912}, "status": "passed", "steps": [{"name": "执行命令: make a call by whatsapp", "time": {"start": 1756134095942, "stop": 1756134130584, "duration": 34642}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134130584, "stop": 1756134130853, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "e522664c69316fd7", "name": "测试总结", "source": "e522664c69316fd7.txt", "type": "text/plain", "size": 319}, {"uid": "403521b1e649fb26", "name": "test_completed", "source": "403521b1e649fb26.png", "type": "image/png", "size": 186617}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134130854, "stop": 1756134130857, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134130857, "stop": 1756134131103, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "9a05491fc677b716", "name": "测试总结", "source": "9a05491fc677b716.txt", "type": "text/plain", "size": 319}, {"uid": "114de2cd9a52364e", "name": "test_completed", "source": "114de2cd9a52364e.png", "type": "image/png", "size": 186266}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1ca82568b49a12a7", "name": "stdout", "source": "1ca82568b49a12a7.txt", "type": "text/plain", "size": 13136}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134131104, "stop": 1756134131104, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134131106, "stop": 1756134132562, "duration": 1456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_make_a_call_by_whatsapp"}, {"name": "subSuite", "value": "TestEllaMakeCallWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_make_a_call_by_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a56e3942da806e51.json", "parameterValues": []}