{"uid": "7ca46deb21291b79", "name": "测试set ultra power saving返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving#test_set_ultra_power_saving", "historyId": "71c64c122f83e6fd138db517bbda4aef", "time": {"start": 1756137763331, "stop": 1756137798979, "duration": 35648}, "description": "验证set ultra power saving指令返回预期的不支持响应", "descriptionHtml": "<p>验证set ultra power saving指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set ultra power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 11 h 53 m | 90% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 59 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving object at 0x00000292058167D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097924D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_ultra_power_saving(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set ultra power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 11 h 53 m | 90% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 59 m | App Power Consumption Rankings | Battery & Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_ultra_power_saving.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137750467, "stop": 1756137763329, "duration": 12862}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137763329, "stop": 1756137763329, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set ultra power saving指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set ultra power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 11 h 53 m | 90% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 59 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving object at 0x00000292058167D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097924D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_ultra_power_saving(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set ultra power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 11 h 53 m | 90% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 59 m | App Power Consumption Rankings | Battery & Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_ultra_power_saving.py:33: AssertionError", "steps": [{"name": "执行命令: set ultra power saving", "time": {"start": 1756137763331, "stop": 1756137798975, "duration": 35644}, "status": "passed", "steps": [{"name": "执行命令: set ultra power saving", "time": {"start": 1756137763331, "stop": 1756137798689, "duration": 35358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137798689, "stop": 1756137798975, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "c974510f16d90c98", "name": "测试总结", "source": "c974510f16d90c98.txt", "type": "text/plain", "size": 541}, {"uid": "ac569747938248f5", "name": "test_completed", "source": "ac569747938248f5.png", "type": "image/png", "size": 165865}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137798975, "stop": 1756137798978, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set ultra power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 11 h 53 m | 90% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 59 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_ultra_power_saving.py\", line 33, in test_set_ultra_power_saving\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b6286d2fd0596110", "name": "stdout", "source": "b6286d2fd0596110.txt", "type": "text/plain", "size": 16635}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137798983, "stop": 1756137799228, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "4617e475c2937c60", "name": "失败截图-TestEllaSetUltraPowerSaving", "source": "4617e475c2937c60.png", "type": "image/png", "size": 165843}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137799229, "stop": 1756137800729, "duration": 1500}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_ultra_power_saving"}, {"name": "subSuite", "value": "TestEllaSetUltraPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "7ca46deb21291b79.json", "parameterValues": []}