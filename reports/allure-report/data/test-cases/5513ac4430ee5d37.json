{"uid": "5513ac4430ee5d37", "name": "测试play jay chou's music by spotify", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music_by_spotify.TestEllaOpenMusic#test_play_jay_chou_s_music_by_spotify", "historyId": "cb21afc40e4aec32b847936756c8ba6e", "time": {"start": 1756118342908, "stop": 1756118370850, "duration": 27942}, "description": "测试play jay chou's music by spotify指令", "descriptionHtml": "<p>测试play jay chou's music by spotify指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118330065, "stop": 1756118342907, "duration": 12842}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118342907, "stop": 1756118342907, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play jay chou's music by spotify指令", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music by spotify", "time": {"start": 1756118342908, "stop": 1756118370590, "duration": 27682}, "status": "passed", "steps": [{"name": "执行命令: play jay chou's music by spotify", "time": {"start": 1756118342908, "stop": 1756118370339, "duration": 27431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118370339, "stop": 1756118370589, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "8679234944301b9a", "name": "测试总结", "source": "8679234944301b9a.txt", "type": "text/plain", "size": 334}, {"uid": "729d13f2ed041e97", "name": "test_completed", "source": "729d13f2ed041e97.png", "type": "image/png", "size": 181308}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118370590, "stop": 1756118370593, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118370593, "stop": 1756118370849, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "80172a346624ecfd", "name": "测试总结", "source": "80172a346624ecfd.txt", "type": "text/plain", "size": 334}, {"uid": "86449ea2e91c2141", "name": "test_completed", "source": "86449ea2e91c2141.png", "type": "image/png", "size": 181086}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5b9a491c7a732c22", "name": "stdout", "source": "5b9a491c7a732c22.txt", "type": "text/plain", "size": 13232}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118370850, "stop": 1756118370850, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118370852, "stop": 1756118372226, "duration": 1374}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music_by_spotify"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music_by_spotify"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5513ac4430ee5d37.json", "parameterValues": []}