{"uid": "f65beecee99fd984", "name": "测试play music by VLC", "fullName": "testcases.test_ella.dialogue.test_play_music_by_VLC.TestEllaOpenPlayPoliticalNews#test_play_music_by_VLC", "historyId": "aac47e3ff4229b41a579f7c9ec938afb", "time": {"start": 1756120790437, "stop": 1756120818362, "duration": 27925}, "description": "测试play music by VLC指令", "descriptionHtml": "<p>测试play music by VLC指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120777484, "stop": 1756120790436, "duration": 12952}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120790436, "stop": 1756120790436, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music by VLC指令", "status": "passed", "steps": [{"name": "执行命令: play music by VLC", "time": {"start": 1756120790437, "stop": 1756120818148, "duration": 27711}, "status": "passed", "steps": [{"name": "执行命令: play music by VLC", "time": {"start": 1756120790437, "stop": 1756120817896, "duration": 27459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120817897, "stop": 1756120818148, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "9e59efc417055313", "name": "测试总结", "source": "9e59efc417055313.txt", "type": "text/plain", "size": 312}, {"uid": "8b757ac3137b672f", "name": "test_completed", "source": "8b757ac3137b672f.png", "type": "image/png", "size": 189976}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120818148, "stop": 1756120818151, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120818151, "stop": 1756120818362, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "832b213090629ad3", "name": "测试总结", "source": "832b213090629ad3.txt", "type": "text/plain", "size": 312}, {"uid": "60dd78bdb4e120a4", "name": "test_completed", "source": "60dd78bdb4e120a4.png", "type": "image/png", "size": 189703}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "97eb1251ab1e93ff", "name": "stdout", "source": "97eb1251ab1e93ff.txt", "type": "text/plain", "size": 13350}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120818363, "stop": 1756120818363, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120818364, "stop": 1756120819735, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_VLC"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_VLC"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f65beecee99fd984.json", "parameterValues": []}