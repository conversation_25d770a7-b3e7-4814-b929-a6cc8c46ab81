{"uid": "b1df56343a9a5197", "name": "测试remember the parking lot能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_remember_the_parking_lot.TestEllaRememberParkingLot#test_remember_the_parking_lot", "historyId": "31c983fdcc8b62f5927f99c0482b828d", "time": {"start": 1756135609340, "stop": 1756135635684, "duration": 26344}, "description": "remember the parking lot", "descriptionHtml": "<p>remember the parking lot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135596510, "stop": 1756135609339, "duration": 12829}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135609339, "stop": 1756135609339, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "remember the parking lot", "status": "passed", "steps": [{"name": "执行命令: remember the parking lot", "time": {"start": 1756135609342, "stop": 1756135635446, "duration": 26104}, "status": "passed", "steps": [{"name": "执行命令: remember the parking lot", "time": {"start": 1756135609342, "stop": 1756135635190, "duration": 25848}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135635190, "stop": 1756135635445, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "b1ba87be9ef6e9b2", "name": "测试总结", "source": "b1ba87be9ef6e9b2.txt", "type": "text/plain", "size": 313}, {"uid": "6aec547783507514", "name": "test_completed", "source": "6aec547783507514.png", "type": "image/png", "size": 176555}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135635446, "stop": 1756135635448, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135635448, "stop": 1756135635683, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "9e36274112e34a51", "name": "测试总结", "source": "9e36274112e34a51.txt", "type": "text/plain", "size": 313}, {"uid": "895f0c6ddad3e02c", "name": "test_completed", "source": "895f0c6ddad3e02c.png", "type": "image/png", "size": 176555}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b3a8f86085a555a1", "name": "stdout", "source": "b3a8f86085a555a1.txt", "type": "text/plain", "size": 12562}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135635686, "stop": 1756135635686, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135635688, "stop": 1756135637167, "duration": 1479}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_remember_the_parking_lot"}, {"name": "subSuite", "value": "TestEllaRememberParkingLot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_remember_the_parking_lot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b1df56343a9a5197.json", "parameterValues": []}