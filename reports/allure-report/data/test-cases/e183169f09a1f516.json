{"uid": "e183169f09a1f516", "name": "测试my phone is too slow能正常执行", "fullName": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow.TestEllaMyPhoneIsTooSlow#test_my_phone_is_too_slow", "historyId": "0a0a3640b2ba4adce516043bd9362070", "time": {"start": 1756117597941, "stop": 1756117623835, "duration": 25894}, "description": "my phone is too slow", "descriptionHtml": "<p>my phone is too slow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117585109, "stop": 1756117597940, "duration": 12831}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117597940, "stop": 1756117597940, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "my phone is too slow", "status": "passed", "steps": [{"name": "执行命令: my phone is too slow", "time": {"start": 1756117597941, "stop": 1756117623595, "duration": 25654}, "status": "passed", "steps": [{"name": "执行命令: my phone is too slow", "time": {"start": 1756117597941, "stop": 1756117623353, "duration": 25412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117623354, "stop": 1756117623595, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "e102d7aa3bec734d", "name": "测试总结", "source": "e102d7aa3bec734d.txt", "type": "text/plain", "size": 276}, {"uid": "eace1857479fa97", "name": "test_completed", "source": "eace1857479fa97.png", "type": "image/png", "size": 175622}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117623595, "stop": 1756117623596, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117623596, "stop": 1756117623835, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "bfb10cdf46b432f8", "name": "测试总结", "source": "bfb10cdf46b432f8.txt", "type": "text/plain", "size": 276}, {"uid": "f3b5d0f25a3b4a6e", "name": "test_completed", "source": "f3b5d0f25a3b4a6e.png", "type": "image/png", "size": 175622}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b9058a526b3e4d86", "name": "stdout", "source": "b9058a526b3e4d86.txt", "type": "text/plain", "size": 12405}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117623836, "stop": 1756117623836, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117623837, "stop": 1756117625155, "duration": 1318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_my_phone_is_too_slow"}, {"name": "subSuite", "value": "TestEllaMyPhoneIsTooSlow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e183169f09a1f516.json", "parameterValues": []}