{"uid": "47d490b159fdc253", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart.TestEllaSetScheduledPowerOffRestart#test_set_scheduled_power_on_off_and_restart", "historyId": "e21c5dda6a9f09862a68c3a0bcda554a", "time": {"start": 1756137177294, "stop": 1756137212656, "duration": 35362}, "description": "验证set scheduled power on/off and restart指令返回预期的不支持响应", "descriptionHtml": "<p>验证set scheduled power on/off and restart指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set scheduled power on/off and restart', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.mediatek.schpwronoff页面内容] Power On | 7:00 | Every day | Power Off | 8:00 | MON, TUE, WED, THU, FRI | Scheduled Restart | 2:00 | Wednesday | Scheduled power on/off and restart']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart.TestEllaSetScheduledPowerOffRestart object at 0x0000029205783590>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8DD8D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_scheduled_power_on_off_and_restart(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set scheduled power on/off and restart', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.mediatek.schpwronoff页面内容] Power On | 7:00 | Every day | Power Off | 8:00 | MON, TUE, WED, THU, FRI | Scheduled Restart | 2:00 | Wednesday | Scheduled power on/off and restart']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_scheduled_power_on_off_and_restart.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137164305, "stop": 1756137177293, "duration": 12988}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137177293, "stop": 1756137177293, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set scheduled power on/off and restart指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set scheduled power on/off and restart', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.mediatek.schpwronoff页面内容] Power On | 7:00 | Every day | Power Off | 8:00 | MON, TUE, WED, THU, FRI | Scheduled Restart | 2:00 | Wednesday | Scheduled power on/off and restart']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart.TestEllaSetScheduledPowerOffRestart object at 0x0000029205783590>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8DD8D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_scheduled_power_on_off_and_restart(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set scheduled power on/off and restart', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.mediatek.schpwronoff页面内容] Power On | 7:00 | Every day | Power Off | 8:00 | MON, TUE, WED, THU, FRI | Scheduled Restart | 2:00 | Wednesday | Scheduled power on/off and restart']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_scheduled_power_on_off_and_restart.py:33: AssertionError", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "time": {"start": 1756137177294, "stop": 1756137212648, "duration": 35354}, "status": "passed", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "time": {"start": 1756137177294, "stop": 1756137212376, "duration": 35082}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137212376, "stop": 1756137212648, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "f3b8aeeb8dc5c35f", "name": "测试总结", "source": "f3b8aeeb8dc5c35f.txt", "type": "text/plain", "size": 503}, {"uid": "ae174dad8a36f341", "name": "test_completed", "source": "ae174dad8a36f341.png", "type": "image/png", "size": 169830}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137212648, "stop": 1756137212655, "duration": 7}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set scheduled power on/off and restart', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.mediatek.schpwronoff页面内容] Power On | 7:00 | Every day | Power Off | 8:00 | MON, TUE, WED, THU, FRI | Scheduled Restart | 2:00 | Wednesday | Scheduled power on/off and restart']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_scheduled_power_on_off_and_restart.py\", line 33, in test_set_scheduled_power_on_off_and_restart\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5dd97d9457718900", "name": "stdout", "source": "5dd97d9457718900.txt", "type": "text/plain", "size": 16661}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137212661, "stop": 1756137212880, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "d45eb67a70f8cc06", "name": "失败截图-TestEllaSetScheduledPowerOffRestart", "source": "d45eb67a70f8cc06.png", "type": "image/png", "size": 169827}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137212881, "stop": 1756137214565, "duration": 1684}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_scheduled_power_on_off_and_restart"}, {"name": "subSuite", "value": "TestEllaSetScheduledPowerOffRestart"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "47d490b159fdc253.json", "parameterValues": []}