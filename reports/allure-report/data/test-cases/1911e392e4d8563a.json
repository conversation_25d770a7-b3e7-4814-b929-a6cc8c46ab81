{"uid": "1911e392e4d8563a", "name": "测试disable zonetouch master返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master.TestEllaDisableZonetouchMaster#test_disable_zonetouch_master", "historyId": "d094a0b21c0bd532e6db707dcbab5564", "time": {"start": 1756131207759, "stop": 1756131233490, "duration": 25731}, "description": "验证disable zonetouch master指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable zonetouch master指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131195078, "stop": 1756131207748, "duration": 12670}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131207748, "stop": 1756131207749, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable zonetouch master指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1756131207759, "stop": 1756131233260, "duration": 25501}, "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1756131207759, "stop": 1756131232980, "duration": 25221}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131232980, "stop": 1756131233260, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "dec4c02fd7a7e33c", "name": "测试总结", "source": "dec4c02fd7a7e33c.txt", "type": "text/plain", "size": 343}, {"uid": "ba842069398c8a6c", "name": "test_completed", "source": "ba842069398c8a6c.png", "type": "image/png", "size": 185847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131233260, "stop": 1756131233262, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131233262, "stop": 1756131233489, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "71cead42f6ea1929", "name": "测试总结", "source": "71cead42f6ea1929.txt", "type": "text/plain", "size": 343}, {"uid": "480ea3cdcdf1875a", "name": "test_completed", "source": "480ea3cdcdf1875a.png", "type": "image/png", "size": 185847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "96bfc8a90526da3f", "name": "stdout", "source": "96bfc8a90526da3f.txt", "type": "text/plain", "size": 12631}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131233491, "stop": 1756131233491, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131233493, "stop": 1756131234922, "duration": 1429}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaDisableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1911e392e4d8563a.json", "parameterValues": []}