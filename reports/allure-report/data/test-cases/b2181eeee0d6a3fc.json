{"uid": "b2181eeee0d6a3fc", "name": "测试download in playstore", "fullName": "testcases.test_ella.unsupported_commands.test_download_in_playstore.TestEllaOpenGooglePlaystore#test_download_in_playstore", "historyId": "c2ecb960f7f893feeaa2f24a34c9d77e", "time": {"start": 1756131292648, "stop": 1756131323862, "duration": 31214}, "description": "测试download in playstore指令", "descriptionHtml": "<p>测试download in playstore指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131279847, "stop": 1756131292647, "duration": 12800}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131292647, "stop": 1756131292647, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试download in playstore指令", "status": "passed", "steps": [{"name": "执行命令: download in playstore", "time": {"start": 1756131292648, "stop": 1756131323624, "duration": 30976}, "status": "passed", "steps": [{"name": "执行命令: download in playstore", "time": {"start": 1756131292648, "stop": 1756131323340, "duration": 30692}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131323340, "stop": 1756131323624, "duration": 284}, "status": "passed", "steps": [], "attachments": [{"uid": "661921dd1d3a916e", "name": "测试总结", "source": "661921dd1d3a916e.txt", "type": "text/plain", "size": 321}, {"uid": "9721fbf16db4dcbc", "name": "test_completed", "source": "9721fbf16db4dcbc.png", "type": "image/png", "size": 170549}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756131323624, "stop": 1756131323626, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证google_playstore已打开", "time": {"start": 1756131323626, "stop": 1756131323626, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131323626, "stop": 1756131323861, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "543881e8f61999be", "name": "测试总结", "source": "543881e8f61999be.txt", "type": "text/plain", "size": 321}, {"uid": "9e6a2dc1429fadaa", "name": "test_completed", "source": "9e6a2dc1429fadaa.png", "type": "image/png", "size": 171079}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2e8cd7b92a113222", "name": "stdout", "source": "2e8cd7b92a113222.txt", "type": "text/plain", "size": 15223}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131323862, "stop": 1756131323862, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131323863, "stop": 1756131325280, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_download_in_playstore"}, {"name": "subSuite", "value": "TestEllaOpenGooglePlaystore"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_download_in_playstore"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b2181eeee0d6a3fc.json", "parameterValues": []}