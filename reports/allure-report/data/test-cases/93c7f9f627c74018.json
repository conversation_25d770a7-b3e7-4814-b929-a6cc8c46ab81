{"uid": "93c7f9f627c74018", "name": "测试why is my phone not ringing on incoming calls能正常执行", "fullName": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls.TestEllaWhyIsMyPhoneNotRingingIncomingCalls#test_why_is_my_phone_not_ringing_on_incoming_calls", "historyId": "cf0ebfd1b4e2ab43e2f516ad6a1a6917", "time": {"start": 1756122681317, "stop": 1756122716775, "duration": 35458}, "description": "why is my phone not ringing on incoming calls", "descriptionHtml": "<p>why is my phone not ringing on incoming calls</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122668245, "stop": 1756122681299, "duration": 13054}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122681299, "stop": 1756122681299, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "why is my phone not ringing on incoming calls", "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1756122681317, "stop": 1756122716537, "duration": 35220}, "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1756122681317, "stop": 1756122716266, "duration": 34949}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122716266, "stop": 1756122716537, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "b380487941f48cc2", "name": "测试总结", "source": "b380487941f48cc2.txt", "type": "text/plain", "size": 714}, {"uid": "86b596dc7606b679", "name": "test_completed", "source": "86b596dc7606b679.png", "type": "image/png", "size": 211034}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122716537, "stop": 1756122716539, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122716539, "stop": 1756122716775, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "2553c6669aecfc58", "name": "测试总结", "source": "2553c6669aecfc58.txt", "type": "text/plain", "size": 714}, {"uid": "bf3326d910be81b6", "name": "test_completed", "source": "bf3326d910be81b6.png", "type": "image/png", "size": 210790}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "729f288bd6f8c226", "name": "stdout", "source": "729f288bd6f8c226.txt", "type": "text/plain", "size": 14311}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756122716776, "stop": 1756122718177, "duration": 1401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756122716776, "stop": 1756122716776, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "subSuite", "value": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "93c7f9f627c74018.json", "parameterValues": []}