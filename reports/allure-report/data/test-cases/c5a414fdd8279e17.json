{"uid": "c5a414fdd8279e17", "name": "测试play taylor swift‘s song love story", "fullName": "testcases.test_ella.unsupported_commands.test_play_taylor_swift_s_song_love_sotry.TestEllaOpenPlayPoliticalNews#test_play_taylor_swift_s_song_love_sotry", "historyId": "b12ee4e4a5d7e51ba0abe166b6c90352", "time": {"start": 1756135217514, "stop": 1756135263067, "duration": 45553}, "description": "测试play taylor swift‘s song love story指令", "descriptionHtml": "<p>测试play taylor swift‘s song love story指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135204489, "stop": 1756135217510, "duration": 13021}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135217511, "stop": 1756135217511, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play taylor swift‘s song love story指令", "status": "passed", "steps": [{"name": "执行命令: play taylor swift‘s song love story", "time": {"start": 1756135217514, "stop": 1756135262839, "duration": 45325}, "status": "passed", "steps": [{"name": "执行命令: play taylor swift‘s song love story", "time": {"start": 1756135217515, "stop": 1756135262540, "duration": 45025}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135262540, "stop": 1756135262838, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "bb1509b00daf1bd5", "name": "测试总结", "source": "bb1509b00daf1bd5.txt", "type": "text/plain", "size": 644}, {"uid": "8f89f6d8dff209c5", "name": "test_completed", "source": "8f89f6d8dff209c5.png", "type": "image/png", "size": 180273}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135262839, "stop": 1756135262840, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证visha已打开", "time": {"start": 1756135262841, "stop": 1756135262841, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135262841, "stop": 1756135263067, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "c5af3d70d7cf7b89", "name": "测试总结", "source": "c5af3d70d7cf7b89.txt", "type": "text/plain", "size": 644}, {"uid": "fe3f7539e2d9024", "name": "test_completed", "source": "fe3f7539e2d9024.png", "type": "image/png", "size": 180273}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "642a66bbeecbb8e0", "name": "stdout", "source": "642a66bbeecbb8e0.txt", "type": "text/plain", "size": 16031}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135263068, "stop": 1756135263068, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135263069, "stop": 1756135264534, "duration": 1465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_taylor_swift_s_song_love_sotry"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_taylor_swift_s_song_love_sotry"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c5a414fdd8279e17.json", "parameterValues": []}