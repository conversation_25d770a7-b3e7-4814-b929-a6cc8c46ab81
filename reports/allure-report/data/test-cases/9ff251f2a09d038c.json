{"uid": "9ff251f2a09d038c", "name": "测试set my alarm volume to 50%", "fullName": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to.TestEllaOpenClock#test_set_my_alarm_volume_to", "historyId": "489e5c631d3a3ce20f77bce4c7c6632a", "time": {"start": 1756125877091, "stop": 1756125904947, "duration": 27856}, "description": "测试set my alarm volume to 50%指令", "descriptionHtml": "<p>测试set my alarm volume to 50%指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125864042, "stop": 1756125877090, "duration": 13048}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125877090, "stop": 1756125877090, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试set my alarm volume to 50%指令", "status": "passed", "steps": [{"name": "执行命令: set my alarm volume to 50%", "time": {"start": 1756125877091, "stop": 1756125904702, "duration": 27611}, "status": "passed", "steps": [{"name": "执行命令: set my alarm volume to 50%", "time": {"start": 1756125877091, "stop": 1756125904382, "duration": 27291}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125904382, "stop": 1756125904702, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "85c49ac5efe63005", "name": "测试总结", "source": "85c49ac5efe63005.txt", "type": "text/plain", "size": 309}, {"uid": "bdf98b076c6c612", "name": "test_completed", "source": "bdf98b076c6c612.png", "type": "image/png", "size": 190939}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125904702, "stop": 1756125904703, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"time": {"start": 1756125904703, "stop": 1756125904703, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125904703, "stop": 1756125904947, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "4f43ec0d17fc558a", "name": "测试总结", "source": "4f43ec0d17fc558a.txt", "type": "text/plain", "size": 309}, {"uid": "8e95bc09c81b9547", "name": "test_completed", "source": "8e95bc09c81b9547.png", "type": "image/png", "size": 191566}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "759cd672b87e5d3b", "name": "stdout", "source": "759cd672b87e5d3b.txt", "type": "text/plain", "size": 13889}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125904948, "stop": 1756125904948, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125904949, "stop": 1756125906313, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_my_alarm_volume_to"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9ff251f2a09d038c.json", "parameterValues": []}