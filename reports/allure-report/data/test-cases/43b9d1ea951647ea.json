{"uid": "43b9d1ea951647ea", "name": "测试turn off adaptive brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness.TestEllaTurnOffAdaptiveBrightness#test_turn_off_adaptive_brightness", "historyId": "ebd2c9b6cd7c07b69e348328a207e18b", "time": {"start": 1756127377215, "stop": 1756127402660, "duration": 25445}, "description": "turn off adaptive brightness", "descriptionHtml": "<p>turn off adaptive brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127364479, "stop": 1756127377214, "duration": 12735}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127377214, "stop": 1756127377214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn off adaptive brightness", "status": "passed", "steps": [{"name": "执行命令: turn off adaptive brightness", "time": {"start": 1756127377215, "stop": 1756127402451, "duration": 25236}, "status": "passed", "steps": [{"name": "执行命令: turn off adaptive brightness", "time": {"start": 1756127377215, "stop": 1756127402160, "duration": 24945}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127402160, "stop": 1756127402450, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "8b8384ec58ba12e0", "name": "测试总结", "source": "8b8384ec58ba12e0.txt", "type": "text/plain", "size": 337}, {"uid": "2bd6b8c9a5a74301", "name": "test_completed", "source": "2bd6b8c9a5a74301.png", "type": "image/png", "size": 167459}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127402451, "stop": 1756127402452, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127402453, "stop": 1756127402453, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127402453, "stop": 1756127402660, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "151ca6a9202a4df0", "name": "测试总结", "source": "151ca6a9202a4df0.txt", "type": "text/plain", "size": 337}, {"uid": "76570b5fdc7a90f4", "name": "test_completed", "source": "76570b5fdc7a90f4.png", "type": "image/png", "size": 167693}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ecb7790f06514a2a", "name": "stdout", "source": "ecb7790f06514a2a.txt", "type": "text/plain", "size": 13329}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127402661, "stop": 1756127402661, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127402662, "stop": 1756127404016, "duration": 1354}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_adaptive_brightness"}, {"name": "subSuite", "value": "TestEllaTurnOffAdaptiveBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "43b9d1ea951647ea.json", "parameterValues": []}