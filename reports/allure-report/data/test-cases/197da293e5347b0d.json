{"uid": "197da293e5347b0d", "name": "测试play jay chou's music", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music.TestEllaOpenMusic#test_play_jay_chou_s_music", "historyId": "b4e75f584d82368436f820de28f92cfd", "time": {"start": 1756118286423, "stop": 1756118328693, "duration": 42270}, "description": "测试play jay chou's music指令", "descriptionHtml": "<p>测试play jay chou's music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118273580, "stop": 1756118286421, "duration": 12841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118286422, "stop": 1756118286422, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play jay chou's music指令", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1756118286423, "stop": 1756118328447, "duration": 42024}, "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1756118286423, "stop": 1756118328146, "duration": 41723}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118328146, "stop": 1756118328447, "duration": 301}, "status": "passed", "steps": [], "attachments": [{"uid": "f2562d22be500d4b", "name": "测试总结", "source": "f2562d22be500d4b.txt", "type": "text/plain", "size": 616}, {"uid": "e3ddb5bcbb8c2677", "name": "test_completed", "source": "e3ddb5bcbb8c2677.png", "type": "image/png", "size": 174991}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118328447, "stop": 1756118328450, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证music已打开", "time": {"start": 1756118328450, "stop": 1756118328450, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118328450, "stop": 1756118328692, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "32610930e8b94b4c", "name": "测试总结", "source": "32610930e8b94b4c.txt", "type": "text/plain", "size": 616}, {"uid": "c43d2d605ad0e335", "name": "test_completed", "source": "c43d2d605ad0e335.png", "type": "image/png", "size": 174994}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f6c9a8e5e52ecc3", "name": "stdout", "source": "f6c9a8e5e52ecc3.txt", "type": "text/plain", "size": 15871}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118328694, "stop": 1756118328694, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118328695, "stop": 1756118330054, "duration": 1359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "197da293e5347b0d.json", "parameterValues": []}