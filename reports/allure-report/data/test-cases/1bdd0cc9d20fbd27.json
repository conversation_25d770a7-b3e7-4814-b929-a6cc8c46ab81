{"uid": "1bdd0cc9d20fbd27", "name": "测试Switch to Barrage Notification能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification.TestEllaSwitchBarrageNotification#test_switch_to_barrage_notification", "historyId": "79b68fd9ac84793c5f55250aad03649a", "time": {"start": 1756126622913, "stop": 1756126657922, "duration": 35009}, "description": "Switch to Barrage Notification", "descriptionHtml": "<p>Switch to Barrage Notification</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126610032, "stop": 1756126622912, "duration": 12880}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126622912, "stop": 1756126622912, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Switch to Barrage Notification", "status": "passed", "steps": [{"name": "执行命令: Switch to Barrage Notification", "time": {"start": 1756126622914, "stop": 1756126657682, "duration": 34768}, "status": "passed", "steps": [{"name": "执行命令: Switch to Barrage Notification", "time": {"start": 1756126622914, "stop": 1756126657395, "duration": 34481}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126657395, "stop": 1756126657682, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "dfbeb7b416468f40", "name": "测试总结", "source": "dfbeb7b416468f40.txt", "type": "text/plain", "size": 712}, {"uid": "a0212397374eb727", "name": "test_completed", "source": "a0212397374eb727.png", "type": "image/png", "size": 173463}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756126657682, "stop": 1756126657685, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126657685, "stop": 1756126657918, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "a0a6219e8e70b21", "name": "测试总结", "source": "a0a6219e8e70b21.txt", "type": "text/plain", "size": 712}, {"uid": "fc7b0fc1ac137898", "name": "test_completed", "source": "fc7b0fc1ac137898.png", "type": "image/png", "size": 173463}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b071b719d6b775c1", "name": "stdout", "source": "b071b719d6b775c1.txt", "type": "text/plain", "size": 15660}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126657924, "stop": 1756126657924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126657926, "stop": 1756126659371, "duration": 1445}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_barrage_notification"}, {"name": "subSuite", "value": "TestEllaSwitchBarrageNotification"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1bdd0cc9d20fbd27.json", "parameterValues": []}