{"uid": "2b42ac864d6e7126", "name": "测试a clear glass cup", "fullName": "testcases.test_ella.unsupported_commands.test_a_clear_glass_cup.TestEllaOpenPlayPoliticalNews#test_a_clear_glass_cup", "historyId": "728822fc623e888cd9efa450f4737787", "time": {"start": 1756129479737, "stop": 1756129508301, "duration": 28564}, "description": "测试a clear glass cup指令", "descriptionHtml": "<p>测试a clear glass cup指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129466962, "stop": 1756129479735, "duration": 12773}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129479735, "stop": 1756129479735, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试a clear glass cup指令", "status": "passed", "steps": [{"name": "执行命令: a clear glass cup", "time": {"start": 1756129479753, "stop": 1756129508046, "duration": 28293}, "status": "passed", "steps": [{"name": "执行命令: a clear glass cup", "time": {"start": 1756129479753, "stop": 1756129507780, "duration": 28027}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129507780, "stop": 1756129508045, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "f7021ccb2e17e59f", "name": "测试总结", "source": "f7021ccb2e17e59f.txt", "type": "text/plain", "size": 1102}, {"uid": "85a24da888c77000", "name": "test_completed", "source": "85a24da888c77000.png", "type": "image/png", "size": 211145}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129508046, "stop": 1756129508048, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129508048, "stop": 1756129508274, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "edccf0be5a21fede", "name": "测试总结", "source": "edccf0be5a21fede.txt", "type": "text/plain", "size": 1102}, {"uid": "e5aaed60bf173043", "name": "test_completed", "source": "e5aaed60bf173043.png", "type": "image/png", "size": 211513}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e259cbe92468350a", "name": "stdout", "source": "e259cbe92468350a.txt", "type": "text/plain", "size": 16218}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129508302, "stop": 1756129508302, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129508307, "stop": 1756129509759, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_clear_glass_cup"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_clear_glass_cup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2b42ac864d6e7126.json", "parameterValues": []}