{"uid": "150061aea3b31a1c", "name": "测试the battery of the mobile phone is too low能正常执行", "fullName": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low.TestEllaBatteryMobilePhoneIsTooLow#test_the_battery_of_the_mobile_phone_is_too_low", "historyId": "2ecac23eb3f511651fafc6ba6a3725f2", "time": {"start": 1756127140982, "stop": 1756127172963, "duration": 31981}, "description": "the battery of the mobile phone is too low", "descriptionHtml": "<p>the battery of the mobile phone is too low</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127127777, "stop": 1756127140981, "duration": 13204}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127140981, "stop": 1756127140981, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "the battery of the mobile phone is too low", "status": "passed", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "time": {"start": 1756127140983, "stop": 1756127172723, "duration": 31740}, "status": "passed", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "time": {"start": 1756127140983, "stop": 1756127172452, "duration": 31469}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127172452, "stop": 1756127172723, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "2e3d4190a22504cf", "name": "测试总结", "source": "2e3d4190a22504cf.txt", "type": "text/plain", "size": 409}, {"uid": "4d733d549b0aa998", "name": "test_completed", "source": "4d733d549b0aa998.png", "type": "image/png", "size": 175618}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127172723, "stop": 1756127172726, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1756127172726, "stop": 1756127172726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127172726, "stop": 1756127172962, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "d81323a90630f252", "name": "测试总结", "source": "d81323a90630f252.txt", "type": "text/plain", "size": 409}, {"uid": "bf2e17a2e3e265", "name": "test_completed", "source": "bf2e17a2e3e265.png", "type": "image/png", "size": 175725}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7dcd84ba5cc180", "name": "stdout", "source": "7dcd84ba5cc180.txt", "type": "text/plain", "size": 15470}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127172963, "stop": 1756127172963, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127172964, "stop": 1756127174385, "duration": 1421}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_the_battery_of_the_mobile_phone_is_too_low"}, {"name": "subSuite", "value": "TestEllaBatteryMobilePhoneIsTooLow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "150061aea3b31a1c.json", "parameterValues": []}