{"uid": "cf8dc484267c5f7b", "name": "测试turn off the 7AM alarm", "fullName": "testcases.test_ella.component_coupling.test_turn_off_the_7_am_alarm.TestEllaOpenClock#test_turn_off_the_am_alarm", "historyId": "e56b7788214bc4e18231f16dfd713954", "time": {"start": 1756118860249, "stop": 1756118966036, "duration": 105787}, "description": "测试turn off the 7AM alarm指令", "descriptionHtml": "<p>测试turn off the 7AM alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118847574, "stop": 1756118860247, "duration": 12673}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118860247, "stop": 1756118860247, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn off the 7AM alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118860249, "stop": 1756118885611, "duration": 25362}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118860249, "stop": 1756118885356, "duration": 25107}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118885356, "stop": 1756118885610, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "88cf011746bcc0d7", "name": "测试总结", "source": "88cf011746bcc0d7.txt", "type": "text/plain", "size": 303}, {"uid": "a5fd88a1f99a92a6", "name": "test_completed", "source": "a5fd88a1f99a92a6.png", "type": "image/png", "size": 180501}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 7 am", "time": {"start": 1756118885611, "stop": 1756118912578, "duration": 26967}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 7 am", "time": {"start": 1756118885611, "stop": 1756118912288, "duration": 26677}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118912288, "stop": 1756118912577, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "9a81ea89b0068ac8", "name": "测试总结", "source": "9a81ea89b0068ac8.txt", "type": "text/plain", "size": 242}, {"uid": "9465e5d18ad24ea3", "name": "test_completed", "source": "9465e5d18ad24ea3.png", "type": "image/png", "size": 144873}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn off the 7AM alarm", "time": {"start": 1756118912578, "stop": 1756118939124, "duration": 26546}, "status": "passed", "steps": [{"name": "执行命令: turn off the 7AM alarm", "time": {"start": 1756118912578, "stop": 1756118938840, "duration": 26262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118938840, "stop": 1756118939123, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "ca9abf40abddd955", "name": "测试总结", "source": "ca9abf40abddd955.txt", "type": "text/plain", "size": 253}, {"uid": "99b6a3e1449ff750", "name": "test_completed", "source": "99b6a3e1449ff750.png", "type": "image/png", "size": 146928}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756118939124, "stop": 1756118965817, "duration": 26693}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756118939124, "stop": 1756118965534, "duration": 26410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118965534, "stop": 1756118965816, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "59710af82766ec4a", "name": "测试总结", "source": "59710af82766ec4a.txt", "type": "text/plain", "size": 224}, {"uid": "3bb76c4777da7c8c", "name": "test_completed", "source": "3bb76c4777da7c8c.png", "type": "image/png", "size": 143213}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118965817, "stop": 1756118965819, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118965819, "stop": 1756118966035, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "a8da640bd2c1695a", "name": "测试总结", "source": "a8da640bd2c1695a.txt", "type": "text/plain", "size": 228}, {"uid": "23b8d2888f59dd76", "name": "test_completed", "source": "23b8d2888f59dd76.png", "type": "image/png", "size": 142963}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d4165831c20f2a18", "name": "stdout", "source": "d4165831c20f2a18.txt", "type": "text/plain", "size": 41164}], "parameters": [], "attachmentsCount": 11, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 14, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118966037, "stop": 1756118966037, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118966038, "stop": 1756118967389, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_off_the_7_am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_off_the_7_am_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cf8dc484267c5f7b.json", "parameterValues": []}