{"uid": "a8b8455f9d34bbb0", "name": "测试why my charging is so slow能正常执行", "fullName": "testcases.test_ella.dialogue.test_why_my_charging_is_so_slow.TestEllaWhyMyChargingIsSoSlow#test_why_my_charging_is_so_slow", "historyId": "3333dd58fd9312a504ae6bc6edf830af", "time": {"start": 1756122731077, "stop": 1756122756686, "duration": 25609}, "description": "why my charging is so slow", "descriptionHtml": "<p>why my charging is so slow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122718186, "stop": 1756122731076, "duration": 12890}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122731076, "stop": 1756122731076, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "why my charging is so slow", "status": "passed", "steps": [{"name": "执行命令: why my charging is so slow", "time": {"start": 1756122731077, "stop": 1756122756436, "duration": 25359}, "status": "passed", "steps": [{"name": "执行命令: why my charging is so slow", "time": {"start": 1756122731077, "stop": 1756122756156, "duration": 25079}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122756157, "stop": 1756122756436, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "d3e618484366ac69", "name": "测试总结", "source": "d3e618484366ac69.txt", "type": "text/plain", "size": 779}, {"uid": "f78a384dbb76ed37", "name": "test_completed", "source": "f78a384dbb76ed37.png", "type": "image/png", "size": 223131}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122756436, "stop": 1756122756438, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122756438, "stop": 1756122756685, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "9e1b0253929bd785", "name": "测试总结", "source": "9e1b0253929bd785.txt", "type": "text/plain", "size": 779}, {"uid": "2e3aecd5f75ffa90", "name": "test_completed", "source": "2e3aecd5f75ffa90.png", "type": "image/png", "size": 223129}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "887e43aa5182c4f5", "name": "stdout", "source": "887e43aa5182c4f5.txt", "type": "text/plain", "size": 13807}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122756686, "stop": 1756122756686, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122756688, "stop": 1756122758030, "duration": 1342}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_why_my_charging_is_so_slow"}, {"name": "subSuite", "value": "TestEllaWhyMyChargingIsSoSlow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_why_my_charging_is_so_slow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a8b8455f9d34bbb0.json", "parameterValues": []}