{"uid": "8ee476f9e6d3489", "name": "测试check my to-do list能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_to_do_list.TestEllaCheckMyDoList#test_check_my_to_do_list", "historyId": "569e770c250388bfbcf64d0cbbb8b351", "time": {"start": 1756130321813, "stop": 1756130348351, "duration": 26538}, "description": "check my to-do list", "descriptionHtml": "<p>check my to-do list</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130309085, "stop": 1756130321812, "duration": 12727}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130321812, "stop": 1756130321812, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "check my to-do list", "status": "passed", "steps": [{"name": "执行命令: check my to-do list", "time": {"start": 1756130321814, "stop": 1756130348111, "duration": 26297}, "status": "passed", "steps": [{"name": "执行命令: check my to-do list", "time": {"start": 1756130321814, "stop": 1756130347898, "duration": 26084}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130347898, "stop": 1756130348111, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "d61f66f5818b71d7", "name": "测试总结", "source": "d61f66f5818b71d7.txt", "type": "text/plain", "size": 284}, {"uid": "9d4ba977d843b0e2", "name": "test_completed", "source": "9d4ba977d843b0e2.png", "type": "image/png", "size": 174289}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130348111, "stop": 1756130348113, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130348113, "stop": 1756130348350, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "7a6ea36c6c7c12c4", "name": "测试总结", "source": "7a6ea36c6c7c12c4.txt", "type": "text/plain", "size": 284}, {"uid": "4c5ccca7cc3f1ff2", "name": "test_completed", "source": "4c5ccca7cc3f1ff2.png", "type": "image/png", "size": 174289}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "33d78ee2675d015c", "name": "stdout", "source": "33d78ee2675d015c.txt", "type": "text/plain", "size": 12431}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130348351, "stop": 1756130348351, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130348352, "stop": 1756130349771, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_to_do_list"}, {"name": "subSuite", "value": "TestEllaCheckMyDoList"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_to_do_list"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8ee476f9e6d3489.json", "parameterValues": []}