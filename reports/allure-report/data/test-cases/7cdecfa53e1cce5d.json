{"uid": "7cdecfa53e1cce5d", "name": "测试close bluetooth能正常执行", "fullName": "testcases.test_ella.system_coupling.test_close_bluetooth.TestEllaCloseBluetooth#test_close_bluetooth", "historyId": "b9bb05ac1dcf8926da63d4ecbb1524cd", "time": {"start": 1756124019337, "stop": 1756124046996, "duration": 27659}, "description": "close bluetooth", "descriptionHtml": "<p>close bluetooth</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124006800, "stop": 1756124019336, "duration": 12536}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124019337, "stop": 1756124019337, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close bluetooth", "status": "passed", "steps": [{"name": "执行命令: close bluetooth", "time": {"start": 1756124019337, "stop": 1756124046786, "duration": 27449}, "status": "passed", "steps": [{"name": "执行命令: close bluetooth", "time": {"start": 1756124019337, "stop": 1756124046533, "duration": 27196}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124046533, "stop": 1756124046784, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "cab20384a0a46020", "name": "测试总结", "source": "cab20384a0a46020.txt", "type": "text/plain", "size": 299}, {"uid": "c5d89d5bac2b1b7d", "name": "test_completed", "source": "c5d89d5bac2b1b7d.png", "type": "image/png", "size": 168090}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124046786, "stop": 1756124046787, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124046787, "stop": 1756124046787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124046787, "stop": 1756124046996, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "68086952121ba57", "name": "测试总结", "source": "68086952121ba57.txt", "type": "text/plain", "size": 299}, {"uid": "a4a7c9cf20202852", "name": "test_completed", "source": "a4a7c9cf20202852.png", "type": "image/png", "size": 167676}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "74267f190d018e9e", "name": "stdout", "source": "74267f190d018e9e.txt", "type": "text/plain", "size": 13670}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124046997, "stop": 1756124046997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124046999, "stop": 1756124048416, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_bluetooth"}, {"name": "subSuite", "value": "TestEllaCloseBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7cdecfa53e1cce5d.json", "parameterValues": []}