{"uid": "e8a84c64a920e329", "name": "测试increase the volume to the maximun能正常执行", "fullName": "testcases.test_ella.system_coupling.test_increase_the_volume_to_the_maximun.TestEllaIncreaseVolumeMaximun#test_increase_the_volume_to_the_maximun", "historyId": "563b9c2c8d2e1d68901eeac733e12913", "time": {"start": 1756124670867, "stop": 1756124697040, "duration": 26173}, "description": "increase the volume to the maximun", "descriptionHtml": "<p>increase the volume to the maximun</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124657698, "stop": 1756124670866, "duration": 13168}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124670866, "stop": 1756124670866, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "increase the volume to the maximun", "status": "passed", "steps": [{"name": "执行命令: increase the volume to the maximun", "time": {"start": 1756124670867, "stop": 1756124696798, "duration": 25931}, "status": "passed", "steps": [{"name": "执行命令: increase the volume to the maximun", "time": {"start": 1756124670867, "stop": 1756124696542, "duration": 25675}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124696542, "stop": 1756124696798, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "292a0ca0471df61c", "name": "测试总结", "source": "292a0ca0471df61c.txt", "type": "text/plain", "size": 335}, {"uid": "2641a3f7c119b909", "name": "test_completed", "source": "2641a3f7c119b909.png", "type": "image/png", "size": 181267}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124696798, "stop": 1756124696799, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124696799, "stop": 1756124696799, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124696799, "stop": 1756124697040, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "e03deab6c2363c97", "name": "测试总结", "source": "e03deab6c2363c97.txt", "type": "text/plain", "size": 335}, {"uid": "ada05e24a658fbc2", "name": "test_completed", "source": "ada05e24a658fbc2.png", "type": "image/png", "size": 181267}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9f8cccbed0425bfe", "name": "stdout", "source": "9f8cccbed0425bfe.txt", "type": "text/plain", "size": 13609}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124697041, "stop": 1756124697041, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124697042, "stop": 1756124698429, "duration": 1387}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_the_volume_to_the_maximun"}, {"name": "subSuite", "value": "TestEllaIncreaseVolumeMaximun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_the_volume_to_the_maximun"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e8a84c64a920e329.json", "parameterValues": []}