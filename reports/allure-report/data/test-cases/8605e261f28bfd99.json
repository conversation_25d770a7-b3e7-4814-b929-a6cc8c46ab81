{"uid": "8605e261f28bfd99", "name": "测试who is harry potter能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_harry_potter.TestEllaWhoIsHarryPotter#test_who_is_harry_potter", "historyId": "56a09613cdb882018377e1c2c4e78472", "time": {"start": 1756122596686, "stop": 1756122625176, "duration": 28490}, "description": "who is harry potter", "descriptionHtml": "<p>who is harry potter</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122583974, "stop": 1756122596685, "duration": 12711}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122596685, "stop": 1756122596685, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "who is harry potter", "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1756122596686, "stop": 1756122624887, "duration": 28201}, "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1756122596686, "stop": 1756122624618, "duration": 27932}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122624618, "stop": 1756122624886, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "b3f16109247233e6", "name": "测试总结", "source": "b3f16109247233e6.txt", "type": "text/plain", "size": 1247}, {"uid": "21541afdc246b555", "name": "test_completed", "source": "21541afdc246b555.png", "type": "image/png", "size": 222489}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122624887, "stop": 1756122624888, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122624889, "stop": 1756122625176, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "2a4a3940e1c603b1", "name": "测试总结", "source": "2a4a3940e1c603b1.txt", "type": "text/plain", "size": 1247}, {"uid": "4b682ec69534e773", "name": "test_completed", "source": "4b682ec69534e773.png", "type": "image/png", "size": 222917}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8047c149d4177d49", "name": "stdout", "source": "8047c149d4177d49.txt", "type": "text/plain", "size": 17017}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122625177, "stop": 1756122625177, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122625178, "stop": 1756122626577, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_harry_potter"}, {"name": "subSuite", "value": "TestEllaWhoIsHarry<PERSON>otter"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_harry_potter"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8605e261f28bfd99.json", "parameterValues": []}