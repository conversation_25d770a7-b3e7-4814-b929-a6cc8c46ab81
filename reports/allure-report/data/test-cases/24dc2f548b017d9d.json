{"uid": "24dc2f548b017d9d", "name": "测试disable call on hold返回正确的不支持响应", "fullName": "testcases.test_ella.component_coupling.test_disable_call_on_hold.TestEllaDisableCallHold#test_disable_call_on_hold", "historyId": "8bf93fd8ac952a757cc694ecc78b8d51", "time": {"start": 1756117502192, "stop": 1756117537590, "duration": 35398}, "description": "验证disable call on hold指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable call on hold指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117489531, "stop": 1756117502188, "duration": 12657}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117502188, "stop": 1756117502188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable call on hold指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable call on hold", "time": {"start": 1756117502192, "stop": 1756117537360, "duration": 35168}, "status": "passed", "steps": [{"name": "执行命令: disable call on hold", "time": {"start": 1756117502192, "stop": 1756117537091, "duration": 34899}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117537091, "stop": 1756117537359, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "c8c05afbaa886e52", "name": "测试总结", "source": "c8c05afbaa886e52.txt", "type": "text/plain", "size": 334}, {"uid": "6d7b30aa07637b0f", "name": "test_completed", "source": "6d7b30aa07637b0f.png", "type": "image/png", "size": 180841}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117537360, "stop": 1756117537360, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117537360, "stop": 1756117537589, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "2549ae2fb5c2e4ec", "name": "测试总结", "source": "2549ae2fb5c2e4ec.txt", "type": "text/plain", "size": 334}, {"uid": "78fea5ed302805da", "name": "test_completed", "source": "78fea5ed302805da.png", "type": "image/png", "size": 180939}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b727f7f69d531143", "name": "stdout", "source": "b727f7f69d531143.txt", "type": "text/plain", "size": 12885}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117537591, "stop": 1756117537591, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117537593, "stop": 1756117539042, "duration": 1449}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_disable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaDisableCallHold"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_disable_call_on_hold"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "24dc2f548b017d9d.json", "parameterValues": []}