{"uid": "54ccd77f5f918930", "name": "测试previous song能正常执行", "fullName": "testcases.test_ella.dialogue.test_previous_song.TestEllaHowIsWeatherToday#test_previous_song", "historyId": "d304f4e805a15a0351109cc931c26ffc", "time": {"start": 1756121160993, "stop": 1756121186650, "duration": 25657}, "description": "previous song", "descriptionHtml": "<p>previous song</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121148020, "stop": 1756121160991, "duration": 12971}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121160991, "stop": 1756121160991, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "previous song", "status": "passed", "steps": [{"name": "执行命令: previous song", "time": {"start": 1756121160993, "stop": 1756121186435, "duration": 25442}, "status": "passed", "steps": [{"name": "执行命令: previous song", "time": {"start": 1756121160993, "stop": 1756121186190, "duration": 25197}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121186190, "stop": 1756121186434, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "59ff6b6e1351c21", "name": "测试总结", "source": "59ff6b6e1351c21.txt", "type": "text/plain", "size": 292}, {"uid": "154633d7762759b9", "name": "test_completed", "source": "154633d7762759b9.png", "type": "image/png", "size": 180631}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121186435, "stop": 1756121186437, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121186437, "stop": 1756121186649, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "be085409ae4aa8c9", "name": "测试总结", "source": "be085409ae4aa8c9.txt", "type": "text/plain", "size": 292}, {"uid": "395bc50f26bbdee9", "name": "test_completed", "source": "395bc50f26bbdee9.png", "type": "image/png", "size": 180631}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6398141667b7bec7", "name": "stdout", "source": "6398141667b7bec7.txt", "type": "text/plain", "size": 12465}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121186650, "stop": 1756121186650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121186651, "stop": 1756121188060, "duration": 1409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_previous_song"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_previous_song"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "54ccd77f5f918930.json", "parameterValues": []}