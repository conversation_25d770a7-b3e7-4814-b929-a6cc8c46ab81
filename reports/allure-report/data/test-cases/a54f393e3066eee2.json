{"uid": "a54f393e3066eee2", "name": "测试jump to battery and power saving返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving#test_jump_to_battery_and_power_saving", "historyId": "436193bc4e8c44d21b6520da0589f88d", "time": {"start": 1756133664279, "stop": 1756133699271, "duration": 34992}, "description": "验证jump to battery and power saving指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to battery and power saving指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to battery and power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 12 h 24 m | 94% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 28 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving object at 0x0000029205390B90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209689610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_battery_and_power_saving(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to battery and power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 12 h 24 m | 94% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 28 m | App Power Consumption Rankings | Battery & Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_battery_and_power_saving.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133651558, "stop": 1756133664278, "duration": 12720}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133664278, "stop": 1756133664278, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to battery and power saving指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to battery and power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 12 h 24 m | 94% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 28 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving object at 0x0000029205390B90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209689610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_battery_and_power_saving(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to battery and power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 12 h 24 m | 94% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 28 m | App Power Consumption Rankings | Battery & Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_battery_and_power_saving.py:33: AssertionError", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1756133664279, "stop": 1756133699265, "duration": 34986}, "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1756133664279, "stop": 1756133698983, "duration": 34704}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133698983, "stop": 1756133699264, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "44029bb7d56312eb", "name": "测试总结", "source": "44029bb7d56312eb.txt", "type": "text/plain", "size": 561}, {"uid": "c8471920b38109c6", "name": "test_completed", "source": "c8471920b38109c6.png", "type": "image/png", "size": 181277}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133699265, "stop": 1756133699268, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to battery and power saving', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] 12 h 24 m | 94% | Charging | Current Mode | Equilibrium Mode | Power Saving | Battery Health & Charging | Battery Usage In Last 24 H | Screen-On Time | 2 h 28 m | App Power Consumption Rankings | Battery & Power Saving']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_battery_and_power_saving.py\", line 33, in test_jump_to_battery_and_power_saving\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6c5cd04e1de2b72e", "name": "stdout", "source": "6c5cd04e1de2b72e.txt", "type": "text/plain", "size": 16764}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133699281, "stop": 1756133699522, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "8e5410279ba07e0d", "name": "失败截图-TestEllaJumpBatteryPowerSaving", "source": "8e5410279ba07e0d.png", "type": "image/png", "size": 181104}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133699523, "stop": 1756133700928, "duration": 1405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_and_power_saving"}, {"name": "subSuite", "value": "TestEllaJumpBatteryPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a54f393e3066eee2.json", "parameterValues": []}