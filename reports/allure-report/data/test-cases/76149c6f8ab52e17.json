{"uid": "76149c6f8ab52e17", "name": "测试disable magic voice changer返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "503ff57584874e8387e6b367bfa70c8c", "time": {"start": 1756130993392, "stop": 1756131020068, "duration": 26676}, "description": "验证disable magic voice changer指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable magic voice changer指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130979620, "stop": 1756130993391, "duration": 13771}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130993391, "stop": 1756130993391, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable magic voice changer指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1756130993392, "stop": 1756131019827, "duration": 26435}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1756130993392, "stop": 1756131019581, "duration": 26189}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131019581, "stop": 1756131019827, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "f2ba56067ae16ac4", "name": "测试总结", "source": "f2ba56067ae16ac4.txt", "type": "text/plain", "size": 348}, {"uid": "d16a2a26a501d447", "name": "test_completed", "source": "d16a2a26a501d447.png", "type": "image/png", "size": 196782}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131019827, "stop": 1756131019828, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131019828, "stop": 1756131020066, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "cb306053cbdc8950", "name": "测试总结", "source": "cb306053cbdc8950.txt", "type": "text/plain", "size": 348}, {"uid": "ea9641beac5acb99", "name": "test_completed", "source": "ea9641beac5acb99.png", "type": "image/png", "size": 196782}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "131745eacea68e09", "name": "stdout", "source": "131745eacea68e09.txt", "type": "text/plain", "size": 12659}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131020077, "stop": 1756131020077, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131020079, "stop": 1756131021499, "duration": 1420}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "76149c6f8ab52e17.json", "parameterValues": []}