{"uid": "dca09a10dbbc4e94", "name": "测试make a call能正常执行", "fullName": "testcases.test_ella.dialogue.test_make_a_call.TestEllaMakeCall#test_make_a_call", "historyId": "2428ad915810150c12838b88ee13f49c", "time": {"start": 1756120501145, "stop": 1756120535370, "duration": 34225}, "description": "make a call", "descriptionHtml": "<p>make a call</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120488328, "stop": 1756120501143, "duration": 12815}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120501143, "stop": 1756120501143, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "make a call", "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1756120501145, "stop": 1756120535137, "duration": 33992}, "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1756120501146, "stop": 1756120534847, "duration": 33701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120534847, "stop": 1756120535136, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "bc3cfd0cebeab255", "name": "测试总结", "source": "bc3cfd0cebeab255.txt", "type": "text/plain", "size": 282}, {"uid": "90fca4c7c364e022", "name": "test_completed", "source": "90fca4c7c364e022.png", "type": "image/png", "size": 180160}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120535137, "stop": 1756120535139, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120535139, "stop": 1756120535370, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "93f4a45f79f22141", "name": "测试总结", "source": "93f4a45f79f22141.txt", "type": "text/plain", "size": 282}, {"uid": "746f301032a86447", "name": "test_completed", "source": "746f301032a86447.png", "type": "image/png", "size": 180160}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6158a37aeb3dc8", "name": "stdout", "source": "6158a37aeb3dc8.txt", "type": "text/plain", "size": 12802}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120535371, "stop": 1756120535371, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120535373, "stop": 1756120536771, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_make_a_call"}, {"name": "subSuite", "value": "TestEllaMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_make_a_call"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dca09a10dbbc4e94.json", "parameterValues": []}