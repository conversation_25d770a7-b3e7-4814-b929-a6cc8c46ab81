{"uid": "1386042e65edf623", "name": "测试help me write an email能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email.TestEllaHelpMeWriteAnEmail#test_help_me_write_an_email", "historyId": "70c9bd8c4aab57e96eb06acb93ca2223", "time": {"start": 1756132939284, "stop": 1756132968275, "duration": 28991}, "description": "help me write an email", "descriptionHtml": "<p>help me write an email</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132926606, "stop": 1756132939283, "duration": 12677}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132939283, "stop": 1756132939283, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "help me write an email", "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1756132939284, "stop": 1756132968030, "duration": 28746}, "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1756132939284, "stop": 1756132967749, "duration": 28465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132967749, "stop": 1756132968028, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "c545f1aec88e8d4b", "name": "测试总结", "source": "c545f1aec88e8d4b.txt", "type": "text/plain", "size": 898}, {"uid": "f43a494a3801c2c2", "name": "test_completed", "source": "f43a494a3801c2c2.png", "type": "image/png", "size": 208275}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132968030, "stop": 1756132968039, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132968039, "stop": 1756132968273, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "3cdcccd4322373db", "name": "测试总结", "source": "3cdcccd4322373db.txt", "type": "text/plain", "size": 898}, {"uid": "74431d8ab2a0b3d9", "name": "test_completed", "source": "74431d8ab2a0b3d9.png", "type": "image/png", "size": 208237}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "398c4733d22a2200", "name": "stdout", "source": "398c4733d22a2200.txt", "type": "text/plain", "size": 15862}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132968276, "stop": 1756132968276, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132968278, "stop": 1756132969672, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1386042e65edf623.json", "parameterValues": []}