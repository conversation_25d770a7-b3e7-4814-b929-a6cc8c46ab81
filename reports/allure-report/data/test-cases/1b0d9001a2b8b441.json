{"uid": "1b0d9001a2b8b441", "name": "测试phone boost能正常执行", "fullName": "testcases.test_ella.component_coupling.test_phone_boost.TestEllaPhoneBoost#test_phone_boost", "historyId": "762b1ab748e39965c1484eb7fe38bfe4", "time": {"start": 1756118182887, "stop": 1756118208772, "duration": 25885}, "description": "phone boost", "descriptionHtml": "<p>phone boost</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118169690, "stop": 1756118182885, "duration": 13195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118182885, "stop": 1756118182885, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "phone boost", "status": "passed", "steps": [{"name": "执行命令: phone boost", "time": {"start": 1756118182887, "stop": 1756118208523, "duration": 25636}, "status": "passed", "steps": [{"name": "执行命令: phone boost", "time": {"start": 1756118182887, "stop": 1756118208220, "duration": 25333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118208220, "stop": 1756118208523, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "3924e4e646fba83a", "name": "测试总结", "source": "3924e4e646fba83a.txt", "type": "text/plain", "size": 258}, {"uid": "67816876144004bd", "name": "test_completed", "source": "67816876144004bd.png", "type": "image/png", "size": 173285}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118208524, "stop": 1756118208525, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118208525, "stop": 1756118208771, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "2a416313205aa34d", "name": "测试总结", "source": "2a416313205aa34d.txt", "type": "text/plain", "size": 258}, {"uid": "2c0cc91fe342f7aa", "name": "test_completed", "source": "2c0cc91fe342f7aa.png", "type": "image/png", "size": 173285}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cbd7d3c68cd69531", "name": "stdout", "source": "cbd7d3c68cd69531.txt", "type": "text/plain", "size": 12312}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118208772, "stop": 1756118208772, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118208775, "stop": 1756118210140, "duration": 1365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_phone_boost"}, {"name": "subSuite", "value": "TestEllaPhoneBoost"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_phone_boost"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1b0d9001a2b8b441.json", "parameterValues": []}