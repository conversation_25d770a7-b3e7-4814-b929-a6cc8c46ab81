{"uid": "8a929c78d7c2559e", "name": "测试next music能正常执行", "fullName": "testcases.test_ella.dialogue.test_next_music.TestEllaHowIsWeatherToday#test_next_music", "historyId": "b236a18e19a6aa2218c85b80afef2746", "time": {"start": 1756120629972, "stop": 1756120655668, "duration": 25696}, "description": "next music", "descriptionHtml": "<p>next music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120616943, "stop": 1756120629971, "duration": 13028}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120629971, "stop": 1756120629971, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "next music", "status": "passed", "steps": [{"name": "执行命令: next music", "time": {"start": 1756120629972, "stop": 1756120655425, "duration": 25453}, "status": "passed", "steps": [{"name": "执行命令: next music", "time": {"start": 1756120629973, "stop": 1756120655147, "duration": 25174}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120655148, "stop": 1756120655424, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "a70e4b0195565cad", "name": "测试总结", "source": "a70e4b0195565cad.txt", "type": "text/plain", "size": 286}, {"uid": "b96ef94525895f53", "name": "test_completed", "source": "b96ef94525895f53.png", "type": "image/png", "size": 178117}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120655425, "stop": 1756120655426, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120655426, "stop": 1756120655667, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "333a2f2a7a0e39ee", "name": "测试总结", "source": "333a2f2a7a0e39ee.txt", "type": "text/plain", "size": 286}, {"uid": "1f4bb6457a090503", "name": "test_completed", "source": "1f4bb6457a090503.png", "type": "image/png", "size": 178117}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7dc20bbd417a46ae", "name": "stdout", "source": "7dc20bbd417a46ae.txt", "type": "text/plain", "size": 12704}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120655669, "stop": 1756120655669, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120655671, "stop": 1756120657026, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_next_music"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_next_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8a929c78d7c2559e.json", "parameterValues": []}