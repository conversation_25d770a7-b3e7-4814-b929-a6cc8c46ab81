{"uid": "92fd416efb820ca9", "name": "测试check ram information", "fullName": "testcases.test_ella.unsupported_commands.test_check_ram_information.TestEllaOpenPlayPoliticalNews#test_check_system_update", "historyId": "11875095b9997cfc7edbe407c3074b7e", "time": {"start": 1756130362620, "stop": 1756130402064, "duration": 39444}, "description": "测试check ram information指令", "descriptionHtml": "<p>测试check ram information指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130349783, "stop": 1756130362619, "duration": 12836}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130362619, "stop": 1756130362619, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试check ram information指令", "status": "passed", "steps": [{"name": "执行命令: check ram information", "time": {"start": 1756130362620, "stop": 1756130401842, "duration": 39222}, "status": "passed", "steps": [{"name": "执行命令: check ram information", "time": {"start": 1756130362620, "stop": 1756130401550, "duration": 38930}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130401550, "stop": 1756130401842, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "5f7a185e9e466717", "name": "测试总结", "source": "5f7a185e9e466717.txt", "type": "text/plain", "size": 608}, {"uid": "969e24f451e76d2d", "name": "test_completed", "source": "969e24f451e76d2d.png", "type": "image/png", "size": 171477}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130401842, "stop": 1756130401844, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130401844, "stop": 1756130402064, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "9fa509ba8ffc7bd9", "name": "测试总结", "source": "9fa509ba8ffc7bd9.txt", "type": "text/plain", "size": 608}, {"uid": "1916eb305fc918cf", "name": "test_completed", "source": "1916eb305fc918cf.png", "type": "image/png", "size": 171517}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3723884432aa47cc", "name": "stdout", "source": "3723884432aa47cc.txt", "type": "text/plain", "size": 14960}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130402065, "stop": 1756130402065, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130402066, "stop": 1756130403490, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_ram_information"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_ram_information"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "92fd416efb820ca9.json", "parameterValues": []}