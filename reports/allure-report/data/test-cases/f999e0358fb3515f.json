{"uid": "f999e0358fb3515f", "name": "测试switch charging modes能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_charging_modes.TestEllaSwitchChargingModes#test_switch_charging_modes", "historyId": "a4af452e0448ec3c1ecc9afcc30459be", "time": {"start": 1756126502277, "stop": 1756126527964, "duration": 25687}, "description": "switch charging modes", "descriptionHtml": "<p>switch charging modes</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126488815, "stop": 1756126502276, "duration": 13461}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126502276, "stop": 1756126502276, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch charging modes", "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "time": {"start": 1756126502277, "stop": 1756126527725, "duration": 25448}, "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "time": {"start": 1756126502277, "stop": 1756126527451, "duration": 25174}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126527451, "stop": 1756126527724, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "d58064d68464f671", "name": "测试总结", "source": "d58064d68464f671.txt", "type": "text/plain", "size": 395}, {"uid": "af65fc552e158fb4", "name": "test_completed", "source": "af65fc552e158fb4.png", "type": "image/png", "size": 197280}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126527725, "stop": 1756126527727, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126527727, "stop": 1756126527963, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "7967b0a5b29cca23", "name": "测试总结", "source": "7967b0a5b29cca23.txt", "type": "text/plain", "size": 395}, {"uid": "49f5769b3b30b1d1", "name": "test_completed", "source": "49f5769b3b30b1d1.png", "type": "image/png", "size": 197280}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "227f8678f4049db6", "name": "stdout", "source": "227f8678f4049db6.txt", "type": "text/plain", "size": 12999}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126527964, "stop": 1756126527964, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126527976, "stop": 1756126529367, "duration": 1391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_charging_modes"}, {"name": "subSuite", "value": "TestEllaSwitchChargingModes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_charging_modes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f999e0358fb3515f.json", "parameterValues": []}