{"uid": "abf6699b6dd7fe35", "name": "测试take notes on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse.TestEllaTakeNotesHowBuildTreehouse#test_take_notes_on_how_to_build_a_treehouse", "historyId": "772728b3468560788490a3673352724d", "time": {"start": 1756121947128, "stop": 1756121977482, "duration": 30354}, "description": "take notes on how to build a treehouse", "descriptionHtml": "<p>take notes on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121933801, "stop": 1756121947125, "duration": 13324}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121947125, "stop": 1756121947126, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take notes on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1756121947128, "stop": 1756121977225, "duration": 30097}, "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1756121947128, "stop": 1756121976958, "duration": 29830}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121976958, "stop": 1756121977225, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "457a300e727bafd2", "name": "测试总结", "source": "457a300e727bafd2.txt", "type": "text/plain", "size": 345}, {"uid": "805d65c7baba270f", "name": "test_completed", "source": "805d65c7baba270f.png", "type": "image/png", "size": 161525}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121977225, "stop": 1756121977229, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121977229, "stop": 1756121977481, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "90bb640450dae11f", "name": "测试总结", "source": "90bb640450dae11f.txt", "type": "text/plain", "size": 345}, {"uid": "28dfcf74840dcc71", "name": "test_completed", "source": "28dfcf74840dcc71.png", "type": "image/png", "size": 161993}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c1e57f8ff1e16b02", "name": "stdout", "source": "c1e57f8ff1e16b02.txt", "type": "text/plain", "size": 13140}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121977484, "stop": 1756121977484, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121977486, "stop": 1756121978899, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_notes_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNotesHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "abf6699b6dd7fe35.json", "parameterValues": []}