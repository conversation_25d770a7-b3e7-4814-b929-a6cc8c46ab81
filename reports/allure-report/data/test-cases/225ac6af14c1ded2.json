{"uid": "225ac6af14c1ded2", "name": "测试turn up the volume to the max能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_up_the_volume_to_the_max.TestEllaTurnUpVolumeMax#test_turn_up_the_volume_to_the_max", "historyId": "1470cf4116a3328d5a8812cd15bb56f8", "time": {"start": 1756128591662, "stop": 1756128617777, "duration": 26115}, "description": "turn up the volume to the max", "descriptionHtml": "<p>turn up the volume to the max</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128578885, "stop": 1756128591661, "duration": 12776}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128591661, "stop": 1756128591661, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn up the volume to the max", "status": "passed", "steps": [{"name": "执行命令: turn up the volume to the max", "time": {"start": 1756128591662, "stop": 1756128617563, "duration": 25901}, "status": "passed", "steps": [{"name": "执行命令: turn up the volume to the max", "time": {"start": 1756128591663, "stop": 1756128617319, "duration": 25656}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128617319, "stop": 1756128617563, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "564caafdaedaddf4", "name": "测试总结", "source": "564caafdaedaddf4.txt", "type": "text/plain", "size": 325}, {"uid": "27c4ce89f3f64123", "name": "test_completed", "source": "27c4ce89f3f64123.png", "type": "image/png", "size": 181651}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128617563, "stop": 1756128617564, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128617565, "stop": 1756128617565, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128617565, "stop": 1756128617776, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "6198f90e54d68fa2", "name": "测试总结", "source": "6198f90e54d68fa2.txt", "type": "text/plain", "size": 325}, {"uid": "3cddcfa1a57420a9", "name": "test_completed", "source": "3cddcfa1a57420a9.png", "type": "image/png", "size": 181651}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eab4c9ec03318165", "name": "stdout", "source": "eab4c9ec03318165.txt", "type": "text/plain", "size": 13552}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128617777, "stop": 1756128617777, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128617778, "stop": 1756128619168, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_the_volume_to_the_max"}, {"name": "subSuite", "value": "TestEllaTurnUpVolumeMax"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_the_volume_to_the_max"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "225ac6af14c1ded2.json", "parameterValues": []}