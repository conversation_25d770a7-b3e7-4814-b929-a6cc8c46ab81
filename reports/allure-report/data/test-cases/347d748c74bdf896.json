{"uid": "347d748c74bdf896", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings#test_jump_to_high_brightness_mode_settings", "historyId": "48a2a80bfed06f0c82b99a0aaa26e252", "time": {"start": 1756133812385, "stop": 1756133847847, "duration": 35462}, "description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to high brightness mode settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to high brightness mode settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings object at 0x00000292053B1D10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096D9610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_high_brightness_mode_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to high brightness mode settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_high_brightness_mode_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133799377, "stop": 1756133812384, "duration": 13007}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133812384, "stop": 1756133812384, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to high brightness mode settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings object at 0x00000292053B1D10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096D9610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_high_brightness_mode_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to high brightness mode settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_high_brightness_mode_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1756133812385, "stop": 1756133847842, "duration": 35457}, "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1756133812385, "stop": 1756133847557, "duration": 35172}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133847557, "stop": 1756133847840, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "8711c96873400428", "name": "测试总结", "source": "8711c96873400428.txt", "type": "text/plain", "size": 704}, {"uid": "637e616e5d413084", "name": "test_completed", "source": "637e616e5d413084.png", "type": "image/png", "size": 180995}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133847842, "stop": 1756133847846, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to high brightness mode settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_high_brightness_mode_settings.py\", line 33, in test_jump_to_high_brightness_mode_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fe0d715688ca445b", "name": "stdout", "source": "fe0d715688ca445b.txt", "type": "text/plain", "size": 17228}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133847852, "stop": 1756133848087, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "f55323e18d2688f8", "name": "失败截图-TestEllaJumpHighBrightnessModeSettings", "source": "f55323e18d2688f8.png", "type": "image/png", "size": 180890}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133848090, "stop": 1756133849587, "duration": 1497}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_high_brightness_mode_settings"}, {"name": "subSuite", "value": "TestEllaJumpHighBrightnessModeSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "347d748c74bdf896.json", "parameterValues": []}