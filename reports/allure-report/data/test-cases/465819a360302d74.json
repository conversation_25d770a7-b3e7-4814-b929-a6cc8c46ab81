{"uid": "465819a360302d74", "name": "测试play news", "fullName": "testcases.test_ella.dialogue.test_play_news.TestEllaOpenPlayNews#test_play_news", "historyId": "fee3033814a8b17ff8c8abe6bbcdc839", "time": {"start": 1756121072568, "stop": 1756121102797, "duration": 30229}, "description": "测试play news指令", "descriptionHtml": "<p>测试play news指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121059700, "stop": 1756121072567, "duration": 12867}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121072567, "stop": 1756121072567, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play news指令", "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1756121072568, "stop": 1756121102562, "duration": 29994}, "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1756121072568, "stop": 1756121102257, "duration": 29689}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121102257, "stop": 1756121102561, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "86aa32042aa8ac52", "name": "测试总结", "source": "86aa32042aa8ac52.txt", "type": "text/plain", "size": 1411}, {"uid": "d080227c7de29ee3", "name": "test_completed", "source": "d080227c7de29ee3.png", "type": "image/png", "size": 252187}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121102562, "stop": 1756121102566, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121102566, "stop": 1756121102797, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "759801ba0831d5f", "name": "测试总结", "source": "759801ba0831d5f.txt", "type": "text/plain", "size": 1411}, {"uid": "6e2288a02779f75d", "name": "test_completed", "source": "6e2288a02779f75d.png", "type": "image/png", "size": 251667}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a2abf38b5c2ba073", "name": "stdout", "source": "a2abf38b5c2ba073.txt", "type": "text/plain", "size": 18095}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121102798, "stop": 1756121102798, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121102799, "stop": 1756121104192, "duration": 1393}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "465819a360302d74.json", "parameterValues": []}