{"uid": "fc8fb7c0ee87bee4", "name": "测试set flip case feature返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature.TestEllaSetFlipCaseFeature#test_set_flip_case_feature", "historyId": "3a27fef360a79f638f96f0461df262da", "time": {"start": 1756136473290, "stop": 1756136499872, "duration": 26582}, "description": "验证set flip case feature指令返回预期的不支持响应", "descriptionHtml": "<p>验证set flip case feature指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136460481, "stop": 1756136473289, "duration": 12808}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136473289, "stop": 1756136473289, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set flip case feature指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set flip case feature", "time": {"start": 1756136473290, "stop": 1756136499650, "duration": 26360}, "status": "passed", "steps": [{"name": "执行命令: set flip case feature", "time": {"start": 1756136473290, "stop": 1756136499405, "duration": 26115}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136499406, "stop": 1756136499649, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "e4d142d8c51c08e8", "name": "测试总结", "source": "e4d142d8c51c08e8.txt", "type": "text/plain", "size": 342}, {"uid": "43da9e4bca1bc0b2", "name": "test_completed", "source": "43da9e4bca1bc0b2.png", "type": "image/png", "size": 181776}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136499650, "stop": 1756136499652, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136499652, "stop": 1756136499871, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "bcb9caedb6c36d75", "name": "测试总结", "source": "bcb9caedb6c36d75.txt", "type": "text/plain", "size": 342}, {"uid": "3a39f3eef72cec80", "name": "test_completed", "source": "3a39f3eef72cec80.png", "type": "image/png", "size": 181776}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "585c4b9813e1aa50", "name": "stdout", "source": "585c4b9813e1aa50.txt", "type": "text/plain", "size": 12611}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136499873, "stop": 1756136499873, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136499874, "stop": 1756136501360, "duration": 1486}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flip_case_feature"}, {"name": "subSuite", "value": "TestEllaSetFlipCaseFeature"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fc8fb7c0ee87bee4.json", "parameterValues": []}