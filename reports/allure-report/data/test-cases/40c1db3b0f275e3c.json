{"uid": "40c1db3b0f275e3c", "name": "测试play music by visha", "fullName": "testcases.test_ella.dialogue.test_play_music_by_visha.TestEllaOpenPlayPoliticalNews#test_play_music_by_visha", "historyId": "85e8e199dba3ac2c9d89e132805a4404", "time": {"start": 1756120875174, "stop": 1756120917271, "duration": 42097}, "description": "测试play music by visha指令", "descriptionHtml": "<p>测试play music by visha指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120862262, "stop": 1756120875173, "duration": 12911}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120875173, "stop": 1756120875173, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music by visha指令", "status": "passed", "steps": [{"name": "执行命令: play music by visha", "time": {"start": 1756120875174, "stop": 1756120917041, "duration": 41867}, "status": "passed", "steps": [{"name": "执行命令: play music by visha", "time": {"start": 1756120875174, "stop": 1756120916782, "duration": 41608}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120916782, "stop": 1756120917041, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "c971459d3cfcc850", "name": "测试总结", "source": "c971459d3cfcc850.txt", "type": "text/plain", "size": 612}, {"uid": "37d37d4b4b3f552a", "name": "test_completed", "source": "37d37d4b4b3f552a.png", "type": "image/png", "size": 181302}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120917041, "stop": 1756120917043, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120917043, "stop": 1756120917271, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "7acb5440dae8c956", "name": "测试总结", "source": "7acb5440dae8c956.txt", "type": "text/plain", "size": 612}, {"uid": "bfcfcfcd70e40178", "name": "test_completed", "source": "bfcfcfcd70e40178.png", "type": "image/png", "size": 181349}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8661a5dd9706acdc", "name": "stdout", "source": "8661a5dd9706acdc.txt", "type": "text/plain", "size": 15877}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756120917272, "stop": 1756120918666, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756120917272, "stop": 1756120917272, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_visha"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_visha"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "40c1db3b0f275e3c.json", "parameterValues": []}