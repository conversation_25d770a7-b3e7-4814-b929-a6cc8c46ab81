{"uid": "f6705b301d7cdc02", "name": "测试Search for addresses on the screen能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen#test_search_for_addresses_on_the_screen", "historyId": "8814f1dafa698e785ee1f58faa6e745d", "time": {"start": 1756135776828, "stop": 1756135804493, "duration": 27665}, "description": "Search for addresses on the screen", "descriptionHtml": "<p>Search for addresses on the screen</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135764035, "stop": 1756135776826, "duration": 12791}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135776826, "stop": 1756135776826, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Search for addresses on the screen", "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1756135776828, "stop": 1756135804247, "duration": 27419}, "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1756135776828, "stop": 1756135804006, "duration": 27178}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135804006, "stop": 1756135804247, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "d62741dd10ae90e5", "name": "测试总结", "source": "d62741dd10ae90e5.txt", "type": "text/plain", "size": 738}, {"uid": "d7594025a1a40840", "name": "test_completed", "source": "d7594025a1a40840.png", "type": "image/png", "size": 184389}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135804247, "stop": 1756135804248, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135804248, "stop": 1756135804492, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "77d1d88b36adf96b", "name": "测试总结", "source": "77d1d88b36adf96b.txt", "type": "text/plain", "size": 738}, {"uid": "7db8dbc1e03bb325", "name": "test_completed", "source": "7db8dbc1e03bb325.png", "type": "image/png", "size": 184095}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4449de8772184f54", "name": "stdout", "source": "4449de8772184f54.txt", "type": "text/plain", "size": 14810}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135804494, "stop": 1756135804494, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135804494, "stop": 1756135805975, "duration": 1481}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_for_addresses_on_the_screen"}, {"name": "subSuite", "value": "TestEllaSearchAddressesScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f6705b301d7cdc02.json", "parameterValues": []}