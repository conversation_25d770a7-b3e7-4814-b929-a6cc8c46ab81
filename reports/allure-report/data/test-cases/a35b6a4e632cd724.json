{"uid": "a35b6a4e632cd724", "name": "测试set an alarm at 8 am", "fullName": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am.TestEllaOpenClock#test_set_an_alarm_at_8_am", "historyId": "de0ed312f350c708e7a00bb74aeaac0f", "time": {"start": 1756118682606, "stop": 1756118761651, "duration": 79045}, "description": "测试set an alarm at 8 am指令", "descriptionHtml": "<p>测试set an alarm at 8 am指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118669630, "stop": 1756118682605, "duration": 12975}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118682605, "stop": 1756118682605, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试set an alarm at 8 am指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118682606, "stop": 1756118708663, "duration": 26057}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118682606, "stop": 1756118708384, "duration": 25778}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118708384, "stop": 1756118708661, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "fadd02936834a1e6", "name": "测试总结", "source": "fadd02936834a1e6.txt", "type": "text/plain", "size": 303}, {"uid": "12161c56ca8f77ad", "name": "test_completed", "source": "12161c56ca8f77ad.png", "type": "image/png", "size": 174960}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756118708663, "stop": 1756118734945, "duration": 26282}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756118708663, "stop": 1756118734705, "duration": 26042}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118734706, "stop": 1756118734945, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "59f924e3b8bf013", "name": "测试总结", "source": "59f924e3b8bf013.txt", "type": "text/plain", "size": 242}, {"uid": "72c06f7acc935a7b", "name": "test_completed", "source": "72c06f7acc935a7b.png", "type": "image/png", "size": 148394}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756118734945, "stop": 1756118761427, "duration": 26482}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756118734945, "stop": 1756118761152, "duration": 26207}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118761152, "stop": 1756118761426, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "8a5046d85a685365", "name": "测试总结", "source": "8a5046d85a685365.txt", "type": "text/plain", "size": 223}, {"uid": "9fbc10e5a029780c", "name": "test_completed", "source": "9fbc10e5a029780c.png", "type": "image/png", "size": 145388}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118761427, "stop": 1756118761429, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118761429, "stop": 1756118761651, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "80c0f5a84ff7aab5", "name": "测试总结", "source": "80c0f5a84ff7aab5.txt", "type": "text/plain", "size": 225}, {"uid": "e4467e9251e8b01e", "name": "test_completed", "source": "e4467e9251e8b01e.png", "type": "image/png", "size": 145388}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "43864da3fcb2e37d", "name": "stdout", "source": "43864da3fcb2e37d.txt", "type": "text/plain", "size": 31698}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 11, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118761652, "stop": 1756118761652, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118761653, "stop": 1756118762962, "duration": 1309}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a35b6a4e632cd724.json", "parameterValues": []}