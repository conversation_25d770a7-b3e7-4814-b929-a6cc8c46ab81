{"uid": "39a5f8655d9b3b2f", "name": "测试set font size返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize#test_set_font_size", "historyId": "6c315a350a546e1382e435255d28245b", "time": {"start": 1756136603666, "stop": 1756136637280, "duration": 33614}, "description": "验证set font size指令返回预期的不支持响应", "descriptionHtml": "<p>验证set font size指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set font size', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize object at 0x00000292056F40D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8DE490>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_font_size(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set font size', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_font_size.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136590846, "stop": 1756136603665, "duration": 12819}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136603665, "stop": 1756136603665, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set font size指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set font size', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize object at 0x00000292056F40D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8DE490>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_font_size(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set font size', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_font_size.py:33: AssertionError", "steps": [{"name": "执行命令: set font size", "time": {"start": 1756136603666, "stop": 1756136637275, "duration": 33609}, "status": "passed", "steps": [{"name": "执行命令: set font size", "time": {"start": 1756136603666, "stop": 1756136637034, "duration": 33368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136637034, "stop": 1756136637275, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "c4e5e7f90b276996", "name": "测试总结", "source": "c4e5e7f90b276996.txt", "type": "text/plain", "size": 551}, {"uid": "a3a6710ce794c066", "name": "test_completed", "source": "a3a6710ce794c066.png", "type": "image/png", "size": 170452}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136637275, "stop": 1756136637278, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set font size', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Fonts | In 2021\\npapers published in Nature\\nand the Nature series journals\\nwere mentioned 16,479 times\\nin policy documents\\nand led to the conversion of 390,238 patents. | My Fonts | TransSans | Roboto | Font Size | Default | Font Weight | Default']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_font_size.py\", line 33, in test_set_font_size\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7a175fd2c3d6811e", "name": "stdout", "source": "7a175fd2c3d6811e.txt", "type": "text/plain", "size": 16048}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136637283, "stop": 1756136637523, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "ef29be20a3400088", "name": "失败截图-TestEllaSetFontSize", "source": "ef29be20a3400088.png", "type": "image/png", "size": 170393}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136637523, "stop": 1756136638985, "duration": 1462}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_font_size"}, {"name": "subSuite", "value": "TestEllaSetFontSize"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_font_size"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "39a5f8655d9b3b2f.json", "parameterValues": []}