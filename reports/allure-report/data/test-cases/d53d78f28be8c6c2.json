{"uid": "d53d78f28be8c6c2", "name": "测试Scan the QR code in the image 能正常执行", "fullName": "testcases.test_ella.self_function.test_scan_the_qr_code_in_the_image.TestEllaScanQrCodeImage#test_scan_the_qr_code_in_the_image", "historyId": "1498285b0d63f23df3a22c9c5262f7f3", "time": {"start": 1756123319655, "stop": 1756123437085, "duration": 117430}, "description": "Scan the QR code in the image ", "descriptionHtml": "<p>Scan the QR code in the image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123306996, "stop": 1756123319653, "duration": 12657}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123319653, "stop": 1756123319653, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Scan the QR code in the image ", "status": "passed", "steps": [{"name": "执行命令: Scan the QR code in the image ", "time": {"start": 1756123319655, "stop": 1756123436831, "duration": 117176}, "status": "passed", "steps": [{"name": "执行命令: Scan the QR code in the image ", "time": {"start": 1756123319655, "stop": 1756123436541, "duration": 116886}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123436541, "stop": 1756123436831, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "79ba8b0d0464e747", "name": "测试总结", "source": "79ba8b0d0464e747.txt", "type": "text/plain", "size": 604}, {"uid": "7810dbecd89aa770", "name": "test_completed", "source": "7810dbecd89aa770.png", "type": "image/png", "size": 221988}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123436831, "stop": 1756123436833, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123436833, "stop": 1756123437084, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "93a222df807d8541", "name": "测试总结", "source": "93a222df807d8541.txt", "type": "text/plain", "size": 558}, {"uid": "beb5eafa6a972e97", "name": "test_completed", "source": "beb5eafa6a972e97.png", "type": "image/png", "size": 221988}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "65753a14a3b78e4d", "name": "stdout", "source": "65753a14a3b78e4d.txt", "type": "text/plain", "size": 18683}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123437085, "stop": 1756123437085, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123437086, "stop": 1756123438407, "duration": 1321}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_scan_the_qr_code_in_the_image"}, {"name": "subSuite", "value": "TestEllaScanQrCodeImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_scan_the_qr_code_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "d53d78f28be8c6c2.json", "parameterValues": []}