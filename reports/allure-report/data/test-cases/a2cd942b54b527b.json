{"uid": "a2cd942b54b527b", "name": "测试jump to nfc settings", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings.TestEllaOpenPlayPoliticalNews#test_jump_to_nfc_settings", "historyId": "88a6294df5f2ab484e181b1f196ff253", "time": {"start": 1756133910625, "stop": 1756133943479, "duration": 32854}, "description": "测试jump to nfc settings指令", "descriptionHtml": "<p>测试jump to nfc settings指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133898061, "stop": 1756133910623, "duration": 12562}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133910624, "stop": 1756133910624, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试jump to nfc settings指令", "status": "passed", "steps": [{"name": "执行命令: jump to nfc settings", "time": {"start": 1756133910625, "stop": 1756133943240, "duration": 32615}, "status": "passed", "steps": [{"name": "执行命令: jump to nfc settings", "time": {"start": 1756133910625, "stop": 1756133942980, "duration": 32355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133942980, "stop": 1756133943239, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "257bf21edbeb3490", "name": "测试总结", "source": "257bf21edbeb3490.txt", "type": "text/plain", "size": 532}, {"uid": "2a0881d1d76a5342", "name": "test_completed", "source": "2a0881d1d76a5342.png", "type": "image/png", "size": 171259}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133943240, "stop": 1756133943241, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证settings已打开", "time": {"start": 1756133943241, "stop": 1756133943241, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133943241, "stop": 1756133943478, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "77d5cee64feacfb4", "name": "测试总结", "source": "77d5cee64feacfb4.txt", "type": "text/plain", "size": 532}, {"uid": "1079d44d08fd15e5", "name": "test_completed", "source": "1079d44d08fd15e5.png", "type": "image/png", "size": 171124}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "561167ffbc333cfb", "name": "stdout", "source": "561167ffbc333cfb.txt", "type": "text/plain", "size": 15717}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133943480, "stop": 1756133943480, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133943481, "stop": 1756133944900, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_nfc_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a2cd942b54b527b.json", "parameterValues": []}