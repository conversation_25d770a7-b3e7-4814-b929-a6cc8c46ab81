{"uid": "177c7595c88f7324", "name": "测试open the settings", "fullName": "testcases.test_ella.unsupported_commands.test_open_the_settings.TestEllaOpenPlayPoliticalNews#test_open_the_settings", "historyId": "c7b8111fa78410a413dfc969cfe6f0e1", "time": {"start": 1756134800472, "stop": 1756134837980, "duration": 37508}, "description": "测试open the settings指令", "descriptionHtml": "<p>测试open the settings指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134787158, "stop": 1756134800471, "duration": 13313}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134800471, "stop": 1756134800471, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open the settings指令", "status": "passed", "steps": [{"name": "执行命令: open the settings", "time": {"start": 1756134800472, "stop": 1756134837732, "duration": 37260}, "status": "passed", "steps": [{"name": "执行命令: open the settings", "time": {"start": 1756134800472, "stop": 1756134837446, "duration": 36974}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134837446, "stop": 1756134837731, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "d718ab3658347557", "name": "测试总结", "source": "d718ab3658347557.txt", "type": "text/plain", "size": 637}, {"uid": "1c0d8b45c1cffd40", "name": "test_completed", "source": "1c0d8b45c1cffd40.png", "type": "image/png", "size": 171971}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134837732, "stop": 1756134837733, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证settings已打开", "time": {"start": 1756134837733, "stop": 1756134837733, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134837733, "stop": 1756134837979, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "5ce988a089c7654b", "name": "测试总结", "source": "5ce988a089c7654b.txt", "type": "text/plain", "size": 637}, {"uid": "2ad2f623d23fee66", "name": "test_completed", "source": "2ad2f623d23fee66.png", "type": "image/png", "size": 171354}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "26ed47a27ac5353", "name": "stdout", "source": "26ed47a27ac5353.txt", "type": "text/plain", "size": 15875}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134837980, "stop": 1756134837980, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134837981, "stop": 1756134839515, "duration": 1534}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_the_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_the_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "177c7595c88f7324.json", "parameterValues": []}