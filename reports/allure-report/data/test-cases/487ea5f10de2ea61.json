{"uid": "487ea5f10de2ea61", "name": "测试Kinkaku-ji", "fullName": "testcases.test_ella.unsupported_commands.test_kinkaku_ji.TestEllaOpenPlayPoliticalNews#test_kinkaku_ji", "historyId": "223077c4b7372197db11e72856c8b7e6", "time": {"start": 1756134053491, "stop": 1756134081225, "duration": 27734}, "description": "测试Kinkaku-ji指令", "descriptionHtml": "<p>测试Kinkaku-ji指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134039927, "stop": 1756134053489, "duration": 13562}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134053489, "stop": 1756134053489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Kinkaku-ji指令", "status": "passed", "steps": [{"name": "执行命令: Kinkaku-ji", "time": {"start": 1756134053491, "stop": 1756134080949, "duration": 27458}, "status": "passed", "steps": [{"name": "执行命令: Kinkaku-ji", "time": {"start": 1756134053491, "stop": 1756134080687, "duration": 27196}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134080687, "stop": 1756134080947, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "c3086c33864a76b0", "name": "测试总结", "source": "c3086c33864a76b0.txt", "type": "text/plain", "size": 1339}, {"uid": "53d2b30af8e3475c", "name": "test_completed", "source": "53d2b30af8e3475c.png", "type": "image/png", "size": 233311}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134080949, "stop": 1756134080982, "duration": 33}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134080982, "stop": 1756134081224, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "83f72385f4e5641e", "name": "测试总结", "source": "83f72385f4e5641e.txt", "type": "text/plain", "size": 1339}, {"uid": "29286c1cbeb59a2f", "name": "test_completed", "source": "29286c1cbeb59a2f.png", "type": "image/png", "size": 233051}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a532c93b3958ee82", "name": "stdout", "source": "a532c93b3958ee82.txt", "type": "text/plain", "size": 16774}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134081225, "stop": 1756134081225, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134081227, "stop": 1756134082633, "duration": 1406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_kinkaku_ji"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_kinkaku_ji"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "487ea5f10de2ea61.json", "parameterValues": []}