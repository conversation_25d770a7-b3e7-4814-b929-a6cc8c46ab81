{"uid": "3a5bef4de5e19d5", "name": "测试introduce yourself能正常执行", "fullName": "testcases.test_ella.dialogue.test_introduce_yourself.TestEllaIntroduceYourself#test_introduce_yourself", "historyId": "a19924fb0a564cf26596907610c0f678", "time": {"start": 1756120377435, "stop": 1756120406306, "duration": 28871}, "description": "introduce yourself", "descriptionHtml": "<p>introduce yourself</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120364561, "stop": 1756120377433, "duration": 12872}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120377433, "stop": 1756120377433, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "introduce yourself", "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1756120377435, "stop": 1756120406059, "duration": 28624}, "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1756120377435, "stop": 1756120405799, "duration": 28364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120405799, "stop": 1756120406059, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "c62aba4dc25b2ebe", "name": "测试总结", "source": "c62aba4dc25b2ebe.txt", "type": "text/plain", "size": 829}, {"uid": "7514e8d013cbd9ee", "name": "test_completed", "source": "7514e8d013cbd9ee.png", "type": "image/png", "size": 199416}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120406060, "stop": 1756120406063, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120406063, "stop": 1756120406305, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "1891dd0218ca7003", "name": "测试总结", "source": "1891dd0218ca7003.txt", "type": "text/plain", "size": 829}, {"uid": "e40b3d0c68e0d20", "name": "test_completed", "source": "e40b3d0c68e0d20.png", "type": "image/png", "size": 199666}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7e4355120957c4c1", "name": "stdout", "source": "7e4355120957c4c1.txt", "type": "text/plain", "size": 15746}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120406306, "stop": 1756120406307, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120406308, "stop": 1756120407739, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_introduce_yourself"}, {"name": "subSuite", "value": "TestEllaIntroduceYourself"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_introduce_yourself"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3a5bef4de5e19d5.json", "parameterValues": []}