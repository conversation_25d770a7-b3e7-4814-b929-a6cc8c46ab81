{"uid": "cf236e97d5abcc88", "name": "测试tell me a joke能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_tell_me_a_joke.TestEllaTellMeJoke#test_tell_me_a_joke", "historyId": "29293108cad153db021139a26e3455ee", "time": {"start": 1756138237505, "stop": 1756138262496, "duration": 24991}, "description": "tell me a joke", "descriptionHtml": "<p>tell me a joke</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_tell_me_a_joke.TestEllaTellMeJoke object at 0x00000292058A5C10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8C8090>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_tell_me_a_joke(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_tell_me_a_joke.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138224730, "stop": 1756138237503, "duration": 12773}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138237504, "stop": 1756138237504, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "tell me a joke", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_tell_me_a_joke.TestEllaTellMeJoke object at 0x00000292058A5C10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8C8090>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_tell_me_a_joke(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_tell_me_a_joke.py:33: AssertionError", "steps": [{"name": "执行命令: tell me a joke", "time": {"start": 1756138237506, "stop": 1756138262494, "duration": 24988}, "status": "passed", "steps": [{"name": "执行命令: tell me a joke", "time": {"start": 1756138237506, "stop": 1756138262243, "duration": 24737}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138262243, "stop": 1756138262493, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "2ea7b55ab4f4806", "name": "测试总结", "source": "2ea7b55ab4f4806.txt", "type": "text/plain", "size": 295}, {"uid": "f20767bc18fbd162", "name": "test_completed", "source": "f20767bc18fbd162.png", "type": "image/png", "size": 154782}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138262494, "stop": 1756138262495, "duration": 1}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_tell_me_a_joke.py\", line 33, in test_tell_me_a_joke\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f6d5ec8b535459da", "name": "stdout", "source": "f6d5ec8b535459da.txt", "type": "text/plain", "size": 13298}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138262501, "stop": 1756138262713, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "88494accf284b83f", "name": "失败截图-TestEllaTellMeJoke", "source": "88494accf284b83f.png", "type": "image/png", "size": 154782}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756138262715, "stop": 1756138264129, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_tell_me_a_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_tell_me_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "cf236e97d5abcc88.json", "parameterValues": []}