{"uid": "416b69b397e9f5c4", "name": "测试turn on the flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight.TestEllaTurnFlashlight#test_turn_on_the_flashlight", "historyId": "8c5a5747e91f2cb0412111d5027bb7ec", "time": {"start": 1756128271351, "stop": 1756128299573, "duration": 28222}, "description": "turn on the flashlight", "descriptionHtml": "<p>turn on the flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128258805, "stop": 1756128271349, "duration": 12544}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128271349, "stop": 1756128271349, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on the flashlight", "status": "passed", "steps": [{"name": "执行命令: turn on the flashlight", "time": {"start": 1756128271352, "stop": 1756128299349, "duration": 27997}, "status": "passed", "steps": [{"name": "执行命令: turn on the flashlight", "time": {"start": 1756128271352, "stop": 1756128299099, "duration": 27747}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128299099, "stop": 1756128299349, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "4dbb3715921a72d3", "name": "测试总结", "source": "4dbb3715921a72d3.txt", "type": "text/plain", "size": 233}, {"uid": "b295750d5d3f4c75", "name": "test_completed", "source": "b295750d5d3f4c75.png", "type": "image/png", "size": 156302}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128299349, "stop": 1756128299351, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128299351, "stop": 1756128299351, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128299351, "stop": 1756128299572, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "3232671446e090bc", "name": "测试总结", "source": "3232671446e090bc.txt", "type": "text/plain", "size": 233}, {"uid": "403ff5b73747ac63", "name": "test_completed", "source": "403ff5b73747ac63.png", "type": "image/png", "size": 156615}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c2f6a5cd4ada241", "name": "stdout", "source": "c2f6a5cd4ada241.txt", "type": "text/plain", "size": 12950}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128299574, "stop": 1756128299574, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128299575, "stop": 1756128301014, "duration": 1439}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "416b69b397e9f5c4.json", "parameterValues": []}