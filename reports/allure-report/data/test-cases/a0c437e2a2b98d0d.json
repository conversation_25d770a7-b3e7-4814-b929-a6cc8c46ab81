{"uid": "a0c437e2a2b98d0d", "name": "测试send my recent photos to mom through whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_send_my_recent_photos_to_mom_through_whatsapp.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "b7e448432379b6f8a430f1cbdb3ee3fb", "time": {"start": 1756121465490, "stop": 1756121494725, "duration": 29235}, "description": "send my recent photos to mom through whatsapp", "descriptionHtml": "<p>send my recent photos to mom through whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121452872, "stop": 1756121465489, "duration": 12617}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121465490, "stop": 1756121465490, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "send my recent photos to mom through whatsapp", "status": "passed", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "time": {"start": 1756121465490, "stop": 1756121494486, "duration": 28996}, "status": "passed", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "time": {"start": 1756121465490, "stop": 1756121494231, "duration": 28741}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121494231, "stop": 1756121494485, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "90c5a041811db0b6", "name": "测试总结", "source": "90c5a041811db0b6.txt", "type": "text/plain", "size": 361}, {"uid": "b6d96cdc3edd623e", "name": "test_completed", "source": "b6d96cdc3edd623e.png", "type": "image/png", "size": 196694}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121494486, "stop": 1756121494487, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121494488, "stop": 1756121494725, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "493587b4f3b7f16f", "name": "测试总结", "source": "493587b4f3b7f16f.txt", "type": "text/plain", "size": 361}, {"uid": "11f3ce17dc82b428", "name": "test_completed", "source": "11f3ce17dc82b428.png", "type": "image/png", "size": 196018}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6113ba0085075149", "name": "stdout", "source": "6113ba0085075149.txt", "type": "text/plain", "size": 13406}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121494726, "stop": 1756121494726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121494728, "stop": 1756121496078, "duration": 1350}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_send_my_recent_photos_to_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_send_my_recent_photos_to_mom_through_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a0c437e2a2b98d0d.json", "parameterValues": []}