{"uid": "e2c4c2384f9bf4b6", "name": "测试min brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_min_brightness.TestEllaMinBrightness#test_min_brightness", "historyId": "f27eb1a59c1d5c5c845852e05e6a17d9", "time": {"start": 1756125105511, "stop": 1756125132686, "duration": 27175}, "description": "min brightness", "descriptionHtml": "<p>min brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125092259, "stop": 1756125105509, "duration": 13250}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125105510, "stop": 1756125105510, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "min brightness", "status": "passed", "steps": [{"name": "执行命令: min brightness", "time": {"start": 1756125105512, "stop": 1756125132412, "duration": 26900}, "status": "passed", "steps": [{"name": "执行命令: min brightness", "time": {"start": 1756125105512, "stop": 1756125132114, "duration": 26602}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125132115, "stop": 1756125132411, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "696932bea97e75dd", "name": "测试总结", "source": "696932bea97e75dd.txt", "type": "text/plain", "size": 198}, {"uid": "836b7de8a4e758f0", "name": "test_completed", "source": "836b7de8a4e758f0.png", "type": "image/png", "size": 151736}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125132412, "stop": 1756125132417, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756125132417, "stop": 1756125132417, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125132417, "stop": 1756125132686, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "a58c16b8282390d2", "name": "测试总结", "source": "a58c16b8282390d2.txt", "type": "text/plain", "size": 198}, {"uid": "1c4d04c4470fa3ac", "name": "test_completed", "source": "1c4d04c4470fa3ac.png", "type": "image/png", "size": 151736}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "16ba4f8d8a8e3987", "name": "stdout", "source": "16ba4f8d8a8e3987.txt", "type": "text/plain", "size": 13063}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125132688, "stop": 1756125132688, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125132689, "stop": 1756125134122, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_brightness"}, {"name": "subSuite", "value": "Test<PERSON>lla<PERSON>inBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e2c4c2384f9bf4b6.json", "parameterValues": []}