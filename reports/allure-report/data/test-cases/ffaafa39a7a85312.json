{"uid": "ffaafa39a7a85312", "name": "测试turn off nfc能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_nfc.TestEllaTurnOffNfc#test_turn_off_nfc", "historyId": "afde8e86697e1ec7ad65ac0c993e60f2", "time": {"start": 1756127537979, "stop": 1756127565377, "duration": 27398}, "description": "turn off nfc", "descriptionHtml": "<p>turn off nfc</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127525402, "stop": 1756127537978, "duration": 12576}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127537978, "stop": 1756127537978, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn off nfc", "status": "passed", "steps": [{"name": "执行命令: turn off nfc", "time": {"start": 1756127537979, "stop": 1756127565165, "duration": 27186}, "status": "passed", "steps": [{"name": "执行命令: turn off nfc", "time": {"start": 1756127537979, "stop": 1756127564917, "duration": 26938}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127564917, "stop": 1756127565165, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "e6b6ef9b96a594d3", "name": "测试总结", "source": "e6b6ef9b96a594d3.txt", "type": "text/plain", "size": 281}, {"uid": "1d09f2a7c46e9b9d", "name": "test_completed", "source": "1d09f2a7c46e9b9d.png", "type": "image/png", "size": 164218}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127565165, "stop": 1756127565167, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127565167, "stop": 1756127565167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127565167, "stop": 1756127565376, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "b72d58d6e60e012b", "name": "测试总结", "source": "b72d58d6e60e012b.txt", "type": "text/plain", "size": 281}, {"uid": "54077e8e30272a1f", "name": "test_completed", "source": "54077e8e30272a1f.png", "type": "image/png", "size": 164245}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "869146c7b00af50d", "name": "stdout", "source": "869146c7b00af50d.txt", "type": "text/plain", "size": 13457}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127565377, "stop": 1756127565378, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127565379, "stop": 1756127566763, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_nfc"}, {"name": "subSuite", "value": "TestEllaTurnOffNfc"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_nfc"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ffaafa39a7a85312.json", "parameterValues": []}