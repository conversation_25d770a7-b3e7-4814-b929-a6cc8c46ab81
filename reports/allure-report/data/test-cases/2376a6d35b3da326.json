{"uid": "2376a6d35b3da326", "name": "测试delete the 8 o'clock alarm", "fullName": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm.TestEllaOpenClock#test_delete_the_8_o_clock_alarm", "historyId": "8c62567d8b8a27f77124afc90fa44336", "time": {"start": 1756117435749, "stop": 1756117488081, "duration": 52332}, "description": "测试delete the 8 o'clock alarm指令", "descriptionHtml": "<p>测试delete the 8 o'clock alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117423177, "stop": 1756117435747, "duration": 12570}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117435747, "stop": 1756117435747, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试delete the 8 o'clock alarm指令", "status": "passed", "steps": [{"name": "执行命令: set  8 o'clock alarm", "time": {"start": 1756117435749, "stop": 1756117461273, "duration": 25524}, "status": "passed", "steps": [{"name": "执行命令: set  8 o'clock alarm", "time": {"start": 1756117435749, "stop": 1756117461011, "duration": 25262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117461011, "stop": 1756117461272, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "9ae363b9ea5c15a", "name": "测试总结", "source": "9ae363b9ea5c15a.txt", "type": "text/plain", "size": 241}, {"uid": "6c00166137ce89f5", "name": "test_completed", "source": "6c00166137ce89f5.png", "type": "image/png", "size": 153808}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1756117461273, "stop": 1756117487866, "duration": 26593}, "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1756117461273, "stop": 1756117487620, "duration": 26347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117487620, "stop": 1756117487865, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "4b3b8c5deba36633", "name": "测试总结", "source": "4b3b8c5deba36633.txt", "type": "text/plain", "size": 251}, {"uid": "5a6655823030a765", "name": "test_completed", "source": "5a6655823030a765.png", "type": "image/png", "size": 159428}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117487866, "stop": 1756117487867, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117487867, "stop": 1756117488081, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "27edeb901a0b7346", "name": "测试总结", "source": "27edeb901a0b7346.txt", "type": "text/plain", "size": 251}, {"uid": "d5cf513d40b577d5", "name": "test_completed", "source": "d5cf513d40b577d5.png", "type": "image/png", "size": 159854}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ff298516fcda692a", "name": "stdout", "source": "ff298516fcda692a.txt", "type": "text/plain", "size": 21843}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 8, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117488082, "stop": 1756117488082, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117488083, "stop": 1756117489526, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_delete_the_8_o_clock_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2376a6d35b3da326.json", "parameterValues": []}