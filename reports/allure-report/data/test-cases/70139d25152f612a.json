{"uid": "70139d25152f612a", "name": "测试A furry little monkey", "fullName": "testcases.test_ella.unsupported_commands.test_help_generate_a_picture_of_ancient_city.TestEllaOpenPlayPoliticalNews#test_help_generate_a_picture_of_ancient_city", "historyId": "6980dbcce9a72cd9dea6dee04c6891de", "time": {"start": 1756132492241, "stop": 1756132519476, "duration": 27235}, "description": "测试A furry little monkey指令", "descriptionHtml": "<p>测试A furry little monkey指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132479495, "stop": 1756132492240, "duration": 12745}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132492240, "stop": 1756132492241, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试A furry little monkey指令", "status": "passed", "steps": [{"name": "执行命令: A furry little monkey", "time": {"start": 1756132492241, "stop": 1756132519228, "duration": 26987}, "status": "passed", "steps": [{"name": "执行命令: A furry little monkey", "time": {"start": 1756132492241, "stop": 1756132518942, "duration": 26701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132518942, "stop": 1756132519227, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "9b2a4f15fa70027", "name": "测试总结", "source": "9b2a4f15fa70027.txt", "type": "text/plain", "size": 920}, {"uid": "a23d70254c204b9e", "name": "test_completed", "source": "a23d70254c204b9e.png", "type": "image/png", "size": 203337}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132519228, "stop": 1756132519229, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132519229, "stop": 1756132519476, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "a310d534d4978eef", "name": "测试总结", "source": "a310d534d4978eef.txt", "type": "text/plain", "size": 920}, {"uid": "83265be40ab02e6d", "name": "test_completed", "source": "83265be40ab02e6d.png", "type": "image/png", "size": 203337}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7e985378ad4fbe26", "name": "stdout", "source": "7e985378ad4fbe26.txt", "type": "text/plain", "size": 15363}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132519477, "stop": 1756132519477, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132519478, "stop": 1756132520884, "duration": 1406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_generate_a_picture_of_ancient_city"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_generate_a_picture_of_ancient_city"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "70139d25152f612a.json", "parameterValues": []}