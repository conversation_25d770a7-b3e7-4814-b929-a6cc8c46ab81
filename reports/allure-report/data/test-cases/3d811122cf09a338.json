{"uid": "3d811122cf09a338", "name": "测试change your language能正常执行", "fullName": "testcases.test_ella.system_coupling.test_change_your_language.TestEllaChangeYourLanguage#test_change_your_language", "historyId": "0f3523ec9dc3ea86ebaca76b6956f01c", "time": {"start": 1756123831128, "stop": 1756123858173, "duration": 27045}, "description": "change your language", "descriptionHtml": "<p>change your language</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['OK, redirecting to the Ella language list page.']，实际响应: '['change your language', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore 08:10 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON>fresh Man Utd Transfer Deal Collapses What is Ask About Screen? Play <PERSON><PERSON>'s song on Spotify change your language <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions… 8:10\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_change_your_language.TestEllaChangeYourLanguage object at 0x000002920489E850>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206D77B10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_change_your_language(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['OK, redirecting to the Ella language list page.']，实际响应: '['change your language', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore 08:10 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Man Utd Transfer Deal Collapses What is Ask About Screen? Play Rema's song on Spotify change your language Ella is thinking… DeepSeek-R1 Feel free to ask me any questions… 8:10\"]'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_change_your_language.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123818750, "stop": 1756123831127, "duration": 12377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123831127, "stop": 1756123831127, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "change your language", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['OK, redirecting to the Ella language list page.']，实际响应: '['change your language', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore 08:10 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON>fresh Man Utd Transfer Deal Collapses What is Ask About Screen? Play <PERSON><PERSON>'s song on Spotify change your language <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions… 8:10\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_change_your_language.TestEllaChangeYourLanguage object at 0x000002920489E850>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206D77B10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_change_your_language(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['OK, redirecting to the Ella language list page.']，实际响应: '['change your language', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore 08:10 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Man Utd Transfer Deal Collapses What is Ask About Screen? Play Rema's song on Spotify change your language Ella is thinking… DeepSeek-R1 Feel free to ask me any questions… 8:10\"]'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_change_your_language.py:33: AssertionError", "steps": [{"name": "执行命令: change your language", "time": {"start": 1756123831128, "stop": 1756123858161, "duration": 27033}, "status": "passed", "steps": [{"name": "执行命令: change your language", "time": {"start": 1756123831128, "stop": 1756123857888, "duration": 26760}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123857888, "stop": 1756123858160, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "47c49fc0688a383d", "name": "测试总结", "source": "47c49fc0688a383d.txt", "type": "text/plain", "size": 596}, {"uid": "1f7951fe4735447a", "name": "test_completed", "source": "1f7951fe4735447a.png", "type": "image/png", "size": 177954}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123858161, "stop": 1756123858169, "duration": 8}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['OK, redirecting to the Ella language list page.']，实际响应: '['change your language', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore 08:10 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. <PERSON>fresh Man Utd Transfer Deal Collapses What is Ask About Screen? Play <PERSON><PERSON>'s song on Spotify change your language <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions… 8:10\"]'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_change_your_language.py\", line 33, in test_change_your_language\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fcbae88efa9b0e47", "name": "stdout", "source": "fcbae88efa9b0e47.txt", "type": "text/plain", "size": 15965}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123858180, "stop": 1756123858404, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "6a0221dd4535b7fd", "name": "失败截图-TestEllaChangeYourLanguage", "source": "6a0221dd4535b7fd.png", "type": "image/png", "size": 177892}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756123858406, "stop": 1756123859720, "duration": 1314}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_change_your_language"}, {"name": "subSuite", "value": "TestEllaChangeYourLanguage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_change_your_language"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "3d811122cf09a338.json", "parameterValues": []}