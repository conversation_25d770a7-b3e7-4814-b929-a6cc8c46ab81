{"uid": "c7460a6571f23975", "name": "测试reset phone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_reset_phone.TestEllaResetPhone#test_reset_phone", "historyId": "1615e8617cafbed9e30baf38018d96b0", "time": {"start": 1756135720106, "stop": 1756135720106, "duration": 0}, "description": "验证reset phone指令返回预期的不支持响应", "descriptionHtml": "<p>验证reset phone指令返回预期的不支持响应</p>\n", "status": "skipped", "statusMessage": "Skipped: reset phone 会导致设备断开，先跳过", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\unsupported_commands\\\\test_reset_phone.py', 16, 'Skipped: reset phone 会导致设备断开，先跳过')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135732883, "stop": 1756135732883, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135762569, "stop": 1756135762569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_reset_phone"}, {"name": "subSuite", "value": "TestEllaResetPhone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_reset_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke", "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"]}, "source": "c7460a6571f23975.json", "parameterValues": []}