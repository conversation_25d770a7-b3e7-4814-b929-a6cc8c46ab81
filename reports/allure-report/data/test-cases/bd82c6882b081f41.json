{"uid": "bd82c6882b081f41", "name": "测试screen record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord#test_screen_record", "historyId": "18fb8c43c609a9825fe52e528761fd1b", "time": {"start": 1756125487084, "stop": 1756125516844, "duration": 29760}, "description": "screen record", "descriptionHtml": "<p>screen record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125474479, "stop": 1756125487083, "duration": 12604}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125487084, "stop": 1756125487084, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "screen record", "status": "passed", "steps": [{"name": "执行命令: screen record", "time": {"start": 1756125487085, "stop": 1756125516572, "duration": 29487}, "status": "passed", "steps": [{"name": "执行命令: screen record", "time": {"start": 1756125487085, "stop": 1756125516335, "duration": 29250}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125516335, "stop": 1756125516572, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "46e420f551a2947", "name": "测试总结", "source": "46e420f551a2947.txt", "type": "text/plain", "size": 283}, {"uid": "dec572cfc5b258a1", "name": "test_completed", "source": "dec572cfc5b258a1.png", "type": "image/png", "size": 180846}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125516572, "stop": 1756125516574, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756125516574, "stop": 1756125516574, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125516574, "stop": 1756125516844, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "e6e22f365929fc6f", "name": "测试总结", "source": "e6e22f365929fc6f.txt", "type": "text/plain", "size": 283}, {"uid": "702be450e2e40c7b", "name": "test_completed", "source": "702be450e2e40c7b.png", "type": "image/png", "size": 180640}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "17facd1b230f009f", "name": "stdout", "source": "17facd1b230f009f.txt", "type": "text/plain", "size": 12901}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125516845, "stop": 1756125516845, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125516846, "stop": 1756125518212, "duration": 1366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_screen_record"}, {"name": "subSuite", "value": "TestEllaScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bd82c6882b081f41.json", "parameterValues": []}