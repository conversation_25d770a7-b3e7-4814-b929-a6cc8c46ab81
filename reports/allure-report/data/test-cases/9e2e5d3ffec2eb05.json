{"uid": "9e2e5d3ffec2eb05", "name": "测试turn on light theme能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_light_theme.TestEllaTurnLightTheme#test_turn_on_light_theme", "historyId": "16f38913a9d7e0ffc4c5ac51d5acf5c7", "time": {"start": 1756127943588, "stop": 1756127970761, "duration": 27173}, "description": "turn on light theme", "descriptionHtml": "<p>turn on light theme</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127930637, "stop": 1756127943584, "duration": 12947}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127943585, "stop": 1756127943585, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on light theme", "status": "passed", "steps": [{"name": "执行命令: turn on light theme", "time": {"start": 1756127943588, "stop": 1756127970452, "duration": 26864}, "status": "passed", "steps": [{"name": "执行命令: turn on light theme", "time": {"start": 1756127943588, "stop": 1756127970135, "duration": 26547}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127970135, "stop": 1756127970452, "duration": 317}, "status": "passed", "steps": [], "attachments": [{"uid": "88304074b9faa8f4", "name": "测试总结", "source": "88304074b9faa8f4.txt", "type": "text/plain", "size": 204}, {"uid": "2ae52f1329fb9570", "name": "test_completed", "source": "2ae52f1329fb9570.png", "type": "image/png", "size": 249741}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127970452, "stop": 1756127970456, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127970456, "stop": 1756127970456, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127970456, "stop": 1756127970759, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "41d67f74a7ed7ea3", "name": "测试总结", "source": "41d67f74a7ed7ea3.txt", "type": "text/plain", "size": 204}, {"uid": "7c9d1a5204aa4a21", "name": "test_completed", "source": "7c9d1a5204aa4a21.png", "type": "image/png", "size": 249728}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9639e09ce7018e16", "name": "stdout", "source": "9639e09ce7018e16.txt", "type": "text/plain", "size": 13981}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127970762, "stop": 1756127970762, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127970765, "stop": 1756127972220, "duration": 1455}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnLightTheme"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_light_theme"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9e2e5d3ffec2eb05.json", "parameterValues": []}