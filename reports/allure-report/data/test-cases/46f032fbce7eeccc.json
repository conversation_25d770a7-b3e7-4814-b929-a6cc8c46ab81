{"uid": "46f032fbce7eeccc", "name": "测试how to set screenshots返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots.TestEllaHowSetScreenshots#test_how_to_set_screenshots", "historyId": "bfd4a9e37b70dca0b14b0ccf5246fc4a", "time": {"start": 1756133068698, "stop": 1756133102034, "duration": 33336}, "description": "验证how to set screenshots指令返回预期的不支持响应", "descriptionHtml": "<p>验证how to set screenshots指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133055677, "stop": 1756133068696, "duration": 13019}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133068696, "stop": 1756133068696, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证how to set screenshots指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1756133068698, "stop": 1756133101768, "duration": 33070}, "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1756133068698, "stop": 1756133101494, "duration": 32796}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133101494, "stop": 1756133101768, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "869e01f0a72894df", "name": "测试总结", "source": "869e01f0a72894df.txt", "type": "text/plain", "size": 671}, {"uid": "f6bd6e3c722c2a89", "name": "test_completed", "source": "f6bd6e3c722c2a89.png", "type": "image/png", "size": 174020}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133101768, "stop": 1756133101782, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133101782, "stop": 1756133102033, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "3483b4df452f4e8d", "name": "测试总结", "source": "3483b4df452f4e8d.txt", "type": "text/plain", "size": 671}, {"uid": "8d6d7998d9e5e2fc", "name": "test_completed", "source": "8d6d7998d9e5e2fc.png", "type": "image/png", "size": 173786}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "badee8650e1e51b9", "name": "stdout", "source": "badee8650e1e51b9.txt", "type": "text/plain", "size": 15420}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133102034, "stop": 1756133102034, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133102035, "stop": 1756133103544, "duration": 1509}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_how_to_set_screenshots"}, {"name": "subSuite", "value": "TestEllaHowSetScreenshots"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "46f032fbce7eeccc.json", "parameterValues": []}