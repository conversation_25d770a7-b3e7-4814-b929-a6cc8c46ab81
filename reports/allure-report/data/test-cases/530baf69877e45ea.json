{"uid": "530baf69877e45ea", "name": "测试decrease the volume to the minimun能正常执行", "fullName": "testcases.test_ella.system_coupling.test_decrease_the_volume_to_the_minimun.TestEllaDecreaseVolumeMinimun#test_decrease_the_volume_to_the_minimun", "historyId": "4aad505422b0d8c87c499477cd89ff5d", "time": {"start": 1756124246974, "stop": 1756124272950, "duration": 25976}, "description": "decrease the volume to the minimun", "descriptionHtml": "<p>decrease the volume to the minimun</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124234497, "stop": 1756124246973, "duration": 12476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124246973, "stop": 1756124246973, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "decrease the volume to the minimun", "status": "passed", "steps": [{"name": "执行命令: decrease the volume to the minimun", "time": {"start": 1756124246974, "stop": 1756124272739, "duration": 25765}, "status": "passed", "steps": [{"name": "执行命令: decrease the volume to the minimun", "time": {"start": 1756124246974, "stop": 1756124272498, "duration": 25524}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124272498, "stop": 1756124272738, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "80b19d7987f5e393", "name": "测试总结", "source": "80b19d7987f5e393.txt", "type": "text/plain", "size": 335}, {"uid": "4f85c0427a4f5bf4", "name": "test_completed", "source": "4f85c0427a4f5bf4.png", "type": "image/png", "size": 186095}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124272739, "stop": 1756124272740, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124272740, "stop": 1756124272740, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124272740, "stop": 1756124272949, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "e4df1052186aff3d", "name": "测试总结", "source": "e4df1052186aff3d.txt", "type": "text/plain", "size": 335}, {"uid": "8de3ab8ac00596f8", "name": "test_completed", "source": "8de3ab8ac00596f8.png", "type": "image/png", "size": 186095}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3283b15493ff2eeb", "name": "stdout", "source": "3283b15493ff2eeb.txt", "type": "text/plain", "size": 13610}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124272950, "stop": 1756124272950, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124272951, "stop": 1756124274335, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_decrease_the_volume_to_the_minimun"}, {"name": "subSuite", "value": "TestEllaDecreaseVolumeMinimun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_decrease_the_volume_to_the_minimun"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "530baf69877e45ea.json", "parameterValues": []}