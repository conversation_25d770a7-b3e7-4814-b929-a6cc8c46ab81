{"uid": "f2b3ab492e54511c", "name": "测试Switch to Low-Temp Charge能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "historyId": "753ba105235625e906d023bd3aaa0821", "time": {"start": 1756126846885, "stop": 1756126873008, "duration": 26123}, "description": "Switch to Low-Temp Charge", "descriptionHtml": "<p>Switch to Low-Temp Charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126834040, "stop": 1756126846884, "duration": 12844}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126846884, "stop": 1756126846884, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Switch to Low-Temp Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "time": {"start": 1756126846885, "stop": 1756126872733, "duration": 25848}, "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "time": {"start": 1756126846885, "stop": 1756126872475, "duration": 25590}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126872475, "stop": 1756126872732, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "a40f80242172fbc9", "name": "测试总结", "source": "a40f80242172fbc9.txt", "type": "text/plain", "size": 403}, {"uid": "4b12f3ebbbd0ae60", "name": "test_completed", "source": "4b12f3ebbbd0ae60.png", "type": "image/png", "size": 195408}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126872733, "stop": 1756126872736, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126872736, "stop": 1756126873007, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "e115dc7707739d9d", "name": "测试总结", "source": "e115dc7707739d9d.txt", "type": "text/plain", "size": 403}, {"uid": "a1add01be455e6cb", "name": "test_completed", "source": "a1add01be455e6cb.png", "type": "image/png", "size": 195408}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "79a37472cc5a1452", "name": "stdout", "source": "79a37472cc5a1452.txt", "type": "text/plain", "size": 13457}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126873009, "stop": 1756126873009, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126873009, "stop": 1756126874436, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_low_temp_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f2b3ab492e54511c.json", "parameterValues": []}