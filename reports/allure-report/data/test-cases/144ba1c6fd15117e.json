{"uid": "144ba1c6fd15117e", "name": "测试hello hello能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_hello_hello.TestEllaHelloHello#test_hello_hello", "historyId": "0ce2a3efa79db58c34a5590015948f51", "time": {"start": 1756132450309, "stop": 1756132478063, "duration": 27754}, "description": "hello hello", "descriptionHtml": "<p>hello hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132437559, "stop": 1756132450307, "duration": 12748}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132450307, "stop": 1756132450307, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1756132450309, "stop": 1756132477838, "duration": 27529}, "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1756132450309, "stop": 1756132477556, "duration": 27247}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132477556, "stop": 1756132477837, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "90ba9b8533516186", "name": "测试总结", "source": "90ba9b8533516186.txt", "type": "text/plain", "size": 722}, {"uid": "a5b5b2bef0dbd9b3", "name": "test_completed", "source": "a5b5b2bef0dbd9b3.png", "type": "image/png", "size": 180273}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132477838, "stop": 1756132477841, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756132477841, "stop": 1756132477841, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132477841, "stop": 1756132478062, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "2f6305f67ddf59a", "name": "测试总结", "source": "2f6305f67ddf59a.txt", "type": "text/plain", "size": 722}, {"uid": "1b5ee51a73cf707e", "name": "test_completed", "source": "1b5ee51a73cf707e.png", "type": "image/png", "size": 179894}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4628764f7bec6e63", "name": "stdout", "source": "4628764f7bec6e63.txt", "type": "text/plain", "size": 15316}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132478063, "stop": 1756132478063, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132478065, "stop": 1756132479488, "duration": 1423}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_hello_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "144ba1c6fd15117e.json", "parameterValues": []}