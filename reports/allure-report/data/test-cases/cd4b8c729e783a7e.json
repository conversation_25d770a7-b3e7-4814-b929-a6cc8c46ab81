{"uid": "cd4b8c729e783a7e", "name": "测试turn down ring volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_down_ring_volume.TestEllaTurnDownRingVolume#test_turn_down_ring_volume", "historyId": "bf94eb080274f830f3097dd5adde1ed1", "time": {"start": 1756127295917, "stop": 1756127322178, "duration": 26261}, "description": "turn down ring volume", "descriptionHtml": "<p>turn down ring volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127283259, "stop": 1756127295915, "duration": 12656}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127295916, "stop": 1756127295916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn down ring volume", "status": "passed", "steps": [{"name": "执行命令: turn down ring volume", "time": {"start": 1756127295917, "stop": 1756127321941, "duration": 26024}, "status": "passed", "steps": [{"name": "执行命令: turn down ring volume", "time": {"start": 1756127295917, "stop": 1756127321695, "duration": 25778}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127321695, "stop": 1756127321940, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "f3dae2547a0614aa", "name": "测试总结", "source": "f3dae2547a0614aa.txt", "type": "text/plain", "size": 304}, {"uid": "c24f477b96264ecc", "name": "test_completed", "source": "c24f477b96264ecc.png", "type": "image/png", "size": 179793}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127321941, "stop": 1756127321943, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127321943, "stop": 1756127322178, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "4f71142974141bff", "name": "测试总结", "source": "4f71142974141bff.txt", "type": "text/plain", "size": 304}, {"uid": "7cca21485c4aa12c", "name": "test_completed", "source": "7cca21485c4aa12c.png", "type": "image/png", "size": 179793}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "64f9b0385d716d4", "name": "stdout", "source": "64f9b0385d716d4.txt", "type": "text/plain", "size": 13752}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127322179, "stop": 1756127322179, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127322213, "stop": 1756127323553, "duration": 1340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_ring_volume"}, {"name": "subSuite", "value": "TestEllaTurnDownRingVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_ring_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cd4b8c729e783a7e.json", "parameterValues": []}