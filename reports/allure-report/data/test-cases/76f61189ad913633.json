{"uid": "76f61189ad913633", "name": "测试memory cleanup能正常执行", "fullName": "testcases.test_ella.system_coupling.test_memory_cleanup.TestEllaMemoryCleanup#test_memory_cleanup", "historyId": "e4bab2ec1074fdbc8b4508dfad12adfc", "time": {"start": 1756124999382, "stop": 1756125049777, "duration": 50395}, "description": "memory cleanup", "descriptionHtml": "<p>memory cleanup</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124985834, "stop": 1756124999377, "duration": 13543}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124999377, "stop": 1756124999377, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "memory cleanup", "status": "passed", "steps": [{"name": "执行命令: memory cleanup", "time": {"start": 1756124999382, "stop": 1756125049506, "duration": 50124}, "status": "passed", "steps": [{"name": "执行命令: memory cleanup", "time": {"start": 1756124999382, "stop": 1756125049256, "duration": 49874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125049256, "stop": 1756125049505, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "6e784f53bf545363", "name": "测试总结", "source": "6e784f53bf545363.txt", "type": "text/plain", "size": 448}, {"uid": "4f951133f8180068", "name": "test_completed", "source": "4f951133f8180068.png", "type": "image/png", "size": 174949}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125049506, "stop": 1756125049510, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1756125049510, "stop": 1756125049511, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125049511, "stop": 1756125049774, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "897a058b98d72f5", "name": "测试总结", "source": "897a058b98d72f5.txt", "type": "text/plain", "size": 448}, {"uid": "fd9d8cbb2e6ec36a", "name": "test_completed", "source": "fd9d8cbb2e6ec36a.png", "type": "image/png", "size": 175589}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c2d9a64f2de0be63", "name": "stdout", "source": "c2d9a64f2de0be63.txt", "type": "text/plain", "size": 15382}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125049780, "stop": 1756125049780, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125049781, "stop": 1756125051158, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_memory_cleanup"}, {"name": "subSuite", "value": "TestEllaMemoryCleanup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_memory_cleanup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "76f61189ad913633.json", "parameterValues": []}