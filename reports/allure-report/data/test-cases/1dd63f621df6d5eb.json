{"uid": "1dd63f621df6d5eb", "name": "测试how is the wheather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday#test_how_is_the_wheather_today", "historyId": "f7282303534c1c8599c3343608e6f453", "time": {"start": 1756119984397, "stop": 1756120009829, "duration": 25432}, "description": "how is the wheather today", "descriptionHtml": "<p>how is the wheather today</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday object at 0x00000292040DB6D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206CF7610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_wheather_today.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119971746, "stop": 1756119984397, "duration": 12651}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119984397, "stop": 1756119984397, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "how is the wheather today", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday object at 0x00000292040DB6D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206CF7610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_wheather_today.py:36: AssertionError", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1756119984398, "stop": 1756120009823, "duration": 25425}, "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1756119984398, "stop": 1756120009583, "duration": 25185}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120009583, "stop": 1756120009822, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "c3815c468c58d5a8", "name": "测试总结", "source": "c3815c468c58d5a8.txt", "type": "text/plain", "size": 318}, {"uid": "1b791ea48e4ff770", "name": "test_completed", "source": "1b791ea48e4ff770.png", "type": "image/png", "size": 177714}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120009823, "stop": 1756120009827, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_how_is_the_wheather_today.py\", line 36, in test_how_is_the_wheather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a3e0fc6b5cb00704", "name": "stdout", "source": "a3e0fc6b5cb00704.txt", "type": "text/plain", "size": 14078}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120009832, "stop": 1756120010051, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "e7ae00944644d252", "name": "失败截图-TestEllaHowIsWheatherToday", "source": "e7ae00944644d252.png", "type": "image/png", "size": 177714}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756120010054, "stop": 1756120011494, "duration": 1440}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_wheather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "1dd63f621df6d5eb.json", "parameterValues": []}