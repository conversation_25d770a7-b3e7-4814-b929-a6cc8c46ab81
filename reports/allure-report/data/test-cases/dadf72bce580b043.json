{"uid": "dadf72bce580b043", "name": "测试set parallel windows返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_parallel_windows.TestEllaSetParallelWindows#test_set_parallel_windows", "historyId": "eb142151b4b5ba4125a1a866dc2b58ef", "time": {"start": 1756137002003, "stop": 1756137027728, "duration": 25725}, "description": "验证set parallel windows指令返回预期的不支持响应", "descriptionHtml": "<p>验证set parallel windows指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136988799, "stop": 1756137002002, "duration": 13203}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137002002, "stop": 1756137002002, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set parallel windows指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set parallel windows", "time": {"start": 1756137002003, "stop": 1756137027489, "duration": 25486}, "status": "passed", "steps": [{"name": "执行命令: set parallel windows", "time": {"start": 1756137002003, "stop": 1756137027198, "duration": 25195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137027198, "stop": 1756137027488, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "222f0b8d19206836", "name": "测试总结", "source": "222f0b8d19206836.txt", "type": "text/plain", "size": 339}, {"uid": "f5d7ef16d170de83", "name": "test_completed", "source": "f5d7ef16d170de83.png", "type": "image/png", "size": 178245}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137027489, "stop": 1756137027490, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137027490, "stop": 1756137027727, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "22655c9bd317f1c6", "name": "测试总结", "source": "22655c9bd317f1c6.txt", "type": "text/plain", "size": 339}, {"uid": "4bd05ba5f17e01fa", "name": "test_completed", "source": "4bd05ba5f17e01fa.png", "type": "image/png", "size": 178245}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3fe8d53283393713", "name": "stdout", "source": "3fe8d53283393713.txt", "type": "text/plain", "size": 12599}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137027728, "stop": 1756137027728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756137027730, "stop": 1756137029235, "duration": 1505}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_parallel_windows"}, {"name": "subSuite", "value": "TestEllaSetParallelWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_parallel_windows"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dadf72bce580b043.json", "parameterValues": []}