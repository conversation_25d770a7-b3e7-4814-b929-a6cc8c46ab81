{"uid": "2bba91f455081344", "name": "测试turn off show battery percentage返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage.TestEllaTurnOffShowBatteryPercentage#test_turn_off_show_battery_percentage", "historyId": "4bda544c08fa4bc5494c7dda01d4cc77", "time": {"start": 1756138563540, "stop": 1756138589612, "duration": 26072}, "description": "验证turn off show battery percentage指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn off show battery percentage指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138550860, "stop": 1756138563537, "duration": 12677}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138563537, "stop": 1756138563537, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证turn off show battery percentage指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn off show battery percentage", "time": {"start": 1756138563540, "stop": 1756138589379, "duration": 25839}, "status": "passed", "steps": [{"name": "执行命令: turn off show battery percentage", "time": {"start": 1756138563540, "stop": 1756138589114, "duration": 25574}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138589114, "stop": 1756138589378, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "b0456fcb850660", "name": "测试总结", "source": "b0456fcb850660.txt", "type": "text/plain", "size": 365}, {"uid": "e269fb55cf36b912", "name": "test_completed", "source": "e269fb55cf36b912.png", "type": "image/png", "size": 175532}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138589379, "stop": 1756138589380, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138589380, "stop": 1756138589611, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "be106c3c95746021", "name": "测试总结", "source": "be106c3c95746021.txt", "type": "text/plain", "size": 365}, {"uid": "ae57c7374221b424", "name": "test_completed", "source": "ae57c7374221b424.png", "type": "image/png", "size": 175532}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f06920979e79f96c", "name": "stdout", "source": "f06920979e79f96c.txt", "type": "text/plain", "size": 13262}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138589612, "stop": 1756138589612, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138589613, "stop": 1756138590953, "duration": 1340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnOffShowBatteryPercentage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2bba91f455081344.json", "parameterValues": []}