{"uid": "de1c266f1f495edf", "name": "测试enable zonetouch master返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master.TestEllaEnableZonetouchMaster#test_enable_zonetouch_master", "historyId": "7a670647c2336e6a5a5d07824fe89da6", "time": {"start": 1756131876834, "stop": 1756131902792, "duration": 25958}, "description": "验证enable zonetouch master指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable zonetouch master指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131863902, "stop": 1756131876833, "duration": 12931}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131876833, "stop": 1756131876833, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable zonetouch master指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "time": {"start": 1756131876835, "stop": 1756131902547, "duration": 25712}, "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "time": {"start": 1756131876835, "stop": 1756131902283, "duration": 25448}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131902283, "stop": 1756131902547, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "7c96e0616bba0ab2", "name": "测试总结", "source": "7c96e0616bba0ab2.txt", "type": "text/plain", "size": 346}, {"uid": "22c1db6aedd1ce3a", "name": "test_completed", "source": "22c1db6aedd1ce3a.png", "type": "image/png", "size": 193599}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131902547, "stop": 1756131902550, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131902550, "stop": 1756131902791, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "26b94e921f23d37d", "name": "测试总结", "source": "26b94e921f23d37d.txt", "type": "text/plain", "size": 346}, {"uid": "55e35b48eead84e2", "name": "test_completed", "source": "55e35b48eead84e2.png", "type": "image/png", "size": 193612}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7604ca9d31eba38b", "name": "stdout", "source": "7604ca9d31eba38b.txt", "type": "text/plain", "size": 12635}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131902793, "stop": 1756131902793, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131902794, "stop": 1756131904184, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaEnableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "de1c266f1f495edf.json", "parameterValues": []}