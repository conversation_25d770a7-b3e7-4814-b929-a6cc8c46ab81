{"uid": "bfaa015874ced1ec", "name": "测试make a phone call to 17621905233", "fullName": "testcases.test_ella.unsupported_commands.test_make_a_phone_call_to_17621905233.TestEllaOpenPlayPoliticalNews#test_make_a_phone_call_to_17621905233", "historyId": "b28e20f7b76af65c54477681eee78169", "time": {"start": 1756134181984, "stop": 1756134181984, "duration": 0}, "description": "测试make a phone call to 17621905233指令", "descriptionHtml": "<p>测试make a phone call to 17621905233指令</p>\n", "status": "skipped", "statusMessage": "Skipped: make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\unsupported_commands\\\\test_make_a_phone_call_to_17621905233.py', 14, 'Skipped: make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134194932, "stop": 1756134194932, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134223926, "stop": 1756134224150, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "7dc07bbab73281b0", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "7dc07bbab73281b0.png", "type": "image/png", "size": 184487}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "@pytest.mark.skip(reason='make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_make_a_phone_call_to_17621905233"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_make_a_phone_call_to_17621905233"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["@pytest.mark.skip(reason='make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')", "smoke"]}, "source": "bfaa015874ced1ec.json", "parameterValues": []}