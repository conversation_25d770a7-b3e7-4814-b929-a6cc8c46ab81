{"uid": "82c5f0337930a62b", "name": "测试disable magic voice changer能正常执行", "fullName": "testcases.test_ella.dialogue.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "d6d97ebce763bf8ead601650bfb2383c", "time": {"start": 1756119641847, "stop": 1756119667341, "duration": 25494}, "description": "disable magic voice changer", "descriptionHtml": "<p>disable magic voice changer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119628715, "stop": 1756119641845, "duration": 13130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119641846, "stop": 1756119641846, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "disable magic voice changer", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1756119641847, "stop": 1756119667117, "duration": 25270}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1756119641847, "stop": 1756119666873, "duration": 25026}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119666873, "stop": 1756119667117, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "a7b51be04abd8b48", "name": "测试总结", "source": "a7b51be04abd8b48.txt", "type": "text/plain", "size": 348}, {"uid": "b6a0f1a7c0772692", "name": "test_completed", "source": "b6a0f1a7c0772692.png", "type": "image/png", "size": 186559}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756119667117, "stop": 1756119667120, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119667120, "stop": 1756119667340, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "5535021c86c792f9", "name": "测试总结", "source": "5535021c86c792f9.txt", "type": "text/plain", "size": 348}, {"uid": "8278ae2a78180e5f", "name": "test_completed", "source": "8278ae2a78180e5f.png", "type": "image/png", "size": 186559}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f842b04b866a0ab2", "name": "stdout", "source": "f842b04b866a0ab2.txt", "type": "text/plain", "size": 12788}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119667342, "stop": 1756119667342, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119667344, "stop": 1756119668683, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "82c5f0337930a62b.json", "parameterValues": []}