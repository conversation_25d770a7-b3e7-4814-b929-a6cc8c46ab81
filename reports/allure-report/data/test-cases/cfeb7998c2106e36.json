{"uid": "cfeb7998c2106e36", "name": "测试set battery saver settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings#test_set_battery_saver_settings", "historyId": "e60dab4e55edacbecf632d4d22f368e6", "time": {"start": 1756136078327, "stop": 1756136114125, "duration": 35798}, "description": "验证set battery saver settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证set battery saver settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set battery saver settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Saver | When Battery Saver is on, system settings will be optimized, and background network status, location access, refresh rate, etc. will be restricted. | Switch Screen Refresh Rate To 60Hz | Enable Dark Theme | Screen Auto Off After 15 S | Enabling Battery Saver will optimize system settings and restrict some features.Tap for details | Battery Saver Notification | When the battery is below 20%, you will be notified to turn on Battery Saver. | Auto-enable Battery Saver Mode | Battery Level for Auto Enabling | 20% | Auto-Disable Battery Saver | Auto-disable when the battery is charged to 75%. If Battery Saver is enabled with battery higher than 75%, it will be auto-disabled when the battery reaches 100%. | Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings object at 0x0000029205661090>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920968A610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_battery_saver_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set battery saver settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Saver | When Battery Saver is on, system settings will be optimized, and background network status, location access, refresh rate, etc. will be restricted. | Switch Screen Refresh Rate To 60Hz | Enable Dark Theme | Screen Auto Off After 15 S | Enabling Battery Saver will optimize system settings and restrict some features.Tap for details | Battery Saver Notification | When the battery is below 20%, you will be notified to turn on Battery Saver. | Auto-enable Battery Saver Mode | Battery Level for Auto Enabling | 20% | Auto-Disable Battery Saver | Auto-disable when the battery is charged to 75%. If Battery Saver is enabled with battery higher than 75%, it will be auto-disabled when the battery reaches 100%. | Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_battery_saver_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136065506, "stop": 1756136078326, "duration": 12820}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136078326, "stop": 1756136078326, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set battery saver settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set battery saver settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Saver | When Battery Saver is on, system settings will be optimized, and background network status, location access, refresh rate, etc. will be restricted. | Switch Screen Refresh Rate To 60Hz | Enable Dark Theme | Screen Auto Off After 15 S | Enabling Battery Saver will optimize system settings and restrict some features.Tap for details | Battery Saver Notification | When the battery is below 20%, you will be notified to turn on Battery Saver. | Auto-enable Battery Saver Mode | Battery Level for Auto Enabling | 20% | Auto-Disable Battery Saver | Auto-disable when the battery is charged to 75%. If Battery Saver is enabled with battery higher than 75%, it will be auto-disabled when the battery reaches 100%. | Power Saving']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings object at 0x0000029205661090>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920968A610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_battery_saver_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set battery saver settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Saver | When Battery Saver is on, system settings will be optimized, and background network status, location access, refresh rate, etc. will be restricted. | Switch Screen Refresh Rate To 60Hz | Enable Dark Theme | Screen Auto Off After 15 S | Enabling Battery Saver will optimize system settings and restrict some features.Tap for details | Battery Saver Notification | When the battery is below 20%, you will be notified to turn on Battery Saver. | Auto-enable Battery Saver Mode | Battery Level for Auto Enabling | 20% | Auto-Disable Battery Saver | Auto-disable when the battery is charged to 75%. If Battery Saver is enabled with battery higher than 75%, it will be auto-disabled when the battery reaches 100%. | Power Saving']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_battery_saver_settings.py:33: AssertionError", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1756136078327, "stop": 1756136114118, "duration": 35791}, "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1756136078327, "stop": 1756136113846, "duration": 35519}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136113846, "stop": 1756136114118, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "a57732effc7a00b", "name": "测试总结", "source": "a57732effc7a00b.txt", "type": "text/plain", "size": 1069}, {"uid": "3e1096552c23115a", "name": "test_completed", "source": "3e1096552c23115a.png", "type": "image/png", "size": 168954}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136114118, "stop": 1756136114123, "duration": 5}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set battery saver settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Saver | When Battery Saver is on, system settings will be optimized, and background network status, location access, refresh rate, etc. will be restricted. | Switch Screen Refresh Rate To 60Hz | Enable Dark Theme | Screen Auto Off After 15 S | Enabling Battery Saver will optimize system settings and restrict some features.Tap for details | Battery Saver Notification | When the battery is below 20%, you will be notified to turn on Battery Saver. | Auto-enable Battery Saver Mode | Battery Level for Auto Enabling | 20% | Auto-Disable Battery Saver | Auto-disable when the battery is charged to 75%. If Battery Saver is enabled with battery higher than 75%, it will be auto-disabled when the battery reaches 100%. | Power Saving']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_battery_saver_settings.py\", line 33, in test_set_battery_saver_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9f24726ac7dc42bf", "name": "stdout", "source": "9f24726ac7dc42bf.txt", "type": "text/plain", "size": 18279}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136114130, "stop": 1756136114357, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "da3c9f046f2e9002", "name": "失败截图-TestEllaSetBatterySaverSettings", "source": "da3c9f046f2e9002.png", "type": "image/png", "size": 168929}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136114358, "stop": 1756136115836, "duration": 1478}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_battery_saver_settings"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "cfeb7998c2106e36.json", "parameterValues": []}