{"uid": "dc0709c8034d0524", "name": "测试navigation to the address in thie image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage#test_navigation_to_the_address_in_the_image", "historyId": "488c24d02f5d5348f35881280e505f32", "time": {"start": 1756134367910, "stop": 1756134400120, "duration": 32210}, "description": "navigation to the address in thie image", "descriptionHtml": "<p>navigation to the address in thie image</p>\n", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to an address on your phone at this time.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"11:06 Dialogue Explore Refresh Mavericks' Post-Doncic Draft Move Switch to a male voice Trump Expands Guard to Baltimore navigation to the address in thie image I am sorry, I am unable to navigate to an address on your phone at this time. Generated by AI, for reference only What navigation apps are available? Can you suggest alternatives? I cannot navigate now. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage object at 0x00000292054517D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209767B50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to an address on your phone at this time.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"11:06 Dialogue Explore Refresh Mavericks' Post-Doncic Draft Move Switch to a male voice Trump Expands Guard to Baltimore navigation to the address in thie image I am sorry, I am unable to navigate to an address on your phone at this time. Generated by AI, for reference only What navigation apps are available? Can you suggest alternatives? I cannot navigate now. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_address_in_the_image.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134354450, "stop": 1756134367908, "duration": 13458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134367909, "stop": 1756134367909, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigation to the address in thie image", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to an address on your phone at this time.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"11:06 Dialogue Explore Refresh Mavericks' Post-Doncic Draft Move Switch to a male voice Trump Expands Guard to Baltimore navigation to the address in thie image I am sorry, I am unable to navigate to an address on your phone at this time. Generated by AI, for reference only What navigation apps are available? Can you suggest alternatives? I cannot navigate now. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage object at 0x00000292054517D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209767B50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to an address on your phone at this time.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"11:06 Dialogue Explore Refresh Mavericks' Post-Doncic Draft Move Switch to a male voice Trump Expands Guard to Baltimore navigation to the address in thie image I am sorry, I am unable to navigate to an address on your phone at this time. Generated by AI, for reference only What navigation apps are available? Can you suggest alternatives? I cannot navigate now. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_address_in_the_image.py:36: AssertionError", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1756134367911, "stop": 1756134400118, "duration": 32207}, "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1756134367911, "stop": 1756134399854, "duration": 31943}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134399854, "stop": 1756134400118, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "7ce9fb9115b5fac9", "name": "测试总结", "source": "7ce9fb9115b5fac9.txt", "type": "text/plain", "size": 770}, {"uid": "d311a9dd2eae6ad8", "name": "test_completed", "source": "d311a9dd2eae6ad8.png", "type": "image/png", "size": 185149}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1756134400118, "stop": 1756134400118, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to an address on your phone at this time.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"11:06 Dialogue Explore Refresh Mavericks' Post-Doncic Draft Move Switch to a male voice Trump Expands Guard to Baltimore navigation to the address in thie image I am sorry, I am unable to navigate to an address on your phone at this time. Generated by AI, for reference only What navigation apps are available? Can you suggest alternatives? I cannot navigate now. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_navigation_to_the_address_in_the_image.py\", line 36, in test_navigation_to_the_address_in_the_image\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "937d66b67d3316f7", "name": "stdout", "source": "937d66b67d3316f7.txt", "type": "text/plain", "size": 15091}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134400124, "stop": 1756134400367, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "8c0f3194df84f072", "name": "失败截图-TestEllaNavigationAddressTheImage", "source": "8c0f3194df84f072.png", "type": "image/png", "size": 185149}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756134400371, "stop": 1756134401894, "duration": 1523}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationAddressTheImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "dc0709c8034d0524.json", "parameterValues": []}