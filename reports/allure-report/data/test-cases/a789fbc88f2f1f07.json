{"uid": "a789fbc88f2f1f07", "name": "测试help me write an thanks email能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email.TestEllaHelpMeWriteAnThanksEmail#test_help_me_write_an_thanks_email", "historyId": "306cbf11cdbcb045eb3c3c716515b1d6", "time": {"start": 1756132982447, "stop": 1756133012057, "duration": 29610}, "description": "help me write an thanks email", "descriptionHtml": "<p>help me write an thanks email</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132969684, "stop": 1756132982445, "duration": 12761}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132982446, "stop": 1756132982446, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "help me write an thanks email", "status": "passed", "steps": [{"name": "执行命令: help me write an thanks email", "time": {"start": 1756132982447, "stop": 1756133011794, "duration": 29347}, "status": "passed", "steps": [{"name": "执行命令: help me write an thanks email", "time": {"start": 1756132982447, "stop": 1756133011509, "duration": 29062}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133011509, "stop": 1756133011794, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "9620928dc740777a", "name": "测试总结", "source": "9620928dc740777a.txt", "type": "text/plain", "size": 2267}, {"uid": "8e2f2d7089cec6e7", "name": "test_completed", "source": "8e2f2d7089cec6e7.png", "type": "image/png", "size": 309266}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133011794, "stop": 1756133011795, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133011795, "stop": 1756133012056, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "7e4b1b968dcff187", "name": "测试总结", "source": "7e4b1b968dcff187.txt", "type": "text/plain", "size": 2267}, {"uid": "a10cb6d3ef4af444", "name": "test_completed", "source": "a10cb6d3ef4af444.png", "type": "image/png", "size": 310313}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d560af79099da5c2", "name": "stdout", "source": "d560af79099da5c2.txt", "type": "text/plain", "size": 18744}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133012057, "stop": 1756133012058, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133012059, "stop": 1756133013489, "duration": 1430}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_thanks_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnThanksEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a789fbc88f2f1f07.json", "parameterValues": []}