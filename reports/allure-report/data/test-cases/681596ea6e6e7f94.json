{"uid": "681596ea6e6e7f94", "name": "测试change your language to chinese能正常执行", "fullName": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese.TestEllaChangeYourLanguageChinese#test_change_your_language_to_chinese", "historyId": "9b6faa79e3fe09fed639b1092082745b", "time": {"start": 1756123859730, "stop": 1756123859730, "duration": 0}, "description": "change your language to chinese", "descriptionHtml": "<p>change your language to chinese</p>\n", "status": "skipped", "statusMessage": "Skipped: 语言设置为中文，影响别的Case，先跳过", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\system_coupling\\\\test_change_your_language_to_chinese.py', 17, 'Skipped: 语言设置为中文，影响别的Case，先跳过')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123871822, "stop": 1756123871822, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123913704, "stop": 1756123913704, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_change_your_language_to_chinese"}, {"name": "subSuite", "value": "TestEllaChangeYourLanguageChinese"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke", "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"]}, "source": "681596ea6e6e7f94.json", "parameterValues": []}