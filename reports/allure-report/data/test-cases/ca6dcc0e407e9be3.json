{"uid": "ca6dcc0e407e9be3", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord#test_stop_screen_recording", "historyId": "75c9edd252211f9e74fd8c1a2faeefd1", "time": {"start": 1756125530836, "stop": 1756125560411, "duration": 29575}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125518220, "stop": 1756125530834, "duration": 12614}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125530835, "stop": 1756125530835, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1756125530837, "stop": 1756125560176, "duration": 29339}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1756125530837, "stop": 1756125559912, "duration": 29075}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125559912, "stop": 1756125560176, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "c1e700e03d128703", "name": "测试总结", "source": "c1e700e03d128703.txt", "type": "text/plain", "size": 300}, {"uid": "8527db1b974e77aa", "name": "test_completed", "source": "8527db1b974e77aa.png", "type": "image/png", "size": 177043}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125560176, "stop": 1756125560179, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756125560179, "stop": 1756125560179, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125560179, "stop": 1756125560410, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "628249cbb6e4e6aa", "name": "测试总结", "source": "628249cbb6e4e6aa.txt", "type": "text/plain", "size": 300}, {"uid": "491a7c00aab2e7a0", "name": "test_completed", "source": "491a7c00aab2e7a0.png", "type": "image/png", "size": 177282}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4427321d35a5c74a", "name": "stdout", "source": "4427321d35a5c74a.txt", "type": "text/plain", "size": 13106}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125560415, "stop": 1756125560415, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125560416, "stop": 1756125561811, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_screen_record"}, {"name": "subSuite", "value": "TestEllaScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ca6dcc0e407e9be3.json", "parameterValues": []}