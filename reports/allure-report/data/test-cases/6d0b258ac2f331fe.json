{"uid": "6d0b258ac2f331fe", "name": "测试take a note on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse.TestEllaTakeNoteHowBuildTreehouse#test_take_a_note_on_how_to_build_a_treehouse", "historyId": "c45aa63628fe12b375ba7e65c39d93b1", "time": {"start": 1756121906470, "stop": 1756121932402, "duration": 25932}, "description": "take a note on how to build a treehouse", "descriptionHtml": "<p>take a note on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121893109, "stop": 1756121906468, "duration": 13359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121906468, "stop": 1756121906468, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take a note on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1756121906470, "stop": 1756121932158, "duration": 25688}, "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1756121906470, "stop": 1756121931875, "duration": 25405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121931875, "stop": 1756121932157, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "8e608834325558e4", "name": "测试总结", "source": "8e608834325558e4.txt", "type": "text/plain", "size": 345}, {"uid": "c11812a28b882f57", "name": "test_completed", "source": "c11812a28b882f57.png", "type": "image/png", "size": 159031}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121932158, "stop": 1756121932163, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121932163, "stop": 1756121932401, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "4f83ed9a1b8d939", "name": "测试总结", "source": "4f83ed9a1b8d939.txt", "type": "text/plain", "size": 345}, {"uid": "aa86cd09e63ec5b7", "name": "test_completed", "source": "aa86cd09e63ec5b7.png", "type": "image/png", "size": 159031}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e276960a0bb68fb4", "name": "stdout", "source": "e276960a0bb68fb4.txt", "type": "text/plain", "size": 12746}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121932403, "stop": 1756121932403, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121932406, "stop": 1756121933787, "duration": 1381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNoteHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6d0b258ac2f331fe.json", "parameterValues": []}