{"uid": "2f6a2092b790ffff", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings#test_jump_to_notifications_and_status_bar_settings", "historyId": "d8bd499fa9e4e04741c5c255fac9036d", "time": {"start": 1756133958045, "stop": 1756133995121, "duration": 37076}, "description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to notifications and status bar settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to notifications and status bar settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', \"[com.android.settings页面内容] Notifications & Status Bar | Search applications | Notification Style | Lock Screen | Banners | Badges | AI Reminder | When it's enabled, unimportant notifications will be auto-muted. | Adaptative to Notifications | Automatically lower the volume when you receive a lot of notifications in a short period of time. | More Settings | Status Bar | Sort by Time | Google Play Store\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings object at 0x00000292053D2810>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209761610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_notifications_and_status_bar_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to notifications and status bar settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', \"[com.android.settings页面内容] Notifications & Status Bar | Search applications | Notification Style | Lock Screen | Banners | Badges | AI Reminder | When it's enabled, unimportant notifications will be auto-muted. | Adaptative to Notifications | Automatically lower the volume when you receive a lot of notifications in a short period of time. | More Settings | Status Bar | Sort by Time | Google Play Store\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_notifications_and_status_bar_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133944907, "stop": 1756133958042, "duration": 13135}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133958043, "stop": 1756133958043, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to notifications and status bar settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', \"[com.android.settings页面内容] Notifications & Status Bar | Search applications | Notification Style | Lock Screen | Banners | Badges | AI Reminder | When it's enabled, unimportant notifications will be auto-muted. | Adaptative to Notifications | Automatically lower the volume when you receive a lot of notifications in a short period of time. | More Settings | Status Bar | Sort by Time | Google Play Store\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings object at 0x00000292053D2810>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209761610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_notifications_and_status_bar_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to notifications and status bar settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', \"[com.android.settings页面内容] Notifications & Status Bar | Search applications | Notification Style | Lock Screen | Banners | Badges | AI Reminder | When it's enabled, unimportant notifications will be auto-muted. | Adaptative to Notifications | Automatically lower the volume when you receive a lot of notifications in a short period of time. | More Settings | Status Bar | Sort by Time | Google Play Store\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_notifications_and_status_bar_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "time": {"start": 1756133958045, "stop": 1756133995110, "duration": 37065}, "status": "passed", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "time": {"start": 1756133958045, "stop": 1756133994847, "duration": 36802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133994847, "stop": 1756133995109, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "502d52fa7b29d09", "name": "测试总结", "source": "502d52fa7b29d09.txt", "type": "text/plain", "size": 742}, {"uid": "c893daaf501ca487", "name": "test_completed", "source": "c893daaf501ca487.png", "type": "image/png", "size": 180174}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133995110, "stop": 1756133995116, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to notifications and status bar settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', \"[com.android.settings页面内容] Notifications & Status Bar | Search applications | Notification Style | Lock Screen | Banners | Badges | AI Reminder | When it's enabled, unimportant notifications will be auto-muted. | Adaptative to Notifications | Automatically lower the volume when you receive a lot of notifications in a short period of time. | More Settings | Status Bar | Sort by Time | Google Play Store\"]'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_notifications_and_status_bar_settings.py\", line 33, in test_jump_to_notifications_and_status_bar_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "134daf6c7b71108", "name": "stdout", "source": "134daf6c7b71108.txt", "type": "text/plain", "size": 17439}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133995128, "stop": 1756133995352, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "e67cfc929930d924", "name": "失败截图-TestEllaJumpNotificationsStatusBarSettings", "source": "e67cfc929930d924.png", "type": "image/png", "size": 180174}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133995354, "stop": 1756133996821, "duration": 1467}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_notifications_and_status_bar_settings"}, {"name": "subSuite", "value": "TestEllaJumpNotificationsStatusBarSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "2f6a2092b790ffff.json", "parameterValues": []}