{"uid": "41059a075bf61a4a", "name": "测试set my themes返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes#test_set_my_themes", "historyId": "084048a337d3081654dc4414f67fce70", "time": {"start": 1756136853540, "stop": 1756136889091, "duration": 35551}, "description": "验证set my themes指令返回预期的不支持响应", "descriptionHtml": "<p>验证set my themes指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my themes', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Wallpapers & Personalization | Wallpapers | My Themes | 11:47 | Mon,Aug 25 | Lock Screen | Lock Screen | Fonts | Always On Display | Customization | Online Themes | Home Screen Settings | System Colors']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes object at 0x0000029205725B10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A852C90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_my_themes(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my themes', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Wallpapers & Personalization | Wallpapers | My Themes | 11:47 | Mon,Aug 25 | Lock Screen | Lock Screen | Fonts | Always On Display | Customization | Online Themes | Home Screen Settings | System Colors']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_my_themes.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136840583, "stop": 1756136853538, "duration": 12955}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136853538, "stop": 1756136853538, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set my themes指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my themes', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Wallpapers & Personalization | Wallpapers | My Themes | 11:47 | Mon,Aug 25 | Lock Screen | Lock Screen | Fonts | Always On Display | Customization | Online Themes | Home Screen Settings | System Colors']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes object at 0x0000029205725B10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A852C90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_my_themes(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my themes', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Wallpapers & Personalization | Wallpapers | My Themes | 11:47 | Mon,Aug 25 | Lock Screen | Lock Screen | Fonts | Always On Display | Customization | Online Themes | Home Screen Settings | System Colors']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_my_themes.py:33: AssertionError", "steps": [{"name": "执行命令: set my themes", "time": {"start": 1756136853540, "stop": 1756136889085, "duration": 35545}, "status": "passed", "steps": [{"name": "执行命令: set my themes", "time": {"start": 1756136853540, "stop": 1756136888780, "duration": 35240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136888780, "stop": 1756136889085, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "8c26e24fb5357e38", "name": "测试总结", "source": "8c26e24fb5357e38.txt", "type": "text/plain", "size": 502}, {"uid": "99ac23a327d885b4", "name": "test_completed", "source": "99ac23a327d885b4.png", "type": "image/png", "size": 168340}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136889085, "stop": 1756136889088, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set my themes', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Wallpapers & Personalization | Wallpapers | My Themes | 11:47 | Mon,Aug 25 | Lock Screen | Lock Screen | Fonts | Always On Display | Customization | Online Themes | Home Screen Settings | System Colors']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_my_themes.py\", line 33, in test_set_my_themes\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "85b0f9992d397fb3", "name": "stdout", "source": "85b0f9992d397fb3.txt", "type": "text/plain", "size": 15934}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136889094, "stop": 1756136889328, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "bb2fb729c2dc3cb7", "name": "失败截图-TestEllaSetMyThemes", "source": "bb2fb729c2dc3cb7.png", "type": "image/png", "size": 168305}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136889329, "stop": 1756136890762, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_themes"}, {"name": "subSuite", "value": "TestEllaSetMyThemes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_themes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "41059a075bf61a4a.json", "parameterValues": []}