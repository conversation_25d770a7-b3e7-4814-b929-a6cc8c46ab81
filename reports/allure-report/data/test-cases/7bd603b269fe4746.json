{"uid": "7bd603b269fe4746", "name": "测试help me generate a picture of a bamboo forest stream", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_bamboo_forest_stream.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "historyId": "9e84af065ec0018044fd43f37b4c2179", "time": {"start": 1756132614832, "stop": 1756132641222, "duration": 26390}, "description": "测试help me generate a picture of a bamboo forest stream指令", "descriptionHtml": "<p>测试help me generate a picture of a bamboo forest stream指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132601911, "stop": 1756132614830, "duration": 12919}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132614830, "stop": 1756132614831, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试help me generate a picture of a bamboo forest stream指令", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a bamboo forest stream", "time": {"start": 1756132614832, "stop": 1756132640981, "duration": 26149}, "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a bamboo forest stream", "time": {"start": 1756132614832, "stop": 1756132640710, "duration": 25878}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132640710, "stop": 1756132640980, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "2b42656e569ca0f9", "name": "测试总结", "source": "2b42656e569ca0f9.txt", "type": "text/plain", "size": 387}, {"uid": "2eb1a8f4b2271e3", "name": "test_completed", "source": "2eb1a8f4b2271e3.png", "type": "image/png", "size": 186701}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132640981, "stop": 1756132640984, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132640984, "stop": 1756132641222, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "2afef73a2ea4aeb6", "name": "测试总结", "source": "2afef73a2ea4aeb6.txt", "type": "text/plain", "size": 387}, {"uid": "190b061b3636f773", "name": "test_completed", "source": "190b061b3636f773.png", "type": "image/png", "size": 186701}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9c842a3deda949e4", "name": "stdout", "source": "9c842a3deda949e4.txt", "type": "text/plain", "size": 13312}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132641223, "stop": 1756132641223, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132641224, "stop": 1756132642609, "duration": 1385}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_bamboo_forest_stream"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7bd603b269fe4746.json", "parameterValues": []}