{"uid": "9d74aadbd0be1e60", "name": "测试turn on the 7AM alarm", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_7am_alarm.TestEllaOpenClock#test_turn_on_the_am_alarm", "historyId": "d235f5ef7da67b833ad362b7c693c8f1", "time": {"start": 1756128152512, "stop": 1756128257419, "duration": 104907}, "description": "测试turn on the 7AM alarm指令", "descriptionHtml": "<p>测试turn on the 7AM alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128139838, "stop": 1756128152509, "duration": 12671}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128152509, "stop": 1756128152509, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn on the 7AM alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756128152512, "stop": 1756128178075, "duration": 25563}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756128152512, "stop": 1756128177791, "duration": 25279}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128177791, "stop": 1756128178074, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "85145e7be9333818", "name": "测试总结", "source": "85145e7be9333818.txt", "type": "text/plain", "size": 303}, {"uid": "44f0dd45c30b7680", "name": "test_completed", "source": "44f0dd45c30b7680.png", "type": "image/png", "size": 180347}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 7 am", "time": {"start": 1756128178075, "stop": 1756128204210, "duration": 26135}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 7 am", "time": {"start": 1756128178075, "stop": 1756128203944, "duration": 25869}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128203944, "stop": 1756128204209, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "861f7cffee04bb48", "name": "测试总结", "source": "861f7cffee04bb48.txt", "type": "text/plain", "size": 242}, {"uid": "1fb8331f69e3316f", "name": "test_completed", "source": "1fb8331f69e3316f.png", "type": "image/png", "size": 145740}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn on the 7AM alarm", "time": {"start": 1756128204210, "stop": 1756128230498, "duration": 26288}, "status": "passed", "steps": [{"name": "执行命令: turn on the 7AM alarm", "time": {"start": 1756128204210, "stop": 1756128230211, "duration": 26001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128230211, "stop": 1756128230498, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "16b5e20f770e2b41", "name": "测试总结", "source": "16b5e20f770e2b41.txt", "type": "text/plain", "size": 238}, {"uid": "ce6c98d0d9bc480", "name": "test_completed", "source": "ce6c98d0d9bc480.png", "type": "image/png", "size": 145232}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756128230498, "stop": 1756128257182, "duration": 26684}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756128230498, "stop": 1756128256926, "duration": 26428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128256926, "stop": 1756128257182, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "ac9dae57330832be", "name": "测试总结", "source": "ac9dae57330832be.txt", "type": "text/plain", "size": 223}, {"uid": "fc85efde1ff10d35", "name": "test_completed", "source": "fc85efde1ff10d35.png", "type": "image/png", "size": 140332}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128257182, "stop": 1756128257185, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128257185, "stop": 1756128257418, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "270ee9e5fcd25c47", "name": "测试总结", "source": "270ee9e5fcd25c47.txt", "type": "text/plain", "size": 226}, {"uid": "172439e3a4c35a44", "name": "test_completed", "source": "172439e3a4c35a44.png", "type": "image/png", "size": 140550}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ae8ff3de10834a6c", "name": "stdout", "source": "ae8ff3de10834a6c.txt", "type": "text/plain", "size": 41126}], "parameters": [], "attachmentsCount": 11, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 14, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128257419, "stop": 1756128257419, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128257421, "stop": 1756128258793, "duration": 1372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_7am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_7am_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9d74aadbd0be1e60.json", "parameterValues": []}