{"uid": "bbe94ed2552a6418", "name": "测试Modify grape timbre返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_modify_grape_timbre.TestEllaEnableRunningLock#test_modify_grape_timbre", "historyId": "db313838c77140c89e69785e101f25d7", "time": {"start": 1756134238603, "stop": 1756134264814, "duration": 26211}, "description": "验证Modify grape timbre指令返回预期的不支持响应", "descriptionHtml": "<p>验证Modify grape timbre指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134225619, "stop": 1756134238601, "duration": 12982}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134238601, "stop": 1756134238601, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证Modify grape timbre指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Modify grape timbre", "time": {"start": 1756134238603, "stop": 1756134264561, "duration": 25958}, "status": "passed", "steps": [{"name": "执行命令: Modify grape timbre", "time": {"start": 1756134238603, "stop": 1756134264273, "duration": 25670}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134264273, "stop": 1756134264560, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "699fcc38fb66b03f", "name": "测试总结", "source": "699fcc38fb66b03f.txt", "type": "text/plain", "size": 295}, {"uid": "6958aa529f777790", "name": "test_completed", "source": "6958aa529f777790.png", "type": "image/png", "size": 178757}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134264561, "stop": 1756134264563, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134264563, "stop": 1756134264813, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "b75beae700650f65", "name": "测试总结", "source": "b75beae700650f65.txt", "type": "text/plain", "size": 295}, {"uid": "97fa0f1f449576bc", "name": "test_completed", "source": "97fa0f1f449576bc.png", "type": "image/png", "size": 178757}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "187116f7ff497f2d", "name": "stdout", "source": "187116f7ff497f2d.txt", "type": "text/plain", "size": 12483}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134264815, "stop": 1756134264815, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134264816, "stop": 1756134266268, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_modify_grape_timbre"}, {"name": "subSuite", "value": "TestEllaEnableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_modify_grape_timbre"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bbe94ed2552a6418.json", "parameterValues": []}