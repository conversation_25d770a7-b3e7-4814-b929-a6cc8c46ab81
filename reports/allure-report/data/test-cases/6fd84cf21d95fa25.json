{"uid": "6fd84cf21d95fa25", "name": "测试what date is it能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_what_date_is_it.TestEllaWhatDateIsIt#test_what_date_is_it", "historyId": "44e27936b56f219f63af671a8fd7f5fb", "time": {"start": 1756138863268, "stop": 1756138888250, "duration": 24982}, "description": "what date is it", "descriptionHtml": "<p>what date is it</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138850517, "stop": 1756138863266, "duration": 12749}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138863266, "stop": 1756138863267, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what date is it", "status": "passed", "steps": [{"name": "执行命令: what date is it", "time": {"start": 1756138863268, "stop": 1756138888002, "duration": 24734}, "status": "passed", "steps": [{"name": "执行命令: what date is it", "time": {"start": 1756138863268, "stop": 1756138887744, "duration": 24476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138887744, "stop": 1756138888002, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "56ceff7e6e1a9a62", "name": "测试总结", "source": "56ceff7e6e1a9a62.txt", "type": "text/plain", "size": 286}, {"uid": "53d1436f4aa232b8", "name": "test_completed", "source": "53d1436f4aa232b8.png", "type": "image/png", "size": 169916}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138888002, "stop": 1756138888007, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138888007, "stop": 1756138888250, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "5732c83f3eb946a8", "name": "测试总结", "source": "5732c83f3eb946a8.txt", "type": "text/plain", "size": 286}, {"uid": "abbafc6b48820b81", "name": "test_completed", "source": "abbafc6b48820b81.png", "type": "image/png", "size": 169916}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b3da8fd6fc79c6b0", "name": "stdout", "source": "b3da8fd6fc79c6b0.txt", "type": "text/plain", "size": 12443}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138888251, "stop": 1756138888251, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138888252, "stop": 1756138889645, "duration": 1393}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_date_is_it"}, {"name": "subSuite", "value": "TestEllaWhatDateIsIt"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_date_is_it"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6fd84cf21d95fa25.json", "parameterValues": []}