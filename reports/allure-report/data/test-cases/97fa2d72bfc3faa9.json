{"uid": "97fa2d72bfc3faa9", "name": "测试turn on auto rotate screen能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_auto_rotate_screen.TestEllaTurnAutoRotateScreen#test_turn_on_auto_rotate_screen", "historyId": "0f4d3881c287fa46a9fdaf099fc19f8d", "time": {"start": 1756127779941, "stop": 1756127806414, "duration": 26473}, "description": "turn on auto rotate screen", "descriptionHtml": "<p>turn on auto rotate screen</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127767094, "stop": 1756127779938, "duration": 12844}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127779938, "stop": 1756127779938, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on auto rotate screen", "status": "passed", "steps": [{"name": "执行命令: turn on auto rotate screen", "time": {"start": 1756127779941, "stop": 1756127806191, "duration": 26250}, "status": "passed", "steps": [{"name": "执行命令: turn on auto rotate screen", "time": {"start": 1756127779941, "stop": 1756127805928, "duration": 25987}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127805928, "stop": 1756127806191, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "31bf9f6642b85712", "name": "测试总结", "source": "31bf9f6642b85712.txt", "type": "text/plain", "size": 248}, {"uid": "224ff96f1c0642dc", "name": "test_completed", "source": "224ff96f1c0642dc.png", "type": "image/png", "size": 153729}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127806192, "stop": 1756127806193, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127806193, "stop": 1756127806193, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127806193, "stop": 1756127806413, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "363de11450b6c35e", "name": "测试总结", "source": "363de11450b6c35e.txt", "type": "text/plain", "size": 248}, {"uid": "a52f61ff47146a67", "name": "test_completed", "source": "a52f61ff47146a67.png", "type": "image/png", "size": 153729}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "248b4ac21d579e78", "name": "stdout", "source": "248b4ac21d579e78.txt", "type": "text/plain", "size": 12999}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127806414, "stop": 1756127806414, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127806415, "stop": 1756127807772, "duration": 1357}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_auto_rotate_screen"}, {"name": "subSuite", "value": "TestEllaTurnAutoRotateScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_auto_rotate_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "97fa2d72bfc3faa9.json", "parameterValues": []}