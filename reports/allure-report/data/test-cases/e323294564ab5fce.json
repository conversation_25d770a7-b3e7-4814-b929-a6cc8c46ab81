{"uid": "e323294564ab5fce", "name": "测试turn on the screen record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord#test_turn_on_the_screen_record", "historyId": "5cf50058091f34fd1ed89d0b8f717355", "time": {"start": 1756128313690, "stop": 1756128374403, "duration": 60713}, "description": "turn on the screen record", "descriptionHtml": "<p>turn on the screen record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128301019, "stop": 1756128313688, "duration": 12669}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128313688, "stop": 1756128313688, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1756128313690, "stop": 1756128342690, "duration": 29000}, "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1756128313690, "stop": 1756128342404, "duration": 28714}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128342404, "stop": 1756128342690, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "85988d30603a1d36", "name": "测试总结", "source": "85988d30603a1d36.txt", "type": "text/plain", "size": 307}, {"uid": "8def861fb242a32a", "name": "test_completed", "source": "8def861fb242a32a.png", "type": "image/png", "size": 183851}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128342690, "stop": 1756128342692, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756128342692, "stop": 1756128342692, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128342692, "stop": 1756128342933, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "33ed53f8fc431f49", "name": "测试总结", "source": "33ed53f8fc431f49.txt", "type": "text/plain", "size": 307}, {"uid": "d72abe56524ec24c", "name": "test_completed", "source": "d72abe56524ec24c.png", "type": "image/png", "size": 183496}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "执行命令: turn on the screen record", "time": {"start": 1756128342933, "stop": 1756128374401, "duration": 31468}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1756128342933, "stop": 1756128374118, "duration": 31185}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128374118, "stop": 1756128374401, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "67b17bffa963881", "name": "测试总结", "source": "67b17bffa963881.txt", "type": "text/plain", "size": 304}, {"uid": "c00bd5701ccf192e", "name": "test_completed", "source": "c00bd5701ccf192e.png", "type": "image/png", "size": 174449}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}], "attachments": [{"uid": "c162a540f237ed71", "name": "stdout", "source": "c162a540f237ed71.txt", "type": "text/plain", "size": 23108}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 9, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128374403, "stop": 1756128374403, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128374404, "stop": 1756128375786, "duration": 1382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_screen_record"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e323294564ab5fce.json", "parameterValues": []}