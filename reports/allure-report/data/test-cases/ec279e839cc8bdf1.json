{"uid": "ec279e839cc8bdf1", "name": "测试Three Little Pigs", "fullName": "testcases.test_ella.unsupported_commands.test_three_little_pigs.TestEllaOpenPlayPoliticalNews#test_three_little_pigs", "historyId": "44720253edec52ccf0868b33f1938265", "time": {"start": 1756138483262, "stop": 1756138510021, "duration": 26759}, "description": "测试Three Little Pigs指令", "descriptionHtml": "<p>测试Three Little Pigs指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138470635, "stop": 1756138483260, "duration": 12625}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138483260, "stop": 1756138483260, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Three Little Pigs指令", "status": "passed", "steps": [{"name": "执行命令: Three Little Pigs", "time": {"start": 1756138483262, "stop": 1756138509806, "duration": 26544}, "status": "passed", "steps": [{"name": "执行命令: Three Little Pigs", "time": {"start": 1756138483262, "stop": 1756138509502, "duration": 26240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138509502, "stop": 1756138509806, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "fda616d69a3657d8", "name": "测试总结", "source": "fda616d69a3657d8.txt", "type": "text/plain", "size": 885}, {"uid": "86f9f16de789cfa9", "name": "test_completed", "source": "86f9f16de789cfa9.png", "type": "image/png", "size": 191897}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138509806, "stop": 1756138509808, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138509808, "stop": 1756138510019, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "2d7aa2d13ef73483", "name": "测试总结", "source": "2d7aa2d13ef73483.txt", "type": "text/plain", "size": 885}, {"uid": "b97578c0347a2802", "name": "test_completed", "source": "b97578c0347a2802.png", "type": "image/png", "size": 191897}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7fccfea91c7c7ed", "name": "stdout", "source": "7fccfea91c7c7ed.txt", "type": "text/plain", "size": 15505}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138510021, "stop": 1756138510021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138510022, "stop": 1756138511382, "duration": 1360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_three_little_pigs"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_three_little_pigs"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ec279e839cc8bdf1.json", "parameterValues": []}