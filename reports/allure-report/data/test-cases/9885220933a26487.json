{"uid": "9885220933a26487", "name": "测试Switch Magic Voice to Grace能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace.TestEllaSwitchMagicVoiceGrace#test_switch_magic_voice_to_grace", "historyId": "04cef4934fe29e90ca0248af7b395794", "time": {"start": 1756126542110, "stop": 1756126568769, "duration": 26659}, "description": "Switch Magic Voice to Grace", "descriptionHtml": "<p><PERSON>witch Magic Voice to Grace</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126529373, "stop": 1756126542109, "duration": 12736}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126542109, "stop": 1756126542109, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Switch Magic Voice to Grace", "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "time": {"start": 1756126542111, "stop": 1756126568540, "duration": 26429}, "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "time": {"start": 1756126542111, "stop": 1756126568306, "duration": 26195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126568306, "stop": 1756126568540, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "769642ec2d992e71", "name": "测试总结", "source": "769642ec2d992e71.txt", "type": "text/plain", "size": 296}, {"uid": "a1f674635da005a", "name": "test_completed", "source": "a1f674635da005a.png", "type": "image/png", "size": 193757}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756126568540, "stop": 1756126568543, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126568543, "stop": 1756126568768, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "9d81b29e4fe713fe", "name": "测试总结", "source": "9d81b29e4fe713fe.txt", "type": "text/plain", "size": 296}, {"uid": "71a368499d435026", "name": "test_completed", "source": "71a368499d435026.png", "type": "image/png", "size": 193757}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f92cb3c7b0fbd056", "name": "stdout", "source": "f92cb3c7b0fbd056.txt", "type": "text/plain", "size": 12864}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126568769, "stop": 1756126568769, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126568772, "stop": 1756126570120, "duration": 1348}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_grace"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceGrace"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9885220933a26487.json", "parameterValues": []}