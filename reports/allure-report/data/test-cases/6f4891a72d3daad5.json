{"uid": "6f4891a72d3daad5", "name": "测试how to say i love you in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french.TestEllaHowSayILoveYouFrench#test_how_to_say_i_love_you_in_french", "historyId": "78de5607a6208f59723ba6cf4fcf09c4", "time": {"start": 1756120161082, "stop": 1756120187781, "duration": 26699}, "description": "how to say i love you in french", "descriptionHtml": "<p>how to say i love you in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120148091, "stop": 1756120161080, "duration": 12989}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120161081, "stop": 1756120161081, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "how to say i love you in french", "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1756120161082, "stop": 1756120187560, "duration": 26478}, "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1756120161082, "stop": 1756120187307, "duration": 26225}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120187307, "stop": 1756120187560, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "d54bfd4b098b05f", "name": "测试总结", "source": "d54bfd4b098b05f.txt", "type": "text/plain", "size": 216}, {"uid": "140a83dbba34bb41", "name": "test_completed", "source": "140a83dbba34bb41.png", "type": "image/png", "size": 149819}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120187561, "stop": 1756120187562, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120187562, "stop": 1756120187780, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "d80f352d4b804438", "name": "测试总结", "source": "d80f352d4b804438.txt", "type": "text/plain", "size": 216}, {"uid": "f5e3bdc89a505df0", "name": "test_completed", "source": "f5e3bdc89a505df0.png", "type": "image/png", "size": 149819}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "35916b99ad7c9004", "name": "stdout", "source": "35916b99ad7c9004.txt", "type": "text/plain", "size": 12314}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120187782, "stop": 1756120187782, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120187785, "stop": 1756120189122, "duration": 1337}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_i_love_you_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayILoveYouFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6f4891a72d3daad5.json", "parameterValues": []}