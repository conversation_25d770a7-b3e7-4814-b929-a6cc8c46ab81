{"uid": "5568e7b3deaed795", "name": "测试min notifications volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_min_notifications_volume.TestEllaMinNotificationsVolume#test_min_notifications_volume", "historyId": "511b4baaab6d7793eefd3f92b3a77d8b", "time": {"start": 1756125147200, "stop": 1756125173368, "duration": 26168}, "description": "min notifications volume", "descriptionHtml": "<p>min notifications volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125134141, "stop": 1756125147198, "duration": 13057}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125147199, "stop": 1756125147199, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "min notifications volume", "status": "passed", "steps": [{"name": "执行命令: min notifications volume", "time": {"start": 1756125147200, "stop": 1756125173110, "duration": 25910}, "status": "passed", "steps": [{"name": "执行命令: min notifications volume", "time": {"start": 1756125147201, "stop": 1756125172864, "duration": 25663}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125172864, "stop": 1756125173109, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "42bd9dce9cff63eb", "name": "测试总结", "source": "42bd9dce9cff63eb.txt", "type": "text/plain", "size": 321}, {"uid": "e89b07986ec9a02f", "name": "test_completed", "source": "e89b07986ec9a02f.png", "type": "image/png", "size": 178221}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125173110, "stop": 1756125173113, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756125173114, "stop": 1756125173114, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125173114, "stop": 1756125173367, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "f3af3eea60fb6bec", "name": "测试总结", "source": "f3af3eea60fb6bec.txt", "type": "text/plain", "size": 321}, {"uid": "7f5af3f8e5d2e5ae", "name": "test_completed", "source": "7f5af3f8e5d2e5ae.png", "type": "image/png", "size": 178461}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5e784efbcae3a98a", "name": "stdout", "source": "5e784efbcae3a98a.txt", "type": "text/plain", "size": 14017}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125173370, "stop": 1756125173370, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125173373, "stop": 1756125174754, "duration": 1381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_notifications_volume"}, {"name": "subSuite", "value": "TestEllaMinNotificationsVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_notifications_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5568e7b3deaed795.json", "parameterValues": []}