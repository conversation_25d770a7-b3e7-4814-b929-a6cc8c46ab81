{"uid": "fc71d950c9d0094e", "name": "测试could you please search an for me能正常执行", "fullName": "testcases.test_ella.dialogue.test_could_you_please_search_an_for_me.TestEllaCouldYouPleaseSearchAnMe#test_could_you_please_search_an_for_me", "historyId": "19cc9ff22947964a912a8f57a87a3c68", "time": {"start": 1756119600732, "stop": 1756119627333, "duration": 26601}, "description": "could you please search an for me", "descriptionHtml": "<p>could you please search an for me</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119587814, "stop": 1756119600730, "duration": 12916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119600730, "stop": 1756119600730, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "could you please search an for me", "status": "passed", "steps": [{"name": "执行命令: could you please search an for me", "time": {"start": 1756119600732, "stop": 1756119627124, "duration": 26392}, "status": "passed", "steps": [{"name": "执行命令: could you please search an for me", "time": {"start": 1756119600732, "stop": 1756119626886, "duration": 26154}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119626886, "stop": 1756119627124, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "e8e64b238a065d68", "name": "测试总结", "source": "e8e64b238a065d68.txt", "type": "text/plain", "size": 702}, {"uid": "b752896edd8ca2", "name": "test_completed", "source": "b752896edd8ca2.png", "type": "image/png", "size": 185378}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119627124, "stop": 1756119627126, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119627126, "stop": 1756119627332, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "3e742e055aee7d61", "name": "测试总结", "source": "3e742e055aee7d61.txt", "type": "text/plain", "size": 702}, {"uid": "4628935661b80c9a", "name": "test_completed", "source": "4628935661b80c9a.png", "type": "image/png", "size": 185378}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3ec793f75b117cf6", "name": "stdout", "source": "3ec793f75b117cf6.txt", "type": "text/plain", "size": 14723}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119627333, "stop": 1756119627333, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119627335, "stop": 1756119628706, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_could_you_please_search_an_for_me"}, {"name": "subSuite", "value": "TestEllaCouldYouPleaseSearchAnMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_could_you_please_search_an_for_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fc71d950c9d0094e.json", "parameterValues": []}