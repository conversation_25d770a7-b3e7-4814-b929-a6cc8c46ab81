{"uid": "1cec63fdd4caa30b", "name": "测试continue playing能正常执行", "fullName": "testcases.test_ella.dialogue.test_continue_playing.TestEllaHowIsWeatherToday#test_continue_playing", "historyId": "309f3fbb8586cc15e324e94cec37e7ea", "time": {"start": 1756119559318, "stop": 1756119586367, "duration": 27049}, "description": "continue playing", "descriptionHtml": "<p>continue playing</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119546374, "stop": 1756119559317, "duration": 12943}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119559317, "stop": 1756119559317, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "continue playing", "status": "passed", "steps": [{"name": "执行命令: continue playing", "time": {"start": 1756119559318, "stop": 1756119586141, "duration": 26823}, "status": "passed", "steps": [{"name": "执行命令: continue playing", "time": {"start": 1756119559318, "stop": 1756119585918, "duration": 26600}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119585919, "stop": 1756119586139, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "c39293c70d6c485f", "name": "测试总结", "source": "c39293c70d6c485f.txt", "type": "text/plain", "size": 300}, {"uid": "c6578d66becdc6bf", "name": "test_completed", "source": "c6578d66becdc6bf.png", "type": "image/png", "size": 173367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119586141, "stop": 1756119586144, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119586144, "stop": 1756119586367, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "70fefb1cbcf55de", "name": "测试总结", "source": "70fefb1cbcf55de.txt", "type": "text/plain", "size": 300}, {"uid": "4e3ae3ba33e0939", "name": "test_completed", "source": "4e3ae3ba33e0939.png", "type": "image/png", "size": 173367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4505b6ed2beced5", "name": "stdout", "source": "4505b6ed2beced5.txt", "type": "text/plain", "size": 12901}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119586368, "stop": 1756119586368, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119586370, "stop": 1756119587803, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_continue_playing"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_continue_playing"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1cec63fdd4caa30b.json", "parameterValues": []}