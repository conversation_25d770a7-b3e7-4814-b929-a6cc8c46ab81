{"uid": "219e12099f4b6b74", "name": "测试go to office", "fullName": "testcases.test_ella.unsupported_commands.test_go_to_office.TestEllaOpenPlayPoliticalNews#test_go_to_office", "historyId": "02dda06829926b51f170419357629e86", "time": {"start": 1756132281236, "stop": 1756132307920, "duration": 26684}, "description": "测试go to office指令", "descriptionHtml": "<p>测试go to office指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132268367, "stop": 1756132281235, "duration": 12868}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132281235, "stop": 1756132281235, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试go to office指令", "status": "passed", "steps": [{"name": "执行命令: go to office", "time": {"start": 1756132281237, "stop": 1756132307698, "duration": 26461}, "status": "passed", "steps": [{"name": "执行命令: go to office", "time": {"start": 1756132281237, "stop": 1756132307442, "duration": 26205}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132307442, "stop": 1756132307697, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "6b9b88a2c3c41736", "name": "测试总结", "source": "6b9b88a2c3c41736.txt", "type": "text/plain", "size": 204}, {"uid": "883b5878d023ed3b", "name": "test_completed", "source": "883b5878d023ed3b.png", "type": "image/png", "size": 162367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132307698, "stop": 1756132307701, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132307701, "stop": 1756132307920, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "1fb0fc0a550a5cae", "name": "测试总结", "source": "1fb0fc0a550a5cae.txt", "type": "text/plain", "size": 204}, {"uid": "550ee9a2a121d234", "name": "test_completed", "source": "550ee9a2a121d234.png", "type": "image/png", "size": 162367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "102c7ef1c66646f5", "name": "stdout", "source": "102c7ef1c66646f5.txt", "type": "text/plain", "size": 12404}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756132307921, "stop": 1756132309311, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756132307921, "stop": 1756132307921, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_go_to_office"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_go_to_office"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "219e12099f4b6b74.json", "parameterValues": []}