{"uid": "4a7e9321605b3c0", "name": "测试help me generate a picture of blue and gold landscape", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_blue_and_gold_landscape.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_blue_and_gold_landscape", "historyId": "eadc304b3069d4918c06805d847a62d7", "time": {"start": 1756132817917, "stop": 1756132843905, "duration": 25988}, "description": "测试help me generate a picture of blue and gold landscape指令", "descriptionHtml": "<p>测试help me generate a picture of blue and gold landscape指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132804887, "stop": 1756132817916, "duration": 13029}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132817916, "stop": 1756132817916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试help me generate a picture of blue and gold landscape指令", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of blue and gold landscape", "time": {"start": 1756132817917, "stop": 1756132843665, "duration": 25748}, "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of blue and gold landscape", "time": {"start": 1756132817917, "stop": 1756132843421, "duration": 25504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132843421, "stop": 1756132843664, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "ac37e41bb52edd41", "name": "测试总结", "source": "ac37e41bb52edd41.txt", "type": "text/plain", "size": 389}, {"uid": "7dae44b1152e6de2", "name": "test_completed", "source": "7dae44b1152e6de2.png", "type": "image/png", "size": 185882}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132843665, "stop": 1756132843668, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132843668, "stop": 1756132843904, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "1f44ef8deb989d08", "name": "测试总结", "source": "1f44ef8deb989d08.txt", "type": "text/plain", "size": 389}, {"uid": "695cc3ec067c7041", "name": "test_completed", "source": "695cc3ec067c7041.png", "type": "image/png", "size": 185882}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "20c3b766626fc9b5", "name": "stdout", "source": "20c3b766626fc9b5.txt", "type": "text/plain", "size": 13132}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132843906, "stop": 1756132843906, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132843907, "stop": 1756132845302, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_blue_and_gold_landscape"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_blue_and_gold_landscape"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4a7e9321605b3c0.json", "parameterValues": []}