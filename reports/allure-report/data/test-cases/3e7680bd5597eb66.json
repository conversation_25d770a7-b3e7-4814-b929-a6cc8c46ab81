{"uid": "3e7680bd5597eb66", "name": "测试measure heart rate", "fullName": "testcases.test_ella.dialogue.test_measure_heart_rate.TestEllaOpenPlayPoliticalNews#test_measure_heart_rate", "historyId": "de524bccf252aabc016822f1a65de7f4", "time": {"start": 1756120589922, "stop": 1756120615568, "duration": 25646}, "description": "测试measure heart rate指令", "descriptionHtml": "<p>测试measure heart rate指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120576953, "stop": 1756120589920, "duration": 12967}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120589920, "stop": 1756120589920, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试measure heart rate指令", "status": "passed", "steps": [{"name": "执行命令: measure heart rate", "time": {"start": 1756120589922, "stop": 1756120615348, "duration": 25426}, "status": "passed", "steps": [{"name": "执行命令: measure heart rate", "time": {"start": 1756120589923, "stop": 1756120615063, "duration": 25140}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120615063, "stop": 1756120615348, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "4a5e698b6ec9c6ab", "name": "测试总结", "source": "4a5e698b6ec9c6ab.txt", "type": "text/plain", "size": 294}, {"uid": "ee156d558bab9376", "name": "test_completed", "source": "ee156d558bab9376.png", "type": "image/png", "size": 178986}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120615348, "stop": 1756120615352, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120615352, "stop": 1756120615568, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "f5dba30d29c62741", "name": "测试总结", "source": "f5dba30d29c62741.txt", "type": "text/plain", "size": 294}, {"uid": "484a2fbf343ed4d8", "name": "test_completed", "source": "484a2fbf343ed4d8.png", "type": "image/png", "size": 179178}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "41ddbbb51c6d4c4a", "name": "stdout", "source": "41ddbbb51c6d4c4a.txt", "type": "text/plain", "size": 12615}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120615568, "stop": 1756120615568, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120615570, "stop": 1756120616939, "duration": 1369}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_measure_heart_rate"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_measure_heart_rate"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3e7680bd5597eb66.json", "parameterValues": []}