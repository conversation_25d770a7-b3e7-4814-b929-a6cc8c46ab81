{"uid": "207de737dda6aa47", "name": "测试set special function返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_special_function.TestEllaSetSpecialFunction#test_set_special_function", "historyId": "c046a1c6e6cc8effa10641e329b1cfad", "time": {"start": 1756137567228, "stop": 1756137603616, "duration": 36388}, "description": "验证set special function指令返回预期的不支持响应", "descriptionHtml": "<p>验证set special function指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set special function', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_special_function.TestEllaSetSpecialFunction object at 0x00000292057E9710>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920968B390>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_special_function(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set special function', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_special_function.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137553920, "stop": 1756137567224, "duration": 13304}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137567224, "stop": 1756137567225, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set special function指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set special function', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_special_function.TestEllaSetSpecialFunction object at 0x00000292057E9710>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920968B390>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_special_function(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set special function', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_special_function.py:33: AssertionError", "steps": [{"name": "执行命令: set special function", "time": {"start": 1756137567228, "stop": 1756137603610, "duration": 36382}, "status": "passed", "steps": [{"name": "执行命令: set special function", "time": {"start": 1756137567228, "stop": 1756137603314, "duration": 36086}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137603314, "stop": 1756137603610, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "1974749c74de695e", "name": "测试总结", "source": "1974749c74de695e.txt", "type": "text/plain", "size": 863}, {"uid": "9e90792fc432cbc3", "name": "test_completed", "source": "9e90792fc432cbc3.png", "type": "image/png", "size": 165088}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137603610, "stop": 1756137603615, "duration": 5}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set special function', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Special Function | Dynamic Port | Display important service information in an efficient, concise, and interesting way. | Floating Windows | Convenient and efficient floating windows for all apps in all scenes | Split-screen Apps | 2 apps will concurrently display on the screen. | Smart Panel | Multitasking windows and smart scenario assistants | Outdoor Booster | Enhance brightness, volume, and network quality when used outdoor. | Game Mode | Improve game experience. | Kids Mode | Help guide kids toward healthy digital habits. | More Settings']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_special_function.py\", line 33, in test_set_special_function\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e242dbdfef5f8506", "name": "stdout", "source": "e242dbdfef5f8506.txt", "type": "text/plain", "size": 17076}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137603620, "stop": 1756137603852, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "40ac67fe3e041b9d", "name": "失败截图-TestEllaSetSpecialFunction", "source": "40ac67fe3e041b9d.png", "type": "image/png", "size": 165073}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137603852, "stop": 1756137605408, "duration": 1556}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_special_function"}, {"name": "subSuite", "value": "TestEllaSetSpecialFunction"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_special_function"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "207de737dda6aa47.json", "parameterValues": []}