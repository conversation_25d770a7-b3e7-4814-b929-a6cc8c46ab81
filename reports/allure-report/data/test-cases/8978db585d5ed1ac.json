{"uid": "8978db585d5ed1ac", "name": "测试what time is it in china能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_china.TestEllaWhatTimeIsItChina#test_what_time_is_it_in_china", "historyId": "7ebb09688c661659cc2b4a26d54a347f", "time": {"start": 1756139071112, "stop": 1756139098311, "duration": 27199}, "description": "what time is it in china", "descriptionHtml": "<p>what time is it in china</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756139058110, "stop": 1756139071111, "duration": 13001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756139071111, "stop": 1756139071111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what time is it in china", "status": "passed", "steps": [{"name": "执行命令: what time is it in china", "time": {"start": 1756139071112, "stop": 1756139098066, "duration": 26954}, "status": "passed", "steps": [{"name": "执行命令: what time is it in china", "time": {"start": 1756139071112, "stop": 1756139097814, "duration": 26702}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139097814, "stop": 1756139098066, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "e204b85d9d6d7423", "name": "测试总结", "source": "e204b85d9d6d7423.txt", "type": "text/plain", "size": 303}, {"uid": "147dddfc2082d92b", "name": "test_completed", "source": "147dddfc2082d92b.png", "type": "image/png", "size": 173123}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756139098066, "stop": 1756139098067, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756139098067, "stop": 1756139098067, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139098067, "stop": 1756139098311, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "13c68727196b41d4", "name": "测试总结", "source": "13c68727196b41d4.txt", "type": "text/plain", "size": 303}, {"uid": "3f7911f053ee0526", "name": "test_completed", "source": "3f7911f053ee0526.png", "type": "image/png", "size": 173123}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f351d2653dedd539", "name": "stdout", "source": "f351d2653dedd539.txt", "type": "text/plain", "size": 12834}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756139098311, "stop": 1756139098311, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756139098312, "stop": 1756139099677, "duration": 1365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_time_is_it_in_china"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItChina"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_china"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8978db585d5ed1ac.json", "parameterValues": []}