{"uid": "78f632744fe35263", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim#test_check_mobile_data_balance_of_sim", "historyId": "7c50481bc992b9ff109abbeeeece073a", "time": {"start": 1756130201203, "stop": 1756130226969, "duration": 25766}, "description": "验证check mobile data balance of sim2指令返回预期的不支持响应", "descriptionHtml": "<p>验证check mobile data balance of sim2指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130188413, "stop": 1756130201202, "duration": 12789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130201202, "stop": 1756130201202, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证check mobile data balance of sim2指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check mobile data balance of sim2", "time": {"start": 1756130201203, "stop": 1756130226693, "duration": 25490}, "status": "passed", "steps": [{"name": "执行命令: check mobile data balance of sim2", "time": {"start": 1756130201203, "stop": 1756130226427, "duration": 25224}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130226427, "stop": 1756130226693, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "f64906391159800d", "name": "测试总结", "source": "f64906391159800d.txt", "type": "text/plain", "size": 344}, {"uid": "4922f70835a35e2", "name": "test_completed", "source": "4922f70835a35e2.png", "type": "image/png", "size": 173685}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130226693, "stop": 1756130226696, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130226696, "stop": 1756130226969, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "e55ec4b183559ee7", "name": "测试总结", "source": "e55ec4b183559ee7.txt", "type": "text/plain", "size": 344}, {"uid": "8cdcd3be89d2c0ac", "name": "test_completed", "source": "8cdcd3be89d2c0ac.png", "type": "image/png", "size": 173685}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "26a2050138fb6e83", "name": "stdout", "source": "26a2050138fb6e83.txt", "type": "text/plain", "size": 13060}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130226970, "stop": 1756130226970, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130226971, "stop": 1756130228360, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_mobile_data_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMobileDataBalanceSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "78f632744fe35263.json", "parameterValues": []}