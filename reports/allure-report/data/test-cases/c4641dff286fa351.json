{"uid": "c4641dff286fa351", "name": "测试yandex eats返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_yandex_eats.TestEllaYandexEats#test_yandex_eats", "historyId": "b911308f3c1fe764715d778a884946c2", "time": {"start": 1756139233550, "stop": 1756139259627, "duration": 26077}, "description": "验证yandex eats指令返回预期的不支持响应", "descriptionHtml": "<p>验证yandex eats指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756139220674, "stop": 1756139233548, "duration": 12874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756139233548, "stop": 1756139233548, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证yandex eats指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1756139233550, "stop": 1756139259377, "duration": 25827}, "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1756139233550, "stop": 1756139259118, "duration": 25568}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139259118, "stop": 1756139259377, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "bd1eb839de5f4c68", "name": "测试总结", "source": "bd1eb839de5f4c68.txt", "type": "text/plain", "size": 321}, {"uid": "5d6e2d2005d1aad8", "name": "test_completed", "source": "5d6e2d2005d1aad8.png", "type": "image/png", "size": 184306}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756139259377, "stop": 1756139259378, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139259378, "stop": 1756139259626, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "c5ada628a1b8acdd", "name": "测试总结", "source": "c5ada628a1b8acdd.txt", "type": "text/plain", "size": 321}, {"uid": "7d4f44df97251072", "name": "test_completed", "source": "7d4f44df97251072.png", "type": "image/png", "size": 184306}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cfbd3d7f73dce42d", "name": "stdout", "source": "cfbd3d7f73dce42d.txt", "type": "text/plain", "size": 12761}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756139259629, "stop": 1756139259629, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756139259630, "stop": 1756139260988, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_yandex_eats"}, {"name": "subSuite", "value": "TestEllaYandexEats"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_yandex_eats"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c4641dff286fa351.json", "parameterValues": []}