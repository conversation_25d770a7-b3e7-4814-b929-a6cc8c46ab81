{"uid": "69ff8f3cf5b09dbf", "name": "测试resume music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_resume_music.TestEllaResumeMusic#test_resume_music", "historyId": "44b646d68146a0c48da2623a58b17f6f", "time": {"start": 1756118642356, "stop": 1756118668235, "duration": 25879}, "description": "resume music", "descriptionHtml": "<p>resume music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118629552, "stop": 1756118642355, "duration": 12803}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118642356, "stop": 1756118642356, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "resume music", "status": "passed", "steps": [{"name": "执行命令: resume music", "time": {"start": 1756118642356, "stop": 1756118667997, "duration": 25641}, "status": "passed", "steps": [{"name": "执行命令: resume music", "time": {"start": 1756118642356, "stop": 1756118667727, "duration": 25371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118667727, "stop": 1756118667996, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "d9a08487686fe886", "name": "测试总结", "source": "d9a08487686fe886.txt", "type": "text/plain", "size": 290}, {"uid": "7ca19b176815043a", "name": "test_completed", "source": "7ca19b176815043a.png", "type": "image/png", "size": 175604}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118667997, "stop": 1756118668000, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118668000, "stop": 1756118668234, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "821b6d2439704a7b", "name": "测试总结", "source": "821b6d2439704a7b.txt", "type": "text/plain", "size": 290}, {"uid": "3bd0c87e3b015665", "name": "test_completed", "source": "3bd0c87e3b015665.png", "type": "image/png", "size": 175604}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5762417bb705782c", "name": "stdout", "source": "5762417bb705782c.txt", "type": "text/plain", "size": 12709}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118668236, "stop": 1756118668236, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118668237, "stop": 1756118669622, "duration": 1385}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_resume_music"}, {"name": "subSuite", "value": "TestEllaResumeMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_resume_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "69ff8f3cf5b09dbf.json", "parameterValues": []}