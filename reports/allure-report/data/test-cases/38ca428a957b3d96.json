{"uid": "38ca428a957b3d96", "name": "测试open countdown能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_countdown.TestEllaCommandConcise#test_open_countdown", "historyId": "37d8f85ba7c46a46b390c4fc5ab20de7", "time": {"start": 1756117835281, "stop": 1756117860953, "duration": 25672}, "description": "open countdown", "descriptionHtml": "<p>open countdown</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117822670, "stop": 1756117835280, "duration": 12610}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117835280, "stop": 1756117835280, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "open countdown", "status": "passed", "steps": [{"name": "执行命令: open countdown", "time": {"start": 1756117835281, "stop": 1756117860732, "duration": 25451}, "status": "passed", "steps": [{"name": "执行命令: open countdown", "time": {"start": 1756117835281, "stop": 1756117860467, "duration": 25186}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117860467, "stop": 1756117860732, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "87104a2e52806696", "name": "测试总结", "source": "87104a2e52806696.txt", "type": "text/plain", "size": 272}, {"uid": "e4e6ffc68c6e92c3", "name": "test_completed", "source": "e4e6ffc68c6e92c3.png", "type": "image/png", "size": 160677}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756117860732, "stop": 1756117860734, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117860734, "stop": 1756117860953, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "19acaf37a50dd1fc", "name": "测试总结", "source": "19acaf37a50dd1fc.txt", "type": "text/plain", "size": 272}, {"uid": "b3f855e9ab5ae278", "name": "test_completed", "source": "b3f855e9ab5ae278.png", "type": "image/png", "size": 160861}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f8f01464f71ca440", "name": "stdout", "source": "f8f01464f71ca440.txt", "type": "text/plain", "size": 12509}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117860954, "stop": 1756117860954, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117860961, "stop": 1756117862396, "duration": 1435}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_countdown"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_countdown"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "38ca428a957b3d96.json", "parameterValues": []}