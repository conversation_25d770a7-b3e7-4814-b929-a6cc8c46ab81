{"uid": "d35e034cca390355", "name": "测试who is j k rowling能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_j_k_rowling.TestEllaWhoIsJKRowling#test_who_is_j_k_rowling", "historyId": "1a5cbbb97cbe59e003ae71750a8d910f", "time": {"start": 1756122639198, "stop": 1756122666887, "duration": 27689}, "description": "who is j k rowling", "descriptionHtml": "<p>who is j k rowling</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122626583, "stop": 1756122639197, "duration": 12614}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122639197, "stop": 1756122639197, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "who is j k rowling", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1756122639198, "stop": 1756122666665, "duration": 27467}, "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1756122639198, "stop": 1756122666400, "duration": 27202}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122666400, "stop": 1756122666664, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "7740e034265af6e9", "name": "测试总结", "source": "7740e034265af6e9.txt", "type": "text/plain", "size": 793}, {"uid": "bd77a0ba13afad81", "name": "test_completed", "source": "bd77a0ba13afad81.png", "type": "image/png", "size": 216314}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122666665, "stop": 1756122666667, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122666667, "stop": 1756122666887, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "ee824634d48cc302", "name": "测试总结", "source": "ee824634d48cc302.txt", "type": "text/plain", "size": 793}, {"uid": "7d01f07a9cb34fba", "name": "test_completed", "source": "7d01f07a9cb34fba.png", "type": "image/png", "size": 215869}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "31960240d1aa2709", "name": "stdout", "source": "31960240d1aa2709.txt", "type": "text/plain", "size": 15272}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756122666888, "stop": 1756122668237, "duration": 1349}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756122666888, "stop": 1756122666888, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowling"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_j_k_rowling"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "d35e034cca390355.json", "parameterValues": []}