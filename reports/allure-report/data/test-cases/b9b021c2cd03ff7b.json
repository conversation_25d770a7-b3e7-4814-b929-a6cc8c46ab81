{"uid": "b9b021c2cd03ff7b", "name": "测试open dialer能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "historyId": "936ae2bf6db744b69d4acf28b22f7646", "time": {"start": 1756117875700, "stop": 1756117914463, "duration": 38763}, "description": "open dialer", "descriptionHtml": "<p>open dialer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117862401, "stop": 1756117875698, "duration": 13297}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117875698, "stop": 1756117875698, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1756117875700, "stop": 1756117914234, "duration": 38534}, "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1756117875700, "stop": 1756117913942, "duration": 38242}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117913942, "stop": 1756117914233, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "5a01c53f17a57cc4", "name": "测试总结", "source": "5a01c53f17a57cc4.txt", "type": "text/plain", "size": 314}, {"uid": "10614d79f02d2e98", "name": "test_completed", "source": "10614d79f02d2e98.png", "type": "image/png", "size": 174753}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756117914234, "stop": 1756117914236, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117914236, "stop": 1756117914462, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "32327f9fb47413f8", "name": "测试总结", "source": "32327f9fb47413f8.txt", "type": "text/plain", "size": 314}, {"uid": "b7e86657b20377a3", "name": "test_completed", "source": "b7e86657b20377a3.png", "type": "image/png", "size": 174908}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "56cf4659d9c4a511", "name": "stdout", "source": "56cf4659d9c4a511.txt", "type": "text/plain", "size": 15143}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117914464, "stop": 1756117914464, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117914466, "stop": 1756117915854, "duration": 1388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_dialer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b9b021c2cd03ff7b.json", "parameterValues": []}