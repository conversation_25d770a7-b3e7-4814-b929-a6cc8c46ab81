{"uid": "4bd331cc7ac68abc", "name": "测试set nfc tag", "fullName": "testcases.test_ella.unsupported_commands.test_set_nfc_tag.TestEllaOpenPlayPoliticalNews#test_set_nfc_tag", "historyId": "7d8296483e3dfc0902b42ccfe6759e59", "time": {"start": 1756136903754, "stop": 1756136938222, "duration": 34468}, "description": "测试set nfc tag指令", "descriptionHtml": "<p>测试set nfc tag指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136890769, "stop": 1756136903752, "duration": 12983}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136903752, "stop": 1756136903752, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试set nfc tag指令", "status": "passed", "steps": [{"name": "执行命令: set nfc tag", "time": {"start": 1756136903754, "stop": 1756136937989, "duration": 34235}, "status": "passed", "steps": [{"name": "执行命令: set nfc tag", "time": {"start": 1756136903754, "stop": 1756136937681, "duration": 33927}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136937681, "stop": 1756136937989, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "797a686a85bd9d95", "name": "测试总结", "source": "797a686a85bd9d95.txt", "type": "text/plain", "size": 514}, {"uid": "80e737b6f685a5a5", "name": "test_completed", "source": "80e737b6f685a5a5.png", "type": "image/png", "size": 157680}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756136937989, "stop": 1756136937990, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证settings已打开", "time": {"start": 1756136937990, "stop": 1756136937990, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136937990, "stop": 1756136938221, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "8bc8b1855b174c73", "name": "测试总结", "source": "8bc8b1855b174c73.txt", "type": "text/plain", "size": 514}, {"uid": "10939736577c11e6", "name": "test_completed", "source": "10939736577c11e6.png", "type": "image/png", "size": 157680}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "35713f752a2dbc42", "name": "stdout", "source": "35713f752a2dbc42.txt", "type": "text/plain", "size": 15636}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136938223, "stop": 1756136938223, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136938229, "stop": 1756136939759, "duration": 1530}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_nfc_tag"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_nfc_tag"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4bd331cc7ac68abc.json", "parameterValues": []}