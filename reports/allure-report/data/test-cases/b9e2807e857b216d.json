{"uid": "b9e2807e857b216d", "name": "测试set color style返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle#test_set_color_style", "historyId": "e2beda2a0bda4155b33d47f14bdcb9ed", "time": {"start": 1756136177803, "stop": 1756136213900, "duration": 36097}, "description": "验证set color style指令返回预期的不支持响应", "descriptionHtml": "<p>验证set color style指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set color style', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle object at 0x00000292056815D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A841410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_color_style(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set color style', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_color_style.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136164961, "stop": 1756136177802, "duration": 12841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136177802, "stop": 1756136177802, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set color style指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set color style', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle object at 0x00000292056815D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A841410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_color_style(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set color style', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_color_style.py:33: AssertionError", "steps": [{"name": "执行命令: set color style", "time": {"start": 1756136177803, "stop": 1756136213895, "duration": 36092}, "status": "passed", "steps": [{"name": "执行命令: set color style", "time": {"start": 1756136177804, "stop": 1756136213585, "duration": 35781}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136213585, "stop": 1756136213895, "duration": 310}, "status": "passed", "steps": [], "attachments": [{"uid": "7f296b0d5c7ced4b", "name": "测试总结", "source": "7f296b0d5c7ced4b.txt", "type": "text/plain", "size": 696}, {"uid": "4ce6259426cdeb73", "name": "test_completed", "source": "4ce6259426cdeb73.png", "type": "image/png", "size": 166582}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136213895, "stop": 1756136213898, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set color style', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_color_style.py\", line 33, in test_set_color_style\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "33eb88bb69bbae88", "name": "stdout", "source": "33eb88bb69bbae88.txt", "type": "text/plain", "size": 16507}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136213905, "stop": 1756136214132, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "e16f8b98dce5871c", "name": "失败截图-TestEllaSetColorStyle", "source": "e16f8b98dce5871c.png", "type": "image/png", "size": 166402}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136214133, "stop": 1756136215697, "duration": 1564}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_color_style"}, {"name": "subSuite", "value": "TestEllaSetColorStyle"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_color_style"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b9e2807e857b216d.json", "parameterValues": []}