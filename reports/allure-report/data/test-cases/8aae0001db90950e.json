{"uid": "8aae0001db90950e", "name": "测试go home能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_go_home.TestEllaGoHome#test_go_home", "historyId": "c612b04b455e8fbc03acd18c2fc89827", "time": {"start": 1756132239064, "stop": 1756132266908, "duration": 27844}, "description": "go home", "descriptionHtml": "<p>go home</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132226315, "stop": 1756132239063, "duration": 12748}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132239063, "stop": 1756132239063, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "go home", "status": "passed", "steps": [{"name": "执行命令: go home", "time": {"start": 1756132239064, "stop": 1756132266651, "duration": 27587}, "status": "passed", "steps": [{"name": "执行命令: go home", "time": {"start": 1756132239064, "stop": 1756132266403, "duration": 27339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132266403, "stop": 1756132266650, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "2d9437caf18f28ab", "name": "测试总结", "source": "2d9437caf18f28ab.txt", "type": "text/plain", "size": 191}, {"uid": "cb3fe45e786af331", "name": "test_completed", "source": "cb3fe45e786af331.png", "type": "image/png", "size": 160415}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132266651, "stop": 1756132266653, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132266653, "stop": 1756132266908, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "76d42537bb9d93a3", "name": "测试总结", "source": "76d42537bb9d93a3.txt", "type": "text/plain", "size": 191}, {"uid": "a8f720d302dd84e5", "name": "test_completed", "source": "a8f720d302dd84e5.png", "type": "image/png", "size": 160188}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "db49d53026de3b38", "name": "stdout", "source": "db49d53026de3b38.txt", "type": "text/plain", "size": 12135}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132266909, "stop": 1756132266909, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132266911, "stop": 1756132268354, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_go_home"}, {"name": "subSuite", "value": "TestEllaGoHome"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_go_home"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8aae0001db90950e.json", "parameterValues": []}