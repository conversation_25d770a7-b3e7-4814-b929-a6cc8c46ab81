{"uid": "4b53ab85f85882c6", "name": "测试open camera能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_camera.TestEllaCommandConcise#test_open_camera", "historyId": "ae0ee984c3712fd05ea04b52289e14fe", "time": {"start": 1756117676591, "stop": 1756117712240, "duration": 35649}, "description": "open camera", "descriptionHtml": "<p>open camera</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117664739, "stop": 1756117676591, "duration": 11852}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117676591, "stop": 1756117676591, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1756117676591, "stop": 1756117712016, "duration": 35425}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1756117676591, "stop": 1756117711744, "duration": 35153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117711744, "stop": 1756117712014, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "500101a01c82cd52", "name": "测试总结", "source": "500101a01c82cd52.txt", "type": "text/plain", "size": 357}, {"uid": "62b79e481fdc79bf", "name": "test_completed", "source": "62b79e481fdc79bf.png", "type": "image/png", "size": 172713}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756117712016, "stop": 1756117712017, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756117712017, "stop": 1756117712017, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117712017, "stop": 1756117712240, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "3227a6df4f9f6c13", "name": "测试总结", "source": "3227a6df4f9f6c13.txt", "type": "text/plain", "size": 357}, {"uid": "b9fd72188591d200", "name": "test_completed", "source": "b9fd72188591d200.png", "type": "image/png", "size": 172805}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "920d7cf090a385a6", "name": "stdout", "source": "920d7cf090a385a6.txt", "type": "text/plain", "size": 15269}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117712241, "stop": 1756117712241, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117712242, "stop": 1756117713612, "duration": 1370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4b53ab85f85882c6.json", "parameterValues": []}