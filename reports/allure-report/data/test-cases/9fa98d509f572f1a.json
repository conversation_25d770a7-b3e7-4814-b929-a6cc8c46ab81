{"uid": "9fa98d509f572f1a", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "fullName": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit.TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit#test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "historyId": "12eb3852c333145c5906579f2346c37a", "time": {"start": 1756119851600, "stop": 1756119881576, "duration": 29976}, "description": "Help me write an email to make an appointment for a visit", "descriptionHtml": "<p>Help me write an email to make an appointment for a visit</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119838894, "stop": 1756119851600, "duration": 12706}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119851600, "stop": 1756119851600, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Help me write an email to make an appointment for a visit", "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1756119851600, "stop": 1756119881331, "duration": 29731}, "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1756119851600, "stop": 1756119881015, "duration": 29415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119881015, "stop": 1756119881331, "duration": 316}, "status": "passed", "steps": [], "attachments": [{"uid": "ad5464f80aeb7859", "name": "测试总结", "source": "ad5464f80aeb7859.txt", "type": "text/plain", "size": 1964}, {"uid": "4954f9add5a30d63", "name": "test_completed", "source": "4954f9add5a30d63.png", "type": "image/png", "size": 280645}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119881331, "stop": 1756119881332, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119881332, "stop": 1756119881575, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "e26057122dda3b65", "name": "测试总结", "source": "e26057122dda3b65.txt", "type": "text/plain", "size": 1964}, {"uid": "c48a4d6b00c7b51f", "name": "test_completed", "source": "c48a4d6b00c7b51f.png", "type": "image/png", "size": 280784}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a363b43dd1359cfc", "name": "stdout", "source": "a363b43dd1359cfc.txt", "type": "text/plain", "size": 17971}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119881577, "stop": 1756119881577, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119881579, "stop": 1756119882994, "duration": 1415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9fa98d509f572f1a.json", "parameterValues": []}