{"uid": "57a88dce54efec1", "name": "测试appeler maman能正常执行", "fullName": "testcases.test_ella.dialogue.test_appeler_maman.TestEllaAppelerMaman#test_appeler_maman", "historyId": "c5050ea089fe0f7a5b962119cd32b32e", "time": {"start": 1756119266565, "stop": 1756119292266, "duration": 25701}, "description": "appeler maman", "descriptionHtml": "<p>appeler maman</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119254153, "stop": 1756119266564, "duration": 12411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119266564, "stop": 1756119266564, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "appeler maman", "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1756119266566, "stop": 1756119292063, "duration": 25497}, "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1756119266566, "stop": 1756119291788, "duration": 25222}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119291789, "stop": 1756119292062, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "eaabad87a6a293b6", "name": "测试总结", "source": "eaabad87a6a293b6.txt", "type": "text/plain", "size": 301}, {"uid": "f93b9e0f2e94d537", "name": "test_completed", "source": "f93b9e0f2e94d537.png", "type": "image/png", "size": 169595}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119292063, "stop": 1756119292064, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119292064, "stop": 1756119292266, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "dfc98f29509d46f6", "name": "测试总结", "source": "dfc98f29509d46f6.txt", "type": "text/plain", "size": 301}, {"uid": "9320e59e8426528c", "name": "test_completed", "source": "9320e59e8426528c.png", "type": "image/png", "size": 169595}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e34ac6b3dde0f960", "name": "stdout", "source": "e34ac6b3dde0f960.txt", "type": "text/plain", "size": 12469}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119292267, "stop": 1756119292267, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119292268, "stop": 1756119293649, "duration": 1381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_appeler_maman"}, {"name": "subSuite", "value": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_appeler_maman"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "57a88dce54efec1.json", "parameterValues": []}