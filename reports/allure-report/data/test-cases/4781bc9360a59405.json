{"uid": "4781bc9360a59405", "name": "测试i wanna be rich能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_wanna_be_rich.TestEllaIWannaBeRich#test_i_wanna_be_rich", "historyId": "6ecc7e0fc961d0d4e7e46672c033625a", "time": {"start": 1756120202032, "stop": 1756120231026, "duration": 28994}, "description": "i wanna be rich", "descriptionHtml": "<p>i wanna be rich</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120189132, "stop": 1756120202031, "duration": 12899}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120202031, "stop": 1756120202031, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "i wanna be rich", "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1756120202032, "stop": 1756120230803, "duration": 28771}, "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1756120202032, "stop": 1756120230518, "duration": 28486}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120230518, "stop": 1756120230803, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "eb0bd202b46c8856", "name": "测试总结", "source": "eb0bd202b46c8856.txt", "type": "text/plain", "size": 288}, {"uid": "7d5ddd3248d05818", "name": "test_completed", "source": "7d5ddd3248d05818.png", "type": "image/png", "size": 178985}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120230803, "stop": 1756120230805, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120230805, "stop": 1756120231025, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "bbce936e8f750e8a", "name": "测试总结", "source": "bbce936e8f750e8a.txt", "type": "text/plain", "size": 288}, {"uid": "f7d66ce7db9703e9", "name": "test_completed", "source": "f7d66ce7db9703e9.png", "type": "image/png", "size": 179448}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "89dbdd4b8603d5e9", "name": "stdout", "source": "89dbdd4b8603d5e9.txt", "type": "text/plain", "size": 12705}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120231026, "stop": 1756120231026, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120231028, "stop": 1756120232382, "duration": 1354}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_wanna_be_rich"}, {"name": "subSuite", "value": "TestEllaIWannaBeRich"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_wanna_be_rich"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4781bc9360a59405.json", "parameterValues": []}