{"uid": "b9c49f7bee0bf7f6", "name": "测试disable all ai magic box features返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features.TestEllaDisableAllAiMagicBoxFeatures#test_disable_all_ai_magic_box_features", "historyId": "********************************", "time": {"start": 1756130766397, "stop": 1756130792047, "duration": 25650}, "description": "验证disable all ai magic box features指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable all ai magic box features指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130753750, "stop": 1756130766396, "duration": 12646}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130766396, "stop": 1756130766396, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable all ai magic box features指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable all ai magic box features", "time": {"start": 1756130766397, "stop": 1756130791799, "duration": 25402}, "status": "passed", "steps": [{"name": "执行命令: disable all ai magic box features", "time": {"start": 1756130766397, "stop": 1756130791519, "duration": 25122}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130791519, "stop": 1756130791798, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "d960cf353be2a319", "name": "测试总结", "source": "d960cf353be2a319.txt", "type": "text/plain", "size": 361}, {"uid": "3df0c9637be6b1de", "name": "test_completed", "source": "3df0c9637be6b1de.png", "type": "image/png", "size": 189356}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130791799, "stop": 1756130791800, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130791800, "stop": 1756130792047, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "75614f7f67badca5", "name": "测试总结", "source": "75614f7f67badca5.txt", "type": "text/plain", "size": 361}, {"uid": "46a2dce18609302c", "name": "test_completed", "source": "46a2dce18609302c.png", "type": "image/png", "size": 189356}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b480b2334d5ad4be", "name": "stdout", "source": "b480b2334d5ad4be.txt", "type": "text/plain", "size": 12724}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130792048, "stop": 1756130792048, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130792049, "stop": 1756130793480, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaDisableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b9c49f7bee0bf7f6.json", "parameterValues": []}