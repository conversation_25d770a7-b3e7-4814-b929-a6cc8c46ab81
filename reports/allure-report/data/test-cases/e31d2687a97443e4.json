{"uid": "e31d2687a97443e4", "name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "fullName": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_walks_on_a_forest_meadow.TestEllaOpenPlayPoliticalNews#test_a_little_raccoon_walks_on_a_forest_meadow", "historyId": "468e62202269d66d1ea3c0ae6e2a0e21", "time": {"start": 1756129700164, "stop": 1756129729817, "duration": 29653}, "description": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance指令", "descriptionHtml": "<p>测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129687033, "stop": 1756129700162, "duration": 13129}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129700163, "stop": 1756129700163, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance指令", "status": "passed", "steps": [{"name": "执行命令: a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "time": {"start": 1756129700164, "stop": 1756129729428, "duration": 29264}, "status": "passed", "steps": [{"name": "执行命令: a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "time": {"start": 1756129700164, "stop": 1756129729005, "duration": 28841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129729005, "stop": 1756129729427, "duration": 422}, "status": "passed", "steps": [], "attachments": [{"uid": "dda56dbb68ed7f72", "name": "测试总结", "source": "dda56dbb68ed7f72.txt", "type": "text/plain", "size": 794}, {"uid": "de8e0b723cb58ce4", "name": "test_completed", "source": "de8e0b723cb58ce4.png", "type": "image/png", "size": 759514}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129729428, "stop": 1756129729429, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129729429, "stop": 1756129729816, "duration": 387}, "status": "passed", "steps": [], "attachments": [{"uid": "17dda27774e5439e", "name": "测试总结", "source": "17dda27774e5439e.txt", "type": "text/plain", "size": 794}, {"uid": "535184813174787a", "name": "test_completed", "source": "535184813174787a.png", "type": "image/png", "size": 759782}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "53771bfb58ce9efe", "name": "stdout", "source": "53771bfb58ce9efe.txt", "type": "text/plain", "size": 15594}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129729817, "stop": 1756129729817, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129729819, "stop": 1756129731259, "duration": 1440}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_little_raccoon_walks_on_a_forest_meadow"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_walks_on_a_forest_meadow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e31d2687a97443e4.json", "parameterValues": []}