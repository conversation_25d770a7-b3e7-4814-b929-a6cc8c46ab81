{"uid": "ff429f90e0fae416", "name": "测试parking space能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_parking_space.TestEllaParkingSpace#test_parking_space", "historyId": "217ee9f7b3be9f625903076716d45106", "time": {"start": 1756134976163, "stop": 1756135002265, "duration": 26102}, "description": "parking space", "descriptionHtml": "<p>parking space</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134963247, "stop": 1756134976160, "duration": 12913}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134976161, "stop": 1756134976161, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "parking space", "status": "passed", "steps": [{"name": "执行命令: parking space", "time": {"start": 1756134976163, "stop": 1756135002045, "duration": 25882}, "status": "passed", "steps": [{"name": "执行命令: parking space", "time": {"start": 1756134976163, "stop": 1756135001780, "duration": 25617}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135001780, "stop": 1756135002045, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "23e778cb628a36be", "name": "测试总结", "source": "23e778cb628a36be.txt", "type": "text/plain", "size": 308}, {"uid": "a14a6d2df8f712c8", "name": "test_completed", "source": "a14a6d2df8f712c8.png", "type": "image/png", "size": 179993}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135002045, "stop": 1756135002047, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135002047, "stop": 1756135002263, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "11dadf3e84a57bef", "name": "测试总结", "source": "11dadf3e84a57bef.txt", "type": "text/plain", "size": 308}, {"uid": "3ad30ffa62b8617e", "name": "test_completed", "source": "3ad30ffa62b8617e.png", "type": "image/png", "size": 179993}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "829d57475c6b3fc2", "name": "stdout", "source": "829d57475c6b3fc2.txt", "type": "text/plain", "size": 12519}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135002267, "stop": 1756135002267, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135002274, "stop": 1756135003689, "duration": 1415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_parking_space"}, {"name": "subSuite", "value": "TestEllaParkingSpace"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_parking_space"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ff429f90e0fae416.json", "parameterValues": []}