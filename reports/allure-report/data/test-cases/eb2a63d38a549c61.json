{"uid": "eb2a63d38a549c61", "name": "测试happy new year能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_happy_new_year.TestEllaHappyNewYear#test_happy_new_year", "historyId": "20bd2e7c8179ca1901bc63c9a02b9ee1", "time": {"start": 1756132407016, "stop": 1756132436162, "duration": 29146}, "description": "happy new year", "descriptionHtml": "<p>happy new year</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132394142, "stop": 1756132407013, "duration": 12871}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132407014, "stop": 1756132407014, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "happy new year", "status": "passed", "steps": [{"name": "执行命令: happy new year", "time": {"start": 1756132407016, "stop": 1756132435937, "duration": 28921}, "status": "passed", "steps": [{"name": "执行命令: happy new year", "time": {"start": 1756132407016, "stop": 1756132435642, "duration": 28626}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132435642, "stop": 1756132435937, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "da508ef5e65443a7", "name": "测试总结", "source": "da508ef5e65443a7.txt", "type": "text/plain", "size": 289}, {"uid": "9f4ee6f8cb8f7ca5", "name": "test_completed", "source": "9f4ee6f8cb8f7ca5.png", "type": "image/png", "size": 188407}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132435937, "stop": 1756132435938, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132435938, "stop": 1756132436161, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "4313fae4e536b0d1", "name": "测试总结", "source": "4313fae4e536b0d1.txt", "type": "text/plain", "size": 289}, {"uid": "31e04c4b89c82615", "name": "test_completed", "source": "31e04c4b89c82615.png", "type": "image/png", "size": 187815}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eef8517a25794b7e", "name": "stdout", "source": "eef8517a25794b7e.txt", "type": "text/plain", "size": 12704}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132436163, "stop": 1756132436163, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132436165, "stop": 1756132437555, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_happy_new_year"}, {"name": "subSuite", "value": "TestEllaHappyNewYear"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_happy_new_year"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "eb2a63d38a549c61.json", "parameterValues": []}