{"uid": "96105f32693c045", "name": "测试set app notifications返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_notifications.TestEllaSetAppNotifications#test_set_app_notifications", "historyId": "9458f45d3c37d9141658da9964a470f5", "time": {"start": 1756136029737, "stop": 1756136063730, "duration": 33993}, "description": "验证set app notifications指令返回预期的不支持响应", "descriptionHtml": "<p>验证set app notifications指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set app notifications', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Do Not Disturb | Do Not Disturb | Schedule | Allow Interruptions | Call Notifications | Favorite Contacts | App Notifications | 0 app(s) allowed | Allow Repeat Callers | Repeated incoming calls are defined as a second call within 15 min.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_app_notifications.TestEllaSetAppNotifications object at 0x0000029205654BD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208407010>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_app_notifications(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set app notifications', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Do Not Disturb | Do Not Disturb | Schedule | Allow Interruptions | Call Notifications | Favorite Contacts | App Notifications | 0 app(s) allowed | Allow Repeat Callers | Repeated incoming calls are defined as a second call within 15 min.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_app_notifications.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136016946, "stop": 1756136029735, "duration": 12789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136029735, "stop": 1756136029735, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set app notifications指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set app notifications', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Do Not Disturb | Do Not Disturb | Schedule | Allow Interruptions | Call Notifications | Favorite Contacts | App Notifications | 0 app(s) allowed | Allow Repeat Callers | Repeated incoming calls are defined as a second call within 15 min.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_app_notifications.TestEllaSetAppNotifications object at 0x0000029205654BD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208407010>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_app_notifications(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set app notifications', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Do Not Disturb | Do Not Disturb | Schedule | Allow Interruptions | Call Notifications | Favorite Contacts | App Notifications | 0 app(s) allowed | Allow Repeat Callers | Repeated incoming calls are defined as a second call within 15 min.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_app_notifications.py:33: AssertionError", "steps": [{"name": "执行命令: set app notifications", "time": {"start": 1756136029737, "stop": 1756136063725, "duration": 33988}, "status": "passed", "steps": [{"name": "执行命令: set app notifications", "time": {"start": 1756136029737, "stop": 1756136063441, "duration": 33704}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136063441, "stop": 1756136063725, "duration": 284}, "status": "passed", "steps": [], "attachments": [{"uid": "1b670a48bc0bf84d", "name": "测试总结", "source": "1b670a48bc0bf84d.txt", "type": "text/plain", "size": 554}, {"uid": "5cc5744e127b2598", "name": "test_completed", "source": "5cc5744e127b2598.png", "type": "image/png", "size": 179398}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136063725, "stop": 1756136063729, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set app notifications', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Do Not Disturb | Do Not Disturb | Schedule | Allow Interruptions | Call Notifications | Favorite Contacts | App Notifications | 0 app(s) allowed | Allow Repeat Callers | Repeated incoming calls are defined as a second call within 15 min.']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_app_notifications.py\", line 33, in test_set_app_notifications\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ac248210d5115315", "name": "stdout", "source": "ac248210d5115315.txt", "type": "text/plain", "size": 16135}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136063735, "stop": 1756136063968, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "790b19995a5a49a4", "name": "失败截图-TestEllaSetAppNotifications", "source": "790b19995a5a49a4.png", "type": "image/png", "size": 179492}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136063968, "stop": 1756136065500, "duration": 1532}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_notifications"}, {"name": "subSuite", "value": "TestEllaSetAppNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_notifications"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "96105f32693c045.json", "parameterValues": []}