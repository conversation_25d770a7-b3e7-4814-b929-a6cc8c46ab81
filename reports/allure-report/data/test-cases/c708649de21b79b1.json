{"uid": "c708649de21b79b1", "name": "测试what's your name", "fullName": "testcases.test_ella.unsupported_commands.test_what_s_your_name.TestEllaOpenPlayPoliticalNews#test_what_s_your_name", "historyId": "929b39cceeb7a01f602573951f5fef39", "time": {"start": 1756138988610, "stop": 1756139015834, "duration": 27224}, "description": "测试what's your name指令", "descriptionHtml": "<p>测试what's your name指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138975986, "stop": 1756138988606, "duration": 12620}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138988606, "stop": 1756138988606, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试what's your name指令", "status": "passed", "steps": [{"name": "执行命令: what's your name", "time": {"start": 1756138988610, "stop": 1756139015545, "duration": 26935}, "status": "passed", "steps": [{"name": "执行命令: what's your name", "time": {"start": 1756138988610, "stop": 1756139015299, "duration": 26689}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139015300, "stop": 1756139015544, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "526d0515e96530ce", "name": "测试总结", "source": "526d0515e96530ce.txt", "type": "text/plain", "size": 735}, {"uid": "7b54605964ba91dc", "name": "test_completed", "source": "7b54605964ba91dc.png", "type": "image/png", "size": 185772}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756139015545, "stop": 1756139015547, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756139015547, "stop": 1756139015833, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "555bc93dd5c10d09", "name": "测试总结", "source": "555bc93dd5c10d09.txt", "type": "text/plain", "size": 735}, {"uid": "ba37081ad6e2d00f", "name": "test_completed", "source": "ba37081ad6e2d00f.png", "type": "image/png", "size": 185772}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eff5dbdb11e3d094", "name": "stdout", "source": "eff5dbdb11e3d094.txt", "type": "text/plain", "size": 14897}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756139015835, "stop": 1756139015835, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756139015836, "stop": 1756139017266, "duration": 1430}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_s_your_name"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_s_your_name"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c708649de21b79b1.json", "parameterValues": []}