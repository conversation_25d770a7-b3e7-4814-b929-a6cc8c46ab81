{"uid": "ac0f850baf113c73", "name": "测试turn up alarm clock volume", "fullName": "testcases.test_ella.system_coupling.test_turn_up_alarm_clock_volume.TestEllaOpenClock#test_turn_up_alarm_clock_volume", "historyId": "0fa1017773031b1388876c73f0e0e653", "time": {"start": 1756128429045, "stop": 1756128455179, "duration": 26134}, "description": "测试turn up alarm clock volume指令", "descriptionHtml": "<p>测试turn up alarm clock volume指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128416245, "stop": 1756128429044, "duration": 12799}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128429044, "stop": 1756128429045, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn up alarm clock volume指令", "status": "passed", "steps": [{"name": "执行命令: turn up alarm clock volume", "time": {"start": 1756128429046, "stop": 1756128454971, "duration": 25925}, "status": "passed", "steps": [{"name": "执行命令: turn up alarm clock volume", "time": {"start": 1756128429046, "stop": 1756128454694, "duration": 25648}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128454694, "stop": 1756128454970, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "eb482e8e1ecd2585", "name": "测试总结", "source": "eb482e8e1ecd2585.txt", "type": "text/plain", "size": 309}, {"uid": "bae7f3e31fcab9fb", "name": "test_completed", "source": "bae7f3e31fcab9fb.png", "type": "image/png", "size": 183708}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128454971, "stop": 1756128454972, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证clock已打开", "time": {"start": 1756128454972, "stop": 1756128454972, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128454972, "stop": 1756128455179, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "40b153ba074ac8b", "name": "测试总结", "source": "40b153ba074ac8b.txt", "type": "text/plain", "size": 309}, {"uid": "5f16c8bb4d2e7918", "name": "test_completed", "source": "5f16c8bb4d2e7918.png", "type": "image/png", "size": 183708}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c7e23b70e952ba0", "name": "stdout", "source": "c7e23b70e952ba0.txt", "type": "text/plain", "size": 13725}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128455180, "stop": 1756128455180, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128455181, "stop": 1756128456539, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_alarm_clock_volume"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_alarm_clock_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ac0f850baf113c73.json", "parameterValues": []}