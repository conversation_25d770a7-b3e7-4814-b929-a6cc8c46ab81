{"uid": "665c57de6402e473", "name": "测试unset alarms能正常执行", "fullName": "testcases.test_ella.dialogue.test_unset_alarms.TestEllaHowIsWeatherToday#test_unset_alarms", "historyId": "b9fc05e613fdd4d145434e9cf6378c4b", "time": {"start": 1756122034719, "stop": 1756122087221, "duration": 52502}, "description": "unset alarms", "descriptionHtml": "<p>unset alarms</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122021344, "stop": 1756122034714, "duration": 13370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122034714, "stop": 1756122034714, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "unset alarms", "status": "passed", "steps": [{"name": "执行命令:  Set an alarm at 10 am tomorrow", "time": {"start": 1756122034720, "stop": 1756122060396, "duration": 25676}, "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "time": {"start": 1756122034720, "stop": 1756122060115, "duration": 25395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122060115, "stop": 1756122060395, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "f551b86d33f279fd", "name": "测试总结", "source": "f551b86d33f279fd.txt", "type": "text/plain", "size": 263}, {"uid": "e8e554fecac20ff7", "name": "test_completed", "source": "e8e554fecac20ff7.png", "type": "image/png", "size": 148290}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: unset alarms", "time": {"start": 1756122060396, "stop": 1756122086993, "duration": 26597}, "status": "passed", "steps": [{"name": "执行命令: unset alarms", "time": {"start": 1756122060396, "stop": 1756122086766, "duration": 26370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122086766, "stop": 1756122086992, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "6ee7142131b24dbb", "name": "测试总结", "source": "6ee7142131b24dbb.txt", "type": "text/plain", "size": 234}, {"uid": "cb6fe50322315634", "name": "test_completed", "source": "cb6fe50322315634.png", "type": "image/png", "size": 144148}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122086993, "stop": 1756122086998, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122086998, "stop": 1756122087219, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "fda58bcd5e89bc0e", "name": "测试总结", "source": "fda58bcd5e89bc0e.txt", "type": "text/plain", "size": 234}, {"uid": "aaeb2ca466681e40", "name": "test_completed", "source": "aaeb2ca466681e40.png", "type": "image/png", "size": 144095}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e5f501048f44ab2c", "name": "stdout", "source": "e5f501048f44ab2c.txt", "type": "text/plain", "size": 22074}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 8, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122087222, "stop": 1756122087222, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122087226, "stop": 1756122088601, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_unset_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_unset_alarms"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "665c57de6402e473.json", "parameterValues": []}