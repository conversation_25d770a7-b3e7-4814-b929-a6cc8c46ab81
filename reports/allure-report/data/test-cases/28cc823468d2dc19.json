{"uid": "28cc823468d2dc19", "name": "测试maximum volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_maximum_volume.TestEllaMaximumVolume#test_maximum_volume", "historyId": "548362627ce690e10e5f8ca35d247c62", "time": {"start": 1756124957946, "stop": 1756124984414, "duration": 26468}, "description": "maximum volume", "descriptionHtml": "<p>maximum volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124945147, "stop": 1756124957944, "duration": 12797}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124957944, "stop": 1756124957944, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "maximum volume", "status": "passed", "steps": [{"name": "执行命令: maximum volume", "time": {"start": 1756124957946, "stop": 1756124984129, "duration": 26183}, "status": "passed", "steps": [{"name": "执行命令: maximum volume", "time": {"start": 1756124957946, "stop": 1756124983836, "duration": 25890}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124983837, "stop": 1756124984129, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "a76707693e065860", "name": "测试总结", "source": "a76707693e065860.txt", "type": "text/plain", "size": 296}, {"uid": "1293951bde2a0205", "name": "test_completed", "source": "1293951bde2a0205.png", "type": "image/png", "size": 182270}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124984129, "stop": 1756124984134, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124984134, "stop": 1756124984134, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124984134, "stop": 1756124984412, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "46b3a79a29f09b0e", "name": "测试总结", "source": "46b3a79a29f09b0e.txt", "type": "text/plain", "size": 296}, {"uid": "e24464ff201a0b86", "name": "test_completed", "source": "e24464ff201a0b86.png", "type": "image/png", "size": 182270}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "feed160c3b84d6ad", "name": "stdout", "source": "feed160c3b84d6ad.txt", "type": "text/plain", "size": 13416}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124984416, "stop": 1756124984416, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124984418, "stop": 1756124985792, "duration": 1374}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_maximum_volume"}, {"name": "subSuite", "value": "TestEllaMaximumVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_maximum_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "28cc823468d2dc19.json", "parameterValues": []}