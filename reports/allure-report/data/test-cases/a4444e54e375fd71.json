{"uid": "a4444e54e375fd71", "name": "测试set off a firework能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_set_off_a_firework.TestEllaSetOffFirework#test_set_off_a_firework", "historyId": "e7de7a828ef1b59725204585ed7e1d64", "time": {"start": 1756136957767, "stop": 1756136987303, "duration": 29536}, "description": "set off a firework", "descriptionHtml": "<p>set off a firework</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136939772, "stop": 1756136957765, "duration": 17993}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136957765, "stop": 1756136957765, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "set off a firework", "status": "passed", "steps": [{"name": "执行命令: set off a firework", "time": {"start": 1756136957767, "stop": 1756136987040, "duration": 29273}, "status": "passed", "steps": [{"name": "执行命令: set off a firework", "time": {"start": 1756136957767, "stop": 1756136986751, "duration": 28984}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136986751, "stop": 1756136987040, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "ae8e8c6c82e1491a", "name": "测试总结", "source": "ae8e8c6c82e1491a.txt", "type": "text/plain", "size": 297}, {"uid": "b2c03ee3707376fa", "name": "test_completed", "source": "b2c03ee3707376fa.png", "type": "image/png", "size": 181760}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756136987040, "stop": 1756136987041, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136987041, "stop": 1756136987301, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "faf48df8354cc409", "name": "测试总结", "source": "faf48df8354cc409.txt", "type": "text/plain", "size": 297}, {"uid": "c6b6c63c99a337ec", "name": "test_completed", "source": "c6b6c63c99a337ec.png", "type": "image/png", "size": 181760}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c5075f74946cb753", "name": "stdout", "source": "c5075f74946cb753.txt", "type": "text/plain", "size": 12744}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136987305, "stop": 1756136987305, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136987308, "stop": 1756136988793, "duration": 1485}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_off_a_firework"}, {"name": "subSuite", "value": "TestEllaSetOffFirework"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_off_a_firework"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a4444e54e375fd71.json", "parameterValues": []}