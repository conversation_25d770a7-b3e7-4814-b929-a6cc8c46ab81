{"uid": "feb8838dadff6bff", "name": "测试take a photo能正常执行", "fullName": "testcases.test_ella.system_coupling.test_take_a_photo.TestEllaTakePhoto#test_take_a_photo", "historyId": "c076d6e18e779bfeb810e69b30339aa2", "time": {"start": 1756127009329, "stop": 1756127056272, "duration": 46943}, "description": "take a photo", "descriptionHtml": "<p>take a photo</p>\n", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_take_a_photo.TestEllaTakePhoto object at 0x0000029204BBAD10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292082DB4D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_photo(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False,verify_files=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n            assert final_status, f\" 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_take_a_photo.py:40: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126996867, "stop": 1756127009327, "duration": 12460}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127009327, "stop": 1756127009327, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take a photo", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_take_a_photo.TestEllaTakePhoto object at 0x0000029204BBAD10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292082DB4D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_photo(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False,verify_files=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n            assert final_status, f\" 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_take_a_photo.py:40: AssertionError", "steps": [{"name": "执行命令: take a photo", "time": {"start": 1756127009329, "stop": 1756127056266, "duration": 46937}, "status": "passed", "steps": [{"name": "执行命令: take a photo", "time": {"start": 1756127009329, "stop": 1756127056016, "duration": 46687}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127056017, "stop": 1756127056265, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "63b9da80b64a74ef", "name": "测试总结", "source": "63b9da80b64a74ef.txt", "type": "text/plain", "size": 359}, {"uid": "6457ae874a406133", "name": "test_completed", "source": "6457ae874a406133.png", "type": "image/png", "size": 169110}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127056266, "stop": 1756127056268, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756127056268, "stop": 1756127056268, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证文件存在", "time": {"start": 1756127056268, "stop": 1756127056268, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_take_a_photo.py\", line 40, in test_take_a_photo\n    assert files_status, f\"文件不存在！\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9ec25f44f287beef", "name": "stdout", "source": "9ec25f44f287beef.txt", "type": "text/plain", "size": 16207}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127056281, "stop": 1756127056545, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "46a412acc90d2cee", "name": "失败截图-TestEllaTakePhoto", "source": "46a412acc90d2cee.png", "type": "image/png", "size": 169297}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756127056549, "stop": 1756127057928, "duration": 1379}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "模块方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_photo"}, {"name": "subSuite", "value": "TestEllaTakePhoto"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_photo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "feb8838dadff6bff.json", "parameterValues": []}