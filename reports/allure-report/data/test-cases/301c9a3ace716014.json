{"uid": "301c9a3ace716014", "name": "测试a clear and pink crystal necklace in the water", "fullName": "testcases.test_ella.unsupported_commands.test_a_clear_and_pink_crystal_necklace_in_the_water.TestEllaOpenPlayPoliticalNews#test_a_clear_and_pink_crystal_necklace_in_the_water", "historyId": "0a76822399c9a8e342924e5ae6cce12c", "time": {"start": 1756129437314, "stop": 1756129465501, "duration": 28187}, "description": "测试a clear and pink crystal necklace in the water指令", "descriptionHtml": "<p>测试a clear and pink crystal necklace in the water指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129424510, "stop": 1756129437311, "duration": 12801}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129437312, "stop": 1756129437312, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试a clear and pink crystal necklace in the water指令", "status": "passed", "steps": [{"name": "执行命令: a clear and pink crystal necklace in the water", "time": {"start": 1756129437314, "stop": 1756129465235, "duration": 27921}, "status": "passed", "steps": [{"name": "执行命令: a clear and pink crystal necklace in the water", "time": {"start": 1756129437314, "stop": 1756129464964, "duration": 27650}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129464964, "stop": 1756129465235, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "6721cb735122fe6f", "name": "测试总结", "source": "6721cb735122fe6f.txt", "type": "text/plain", "size": 1475}, {"uid": "20cde325b95bf7d0", "name": "test_completed", "source": "20cde325b95bf7d0.png", "type": "image/png", "size": 240461}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129465235, "stop": 1756129465236, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129465236, "stop": 1756129465500, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "f2ea6c8aef062167", "name": "测试总结", "source": "f2ea6c8aef062167.txt", "type": "text/plain", "size": 1475}, {"uid": "f756bce2f4a42137", "name": "test_completed", "source": "f756bce2f4a42137.png", "type": "image/png", "size": 240764}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7404706a13c45e83", "name": "stdout", "source": "7404706a13c45e83.txt", "type": "text/plain", "size": 16821}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129465501, "stop": 1756129465501, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129465503, "stop": 1756129466957, "duration": 1454}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_clear_and_pink_crystal_necklace_in_the_water"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_clear_and_pink_crystal_necklace_in_the_water"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "301c9a3ace716014.json", "parameterValues": []}