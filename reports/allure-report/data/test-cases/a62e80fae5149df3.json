{"uid": "a62e80fae5149df3", "name": "测试the mobile phone is very hot", "fullName": "testcases.test_ella.unsupported_commands.test_the_mobile_phone_is_very_hot.TestEllaOpenPlayPoliticalNews#test_the_mobile_phone_is_very_hot", "historyId": "9fcc7f87aa3845b28893959bf17baa2b", "time": {"start": 1756138317433, "stop": 1756138346225, "duration": 28792}, "description": "测试the mobile phone is very hot指令", "descriptionHtml": "<p>测试the mobile phone is very hot指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138304475, "stop": 1756138317431, "duration": 12956}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138317432, "stop": 1756138317432, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试the mobile phone is very hot指令", "status": "passed", "steps": [{"name": "执行命令: the mobile phone is very hot", "time": {"start": 1756138317433, "stop": 1756138345957, "duration": 28524}, "status": "passed", "steps": [{"name": "执行命令: the mobile phone is very hot", "time": {"start": 1756138317433, "stop": 1756138345696, "duration": 28263}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138345696, "stop": 1756138345957, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "286cac8ba366f4a2", "name": "测试总结", "source": "286cac8ba366f4a2.txt", "type": "text/plain", "size": 2448}, {"uid": "95b8b9a4a659f2ad", "name": "test_completed", "source": "95b8b9a4a659f2ad.png", "type": "image/png", "size": 254561}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138345957, "stop": 1756138345973, "duration": 16}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138345973, "stop": 1756138346224, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "fe335ca8a104ee15", "name": "测试总结", "source": "fe335ca8a104ee15.txt", "type": "text/plain", "size": 2448}, {"uid": "cc00a8b4d1bbe331", "name": "test_completed", "source": "cc00a8b4d1bbe331.png", "type": "image/png", "size": 254709}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "101ce0eaf6e390b5", "name": "stdout", "source": "101ce0eaf6e390b5.txt", "type": "text/plain", "size": 19969}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138346225, "stop": 1756138346225, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138346227, "stop": 1756138347567, "duration": 1340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_the_mobile_phone_is_very_hot"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_the_mobile_phone_is_very_hot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a62e80fae5149df3.json", "parameterValues": []}