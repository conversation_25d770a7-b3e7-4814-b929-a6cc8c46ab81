{"uid": "71ad9c00f61d07fa", "name": "测试turn off flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_flashlight.TestEllaTurnOffFlashlight#test_turn_off_flashlight", "historyId": "c190fe929896ea57ed1e33f8bc5bf113", "time": {"start": 1756127456432, "stop": 1756127483731, "duration": 27299}, "description": "turn off flashlight", "descriptionHtml": "<p>turn off flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127443380, "stop": 1756127456431, "duration": 13051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127456431, "stop": 1756127456431, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn off flashlight", "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1756127456432, "stop": 1756127483495, "duration": 27063}, "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1756127456432, "stop": 1756127483212, "duration": 26780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127483212, "stop": 1756127483495, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "d82caad6cbe02424", "name": "测试总结", "source": "d82caad6cbe02424.txt", "type": "text/plain", "size": 310}, {"uid": "f14939691716dd12", "name": "test_completed", "source": "f14939691716dd12.png", "type": "image/png", "size": 158077}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127483495, "stop": 1756127483496, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127483496, "stop": 1756127483496, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127483496, "stop": 1756127483730, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "a13dda335e3ac2aa", "name": "测试总结", "source": "a13dda335e3ac2aa.txt", "type": "text/plain", "size": 310}, {"uid": "b058bdae1319170e", "name": "test_completed", "source": "b058bdae1319170e.png", "type": "image/png", "size": 158151}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f2c77a3f1acfb420", "name": "stdout", "source": "f2c77a3f1acfb420.txt", "type": "text/plain", "size": 13156}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127483732, "stop": 1756127483732, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127483734, "stop": 1756127485131, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnOffFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "71ad9c00f61d07fa.json", "parameterValues": []}