{"uid": "9058ec3529ad8927", "name": "测试turn on smart reminder能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_smart_reminder.TestEllaTurnSmartReminder#test_turn_on_smart_reminder", "historyId": "cf1bdc2b1d9b604939681e5b6ac6f506", "time": {"start": 1756128111777, "stop": 1756128138428, "duration": 26651}, "description": "turn on smart reminder", "descriptionHtml": "<p>turn on smart reminder</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128099201, "stop": 1756128111775, "duration": 12574}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128111775, "stop": 1756128111775, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on smart reminder", "status": "passed", "steps": [{"name": "执行命令: turn on smart reminder", "time": {"start": 1756128111779, "stop": 1756128138217, "duration": 26438}, "status": "passed", "steps": [{"name": "执行命令: turn on smart reminder", "time": {"start": 1756128111779, "stop": 1756128137964, "duration": 26185}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128137965, "stop": 1756128138217, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "ccf8eb20b0c19b56", "name": "测试总结", "source": "ccf8eb20b0c19b56.txt", "type": "text/plain", "size": 241}, {"uid": "33fea38df8667ac4", "name": "test_completed", "source": "33fea38df8667ac4.png", "type": "image/png", "size": 159007}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128138217, "stop": 1756128138218, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128138218, "stop": 1756128138218, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128138218, "stop": 1756128138428, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "88e74f71e1121c35", "name": "测试总结", "source": "88e74f71e1121c35.txt", "type": "text/plain", "size": 241}, {"uid": "34539d7c4487f95c", "name": "test_completed", "source": "34539d7c4487f95c.png", "type": "image/png", "size": 159007}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d688df8b7210c388", "name": "stdout", "source": "d688df8b7210c388.txt", "type": "text/plain", "size": 12984}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128138429, "stop": 1756128138429, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128138430, "stop": 1756128139831, "duration": 1401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_smart_reminder"}, {"name": "subSuite", "value": "TestEllaTurnSmartReminder"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_smart_reminder"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9058ec3529ad8927.json", "parameterValues": []}