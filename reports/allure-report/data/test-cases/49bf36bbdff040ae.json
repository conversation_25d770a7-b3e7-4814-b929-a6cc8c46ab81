{"uid": "49bf36bbdff040ae", "name": "测试A cute little boy is skiing 能正常执行", "fullName": "testcases.test_ella.self_function.test_a_cute_little_boy_is_skiing.TestEllaCuteLittleBoyIsSkiing#test_a_cute_little_boy_is_skiing", "historyId": "da0740a44317121ce1879d022e95ae23", "time": {"start": 1756122770886, "stop": 1756122854103, "duration": 83217}, "description": "A cute little boy is skiing ", "descriptionHtml": "<p>A cute little boy is skiing</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122758037, "stop": 1756122770885, "duration": 12848}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122770885, "stop": 1756122770885, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "A cute little boy is skiing ", "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing ", "time": {"start": 1756122770886, "stop": 1756122853638, "duration": 82752}, "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing ", "time": {"start": 1756122770886, "stop": 1756122853190, "duration": 82304}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122853190, "stop": 1756122853638, "duration": 448}, "status": "passed", "steps": [], "attachments": [{"uid": "fbb8625cc0eaa613", "name": "测试总结", "source": "fbb8625cc0eaa613.txt", "type": "text/plain", "size": 368}, {"uid": "f6751da58556e60d", "name": "test_completed", "source": "f6751da58556e60d.png", "type": "image/png", "size": 688685}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122853639, "stop": 1756122853640, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122853640, "stop": 1756122854103, "duration": 463}, "status": "passed", "steps": [], "attachments": [{"uid": "69be2cefb64ef034", "name": "测试总结", "source": "69be2cefb64ef034.txt", "type": "text/plain", "size": 311}, {"uid": "2e5de648b7b51e9a", "name": "test_completed", "source": "2e5de648b7b51e9a.png", "type": "image/png", "size": 689703}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "abe5b45d558f65b3", "name": "stdout", "source": "abe5b45d558f65b3.txt", "type": "text/plain", "size": 16319}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122854104, "stop": 1756122854104, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122854106, "stop": 1756122855517, "duration": 1411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_a_cute_little_boy_is_skiing"}, {"name": "subSuite", "value": "TestEllaCuteLittleBoyIsSkiing"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_a_cute_little_boy_is_skiing"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "49bf36bbdff040ae.json", "parameterValues": []}