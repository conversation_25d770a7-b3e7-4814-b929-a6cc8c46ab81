{"uid": "dea3e785ff0e6b5", "name": "测试hi能正常执行", "fullName": "testcases.test_ella.dialogue.test_hi.TestEllaHi#test_hi", "historyId": "18415b75388fbfdac9a7e4232373c000", "time": {"start": 1756119896069, "stop": 1756119923590, "duration": 27521}, "description": "hi", "descriptionHtml": "<p>hi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119883033, "stop": 1756119896068, "duration": 13035}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119896068, "stop": 1756119896068, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "hi", "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1756119896069, "stop": 1756119923369, "duration": 27300}, "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1756119896069, "stop": 1756119923078, "duration": 27009}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119923078, "stop": 1756119923368, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "9c840372fe23f77a", "name": "测试总结", "source": "9c840372fe23f77a.txt", "type": "text/plain", "size": 718}, {"uid": "445711a9e75b1c5f", "name": "test_completed", "source": "445711a9e75b1c5f.png", "type": "image/png", "size": 181350}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119923369, "stop": 1756119923370, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119923370, "stop": 1756119923589, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "ffe64f3eba5b6368", "name": "测试总结", "source": "ffe64f3eba5b6368.txt", "type": "text/plain", "size": 718}, {"uid": "11333b24f19b34e4", "name": "test_completed", "source": "11333b24f19b34e4.png", "type": "image/png", "size": 181290}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1c73021f5f24ff8", "name": "stdout", "source": "1c73021f5f24ff8.txt", "type": "text/plain", "size": 14710}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119923591, "stop": 1756119923591, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119923592, "stop": 1756119925031, "duration": 1439}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hi"}, {"name": "subSuite", "value": "TestEllaHi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dea3e785ff0e6b5.json", "parameterValues": []}