{"uid": "3d93186b4be8c87f", "name": "测试there are many yellow sunflowers on the ground", "fullName": "testcases.test_ella.unsupported_commands.test_there_are_many_yellow_sunflowers_on_the_ground.TestEllaOpenPlayPoliticalNews#test_there_are_many_yellow_sunflowers_on_the_ground", "historyId": "74da34fd3c558df1234d7c0e937641b3", "time": {"start": 1756138359738, "stop": 1756138387382, "duration": 27644}, "description": "测试there are many yellow sunflowers on the ground指令", "descriptionHtml": "<p>测试there are many yellow sunflowers on the ground指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138347573, "stop": 1756138359737, "duration": 12164}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138359737, "stop": 1756138359737, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试there are many yellow sunflowers on the ground指令", "status": "passed", "steps": [{"name": "执行命令: there are many yellow sunflowers on the ground", "time": {"start": 1756138359738, "stop": 1756138387143, "duration": 27405}, "status": "passed", "steps": [{"name": "执行命令: there are many yellow sunflowers on the ground", "time": {"start": 1756138359738, "stop": 1756138386870, "duration": 27132}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138386870, "stop": 1756138387142, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "543c9d11058ef632", "name": "测试总结", "source": "543c9d11058ef632.txt", "type": "text/plain", "size": 924}, {"uid": "2d028329a20d6179", "name": "test_completed", "source": "2d028329a20d6179.png", "type": "image/png", "size": 196884}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138387143, "stop": 1756138387144, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138387144, "stop": 1756138387381, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "19cf4c535f2cd2ce", "name": "测试总结", "source": "19cf4c535f2cd2ce.txt", "type": "text/plain", "size": 924}, {"uid": "8137588844a1ecb6", "name": "test_completed", "source": "8137588844a1ecb6.png", "type": "image/png", "size": 196841}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e8a254674fe5f049", "name": "stdout", "source": "e8a254674fe5f049.txt", "type": "text/plain", "size": 15679}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138387384, "stop": 1756138387384, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138387386, "stop": 1756138388739, "duration": 1353}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_are_many_yellow_sunflowers_on_the_ground"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_are_many_yellow_sunflowers_on_the_ground"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3d93186b4be8c87f.json", "parameterValues": []}