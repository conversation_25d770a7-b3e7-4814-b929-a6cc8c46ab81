{"uid": "a31a48a8d6546789", "name": "测试set screen to minimum brightness返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness#test_set_screen_to_minimum_brightness", "historyId": "544fc8b021d2dbcaf295cd05b798f816", "time": {"start": 1756137372414, "stop": 1756137410494, "duration": 38080}, "description": "验证set screen to minimum brightness指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen to minimum brightness指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen to minimum brightness', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded Apps | Ella | Hi Translate | Select to Speak | Off / Hear selected text | Smart Translate | TalkBack | Off / Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Off / Zoom in on the screen | Interaction Controls | Accessibility Menu']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness object at 0x00000292056440D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209682610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_screen_to_minimum_brightness(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen to minimum brightness', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded Apps | Ella | Hi Translate | Select to Speak | Off / Hear selected text | Smart Translate | TalkBack | Off / Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Off / Zoom in on the screen | Interaction Controls | Accessibility Menu']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_screen_to_minimum_brightness.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137359043, "stop": 1756137372411, "duration": 13368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137372411, "stop": 1756137372411, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set screen to minimum brightness指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen to minimum brightness', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded Apps | Ella | Hi Translate | Select to Speak | Off / Hear selected text | Smart Translate | TalkBack | Off / Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Off / Zoom in on the screen | Interaction Controls | Accessibility Menu']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness object at 0x00000292056440D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209682610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_screen_to_minimum_brightness(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen to minimum brightness', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded Apps | Ella | Hi Translate | Select to Speak | Off / Hear selected text | Smart Translate | TalkBack | Off / Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Off / Zoom in on the screen | Interaction Controls | Accessibility Menu']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_screen_to_minimum_brightness.py:33: AssertionError", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1756137372414, "stop": 1756137410490, "duration": 38076}, "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1756137372414, "stop": 1756137410236, "duration": 37822}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137410236, "stop": 1756137410489, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "ab18b15f40325bc9", "name": "测试总结", "source": "ab18b15f40325bc9.txt", "type": "text/plain", "size": 679}, {"uid": "bfa9537da187b012", "name": "test_completed", "source": "bfa9537da187b012.png", "type": "image/png", "size": 167314}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137410490, "stop": 1756137410492, "duration": 2}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen to minimum brightness', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded Apps | Ella | Hi Translate | Select to Speak | Off / Hear selected text | Smart Translate | TalkBack | Off / Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Off / Zoom in on the screen | Interaction Controls | Accessibility Menu']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_screen_to_minimum_brightness.py\", line 33, in test_set_screen_to_minimum_brightness\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f02cdbf58e552be1", "name": "stdout", "source": "f02cdbf58e552be1.txt", "type": "text/plain", "size": 17348}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137410497, "stop": 1756137410720, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "e21fb21970985a59", "name": "失败截图-TestEllaSetScreenMinimumBrightness", "source": "e21fb21970985a59.png", "type": "image/png", "size": 167353}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137410722, "stop": 1756137412260, "duration": 1538}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_to_minimum_brightness"}, {"name": "subSuite", "value": "TestEllaSetScreenMinimumBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a31a48a8d6546789.json", "parameterValues": []}