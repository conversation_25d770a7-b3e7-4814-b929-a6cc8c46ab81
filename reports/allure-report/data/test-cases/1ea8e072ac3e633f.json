{"uid": "1ea8e072ac3e633f", "name": "测试open settings", "fullName": "testcases.test_ella.unsupported_commands.test_open_settings.TestEllaOpenPlayPoliticalNews#test_open_settings", "historyId": "af89bd1d18cd6a175678a8fe1f43ee33", "time": {"start": 1756134748105, "stop": 1756134785688, "duration": 37583}, "description": "测试open settings指令", "descriptionHtml": "<p>测试open settings指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134735060, "stop": 1756134748103, "duration": 13043}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134748104, "stop": 1756134748104, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open settings指令", "status": "passed", "steps": [{"name": "执行命令: open settings", "time": {"start": 1756134748105, "stop": 1756134785439, "duration": 37334}, "status": "passed", "steps": [{"name": "执行命令: open settings", "time": {"start": 1756134748105, "stop": 1756134785175, "duration": 37070}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134785175, "stop": 1756134785437, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "7bda826d966038d6", "name": "测试总结", "source": "7bda826d966038d6.txt", "type": "text/plain", "size": 629}, {"uid": "77450360cbb3fbe4", "name": "test_completed", "source": "77450360cbb3fbe4.png", "type": "image/png", "size": 174235}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134785439, "stop": 1756134785441, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证settings已打开", "time": {"start": 1756134785441, "stop": 1756134785441, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134785441, "stop": 1756134785687, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "198679401d660a67", "name": "测试总结", "source": "198679401d660a67.txt", "type": "text/plain", "size": 629}, {"uid": "5057ef35a3f1c095", "name": "test_completed", "source": "5057ef35a3f1c095.png", "type": "image/png", "size": 174174}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fb6612b9f0394a0b", "name": "stdout", "source": "fb6612b9f0394a0b.txt", "type": "text/plain", "size": 15839}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134785689, "stop": 1756134785689, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134785690, "stop": 1756134787155, "duration": 1465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1ea8e072ac3e633f.json", "parameterValues": []}