{"uid": "54bb5fd04864f1c6", "name": "测试Change the style of this image to 3D cartoon能正常执行", "fullName": "testcases.test_ella.self_function.test_change_the_style_of_this_image_to_d_cartoon.TestEllaChangeStyleThisImageDCartoon#test_change_the_style_of_this_image_to_d_cartoon", "historyId": "caccad19499f4bc8494953ac84d8d23c", "time": {"start": 1756122868233, "stop": 1756122986102, "duration": 117869}, "description": "Change the style of this image to 3D cartoon", "descriptionHtml": "<p>Change the style of this image to 3D cartoon</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122855525, "stop": 1756122868232, "duration": 12707}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122868232, "stop": 1756122868232, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Change the style of this image to 3D cartoon", "status": "passed", "steps": [{"name": "执行命令: Change the style of this image to 3D cartoon", "time": {"start": 1756122868233, "stop": 1756122985860, "duration": 117627}, "status": "passed", "steps": [{"name": "执行命令: Change the style of this image to 3D cartoon", "time": {"start": 1756122868233, "stop": 1756122985591, "duration": 117358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122985591, "stop": 1756122985859, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "4039630ef9ba6ee7", "name": "测试总结", "source": "4039630ef9ba6ee7.txt", "type": "text/plain", "size": 479}, {"uid": "d30cee308fff49ba", "name": "test_completed", "source": "d30cee308fff49ba.png", "type": "image/png", "size": 208508}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122985860, "stop": 1756122985863, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122985863, "stop": 1756122986101, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "4887ab6bb76637eb", "name": "测试总结", "source": "4887ab6bb76637eb.txt", "type": "text/plain", "size": 433}, {"uid": "92b58009ac2193cd", "name": "test_completed", "source": "92b58009ac2193cd.png", "type": "image/png", "size": 208508}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f220968160d5f542", "name": "stdout", "source": "f220968160d5f542.txt", "type": "text/plain", "size": 18215}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122986103, "stop": 1756122986103, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122986105, "stop": 1756122987446, "duration": 1341}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_change_the_style_of_this_image_to_d_cartoon"}, {"name": "subSuite", "value": "TestEllaChangeStyleThisImageDCartoon"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_change_the_style_of_this_image_to_d_cartoon"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "54bb5fd04864f1c6.json", "parameterValues": []}