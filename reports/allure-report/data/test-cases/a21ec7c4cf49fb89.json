{"uid": "a21ec7c4cf49fb89", "name": "测试disable hide notifications返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_hide_notifications.TestEllaDisableHideNotifications#test_disable_hide_notifications", "historyId": "3eec462c8da39eaf95742ed9ab45b7c7", "time": {"start": 1756130952377, "stop": 1756130978257, "duration": 25880}, "description": "验证disable hide notifications指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable hide notifications指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130939355, "stop": 1756130952375, "duration": 13020}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130952375, "stop": 1756130952375, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable hide notifications指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable hide notifications", "time": {"start": 1756130952377, "stop": 1756130978025, "duration": 25648}, "status": "passed", "steps": [{"name": "执行命令: disable hide notifications", "time": {"start": 1756130952377, "stop": 1756130977765, "duration": 25388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130977765, "stop": 1756130978025, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "74cc493ea1986767", "name": "测试总结", "source": "74cc493ea1986767.txt", "type": "text/plain", "size": 353}, {"uid": "fa3d8806c883f185", "name": "test_completed", "source": "fa3d8806c883f185.png", "type": "image/png", "size": 187302}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130978025, "stop": 1756130978026, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130978027, "stop": 1756130978256, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "8c879e554becf8a9", "name": "测试总结", "source": "8c879e554becf8a9.txt", "type": "text/plain", "size": 353}, {"uid": "31a98b76302807bc", "name": "test_completed", "source": "31a98b76302807bc.png", "type": "image/png", "size": 187302}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "42a9522fc6a2ea72", "name": "stdout", "source": "42a9522fc6a2ea72.txt", "type": "text/plain", "size": 12698}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130978259, "stop": 1756130978259, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130978260, "stop": 1756130979615, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_hide_notifications"}, {"name": "subSuite", "value": "TestEllaDisableHideNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_hide_notifications"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a21ec7c4cf49fb89.json", "parameterValues": []}