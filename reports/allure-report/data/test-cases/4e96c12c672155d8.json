{"uid": "4e96c12c672155d8", "name": "测试Set an alarm at 10 am tomorrow", "fullName": "testcases.test_ella.system_coupling.test_set_an_alarm_at_am_tomorrow.TestEllaOpenSetAnAlarmAmTomorrow#test_set_an_alarm_at_am_tomorrow", "historyId": "********************************", "time": {"start": 1756125732294, "stop": 1756125812899, "duration": 80605}, "description": "测试Set an alarm at 10 am tomorrow指令", "descriptionHtml": "<p>测试Set an alarm at 10 am tomorrow指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125719417, "stop": 1756125732291, "duration": 12874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125732291, "stop": 1756125732291, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Set an alarm at 10 am tomorrow指令", "status": "passed", "steps": [{"name": "执行命令:  delete all the alarms", "time": {"start": 1756125732294, "stop": 1756125758219, "duration": 25925}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756125732294, "stop": 1756125757949, "duration": 25655}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125757949, "stop": 1756125758218, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "eedf19b6630cae4a", "name": "测试总结", "source": "eedf19b6630cae4a.txt", "type": "text/plain", "size": 303}, {"uid": "5a4eab1f8a706dcd", "name": "test_completed", "source": "5a4eab1f8a706dcd.png", "type": "image/png", "size": 192299}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: Set an alarm at 10 am tomorrow", "time": {"start": 1756125758219, "stop": 1756125785613, "duration": 27394}, "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "time": {"start": 1756125758219, "stop": 1756125785351, "duration": 27132}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125785351, "stop": 1756125785613, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "ed0f01c60a3be7c8", "name": "测试总结", "source": "ed0f01c60a3be7c8.txt", "type": "text/plain", "size": 254}, {"uid": "8f9cae8eb5e59b0d", "name": "test_completed", "source": "8f9cae8eb5e59b0d.png", "type": "image/png", "size": 155179}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756125785613, "stop": 1756125812675, "duration": 27062}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756125785613, "stop": 1756125812394, "duration": 26781}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125812394, "stop": 1756125812675, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "d4949ee927f69d58", "name": "测试总结", "source": "d4949ee927f69d58.txt", "type": "text/plain", "size": 224}, {"uid": "a7101015ba1712a3", "name": "test_completed", "source": "a7101015ba1712a3.png", "type": "image/png", "size": 148478}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125812675, "stop": 1756125812677, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125812677, "stop": 1756125812898, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "7548d9f7c025d55b", "name": "测试总结", "source": "7548d9f7c025d55b.txt", "type": "text/plain", "size": 236}, {"uid": "9fa3538ad76fca66", "name": "test_completed", "source": "9fa3538ad76fca66.png", "type": "image/png", "size": 148622}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6174d688482b24dd", "name": "stdout", "source": "6174d688482b24dd.txt", "type": "text/plain", "size": 31826}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 11, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125812899, "stop": 1756125812899, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125812900, "stop": 1756125814289, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_am_tomorrow"}, {"name": "subSuite", "value": "TestEllaOpenSetAnAlarmAmTomorrow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_an_alarm_at_am_tomorrow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4e96c12c672155d8.json", "parameterValues": []}