{"uid": "46b26fdbf8bf845d", "name": "测试help me take a screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot.TestEllaHelpMeTakeScreenshot#test_help_me_take_a_screenshot", "historyId": "459c099a876d1129ddcb7cb28663b756", "time": {"start": 1756124503135, "stop": 1756124532146, "duration": 29011}, "description": "help me take a screenshot", "descriptionHtml": "<p>help me take a screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124490677, "stop": 1756124503132, "duration": 12455}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124503132, "stop": 1756124503132, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "help me take a screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1756124503135, "stop": 1756124531881, "duration": 28746}, "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1756124503135, "stop": 1756124531599, "duration": 28464}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124531599, "stop": 1756124531880, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "b7b7af5f52a41869", "name": "测试总结", "source": "b7b7af5f52a41869.txt", "type": "text/plain", "size": 585}, {"uid": "774e9d573a2e1883", "name": "test_completed", "source": "774e9d573a2e1883.png", "type": "image/png", "size": 163645}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证文件存在", "time": {"start": 1756124531881, "stop": 1756124531881, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124531881, "stop": 1756124532144, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "5f94906a86e6a853", "name": "测试总结", "source": "5f94906a86e6a853.txt", "type": "text/plain", "size": 585}, {"uid": "4e11198def653eab", "name": "test_completed", "source": "4e11198def653eab.png", "type": "image/png", "size": 163651}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d7612cdbe5951a99", "name": "stdout", "source": "d7612cdbe5951a99.txt", "type": "text/plain", "size": 14876}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124532147, "stop": 1756124532147, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124532150, "stop": 1756124533551, "duration": 1401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "46b26fdbf8bf845d.json", "parameterValues": []}