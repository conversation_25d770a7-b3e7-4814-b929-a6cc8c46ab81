{"uid": "4f69cc95fb2d3d28", "name": "测试Dial the number on the screen", "fullName": "testcases.test_ella.unsupported_commands.test_dial_the_number_on_the_screen.TestEllaOpenPlayPoliticalNews#test_dial_the_number_on_the_screen", "historyId": "44c9275711c93730c8d2cb2a7374b3cd", "time": {"start": 1756130686264, "stop": 1756130712331, "duration": 26067}, "description": "测试Dial the number on the screen指令", "descriptionHtml": "<p>测试Dial the number on the screen指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130673523, "stop": 1756130686253, "duration": 12730}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130686254, "stop": 1756130686254, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Dial the number on the screen指令", "status": "passed", "steps": [{"name": "执行命令: Dial the number on the screen", "time": {"start": 1756130686264, "stop": 1756130712091, "duration": 25827}, "status": "passed", "steps": [{"name": "执行命令: Dial the number on the screen", "time": {"start": 1756130686264, "stop": 1756130711837, "duration": 25573}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130711837, "stop": 1756130712090, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "6d9ddfc667a18cc4", "name": "测试总结", "source": "6d9ddfc667a18cc4.txt", "type": "text/plain", "size": 316}, {"uid": "c9bfba8772ecb16c", "name": "test_completed", "source": "c9bfba8772ecb16c.png", "type": "image/png", "size": 179655}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130712091, "stop": 1756130712092, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130712092, "stop": 1756130712331, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "daea0fb8dbdf41bc", "name": "测试总结", "source": "daea0fb8dbdf41bc.txt", "type": "text/plain", "size": 316}, {"uid": "c79e211ea6736eb5", "name": "test_completed", "source": "c79e211ea6736eb5.png", "type": "image/png", "size": 179655}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ea4e61cada4db5c7", "name": "stdout", "source": "ea4e61cada4db5c7.txt", "type": "text/plain", "size": 12667}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130712332, "stop": 1756130712332, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130712333, "stop": 1756130713735, "duration": 1402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_dial_the_number_on_the_screen"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_dial_the_number_on_the_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4f69cc95fb2d3d28.json", "parameterValues": []}