{"uid": "740ec85ba630dbe7", "name": "测试install whatsapp", "fullName": "testcases.test_ella.unsupported_commands.test_install_whatsapp.TestEllaOpenPlayPoliticalNews#test_install_whatsapp", "historyId": "434b905bf8be3ce2ee79606468e155db", "time": {"start": 1756133396057, "stop": 1756133427475, "duration": 31418}, "description": "测试install whatsapp指令", "descriptionHtml": "<p>测试install whatsapp指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133383135, "stop": 1756133396056, "duration": 12921}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133396056, "stop": 1756133396056, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试install whatsapp指令", "status": "passed", "steps": [{"name": "执行命令: install whatsapp", "time": {"start": 1756133396057, "stop": 1756133427223, "duration": 31166}, "status": "passed", "steps": [{"name": "执行命令: install whatsapp", "time": {"start": 1756133396057, "stop": 1756133426910, "duration": 30853}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133426910, "stop": 1756133427223, "duration": 313}, "status": "passed", "steps": [], "attachments": [{"uid": "5624a27173ab4b5", "name": "测试总结", "source": "5624a27173ab4b5.txt", "type": "text/plain", "size": 368}, {"uid": "7db8e20c19f646b0", "name": "test_completed", "source": "7db8e20c19f646b0.png", "type": "image/png", "size": 185482}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证google_playstore已打开", "time": {"start": 1756133427223, "stop": 1756133427223, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133427223, "stop": 1756133427475, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "a5f49ace882099a4", "name": "测试总结", "source": "a5f49ace882099a4.txt", "type": "text/plain", "size": 368}, {"uid": "1eeb3695fb1d7b5", "name": "test_completed", "source": "1eeb3695fb1d7b5.png", "type": "image/png", "size": 185774}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6e890d2285b77cf", "name": "stdout", "source": "6e890d2285b77cf.txt", "type": "text/plain", "size": 14691}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133427476, "stop": 1756133427476, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133427477, "stop": 1756133428908, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_install_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_install_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "740ec85ba630dbe7.json", "parameterValues": []}