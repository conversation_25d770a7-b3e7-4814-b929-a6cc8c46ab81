{"uid": "b336e572798e053c", "name": "测试open facebook能正常执行", "fullName": "testcases.test_ella.third_coupling.test_open_facebook.TestEllaCommandConcise#test_open_facebook", "historyId": "4933b925ec694ecfcd17b3423ac28184", "time": {"start": 1756129088223, "stop": 1756129122249, "duration": 34026}, "description": "open facebook", "descriptionHtml": "<p>open facebook</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129075515, "stop": 1756129088222, "duration": 12707}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129088223, "stop": 1756129088223, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "open facebook", "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1756129088223, "stop": 1756129122004, "duration": 33781}, "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1756129088223, "stop": 1756129121743, "duration": 33520}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129121743, "stop": 1756129122003, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "921bc0346673fd0f", "name": "测试总结", "source": "921bc0346673fd0f.txt", "type": "text/plain", "size": 509}, {"uid": "30000c460f1702c1", "name": "test_completed", "source": "30000c460f1702c1.png", "type": "image/png", "size": 180651}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756129122004, "stop": 1756129122005, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129122006, "stop": 1756129122247, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "c9cb2c7cb1db776a", "name": "测试总结", "source": "c9cb2c7cb1db776a.txt", "type": "text/plain", "size": 509}, {"uid": "46d164aefcd18209", "name": "test_completed", "source": "46d164aefcd18209.png", "type": "image/png", "size": 180651}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fbce30a1356d8eb6", "name": "stdout", "source": "fbce30a1356d8eb6.txt", "type": "text/plain", "size": 15617}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129122250, "stop": 1756129122250, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129122256, "stop": 1756129123663, "duration": 1407}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_open_facebook"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_open_facebook"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b336e572798e053c.json", "parameterValues": []}