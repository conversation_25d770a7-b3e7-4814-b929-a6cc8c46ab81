{"uid": "5f0dba6694b658a1", "name": "测试adjustment the brightness to minimun能正常执行", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_minimun.TestEllaAdjustmentBrightnessMinimun#test_adjustment_the_brightness_to_minimun", "historyId": "1a94a631f074dc4c0ba2bd39c9518123", "time": {"start": 1756123750406, "stop": 1756123777439, "duration": 27033}, "description": "adjustment the brightness to minimun", "descriptionHtml": "<p>adjustment the brightness to minimun</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123737842, "stop": 1756123750405, "duration": 12563}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123750405, "stop": 1756123750405, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "adjustment the brightness to minimun", "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to minimun", "time": {"start": 1756123750406, "stop": 1756123777194, "duration": 26788}, "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to minimun", "time": {"start": 1756123750406, "stop": 1756123776947, "duration": 26541}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123776948, "stop": 1756123777193, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "d3f895031e7c1db", "name": "测试总结", "source": "d3f895031e7c1db.txt", "type": "text/plain", "size": 242}, {"uid": "3cd836842f55cc48", "name": "test_completed", "source": "3cd836842f55cc48.png", "type": "image/png", "size": 156636}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123777195, "stop": 1756123777199, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756123777199, "stop": 1756123777199, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123777199, "stop": 1756123777438, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "aaf49c9f45a40ad8", "name": "测试总结", "source": "aaf49c9f45a40ad8.txt", "type": "text/plain", "size": 242}, {"uid": "fee20e2d51c189c5", "name": "test_completed", "source": "fee20e2d51c189c5.png", "type": "image/png", "size": 156636}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5246e5deaf1726e9", "name": "stdout", "source": "5246e5deaf1726e9.txt", "type": "text/plain", "size": 13290}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123777440, "stop": 1756123777440, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123777444, "stop": 1756123778837, "duration": 1393}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to_minimun"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightnessMinimun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_minimun"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5f0dba6694b658a1.json", "parameterValues": []}