{"uid": "145067311da6cd4d", "name": "测试change (female/tone name) voice能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_change_female_tone_name_voice.TestEllaChangeFemaleToneNameVoice#test_change_female_tone_name_voice", "historyId": "d8a01bf3d9b9092622318d8d22f17d9e", "time": {"start": 1756129928393, "stop": 1756129955399, "duration": 27006}, "description": "change (female/tone name) voice", "descriptionHtml": "<p>change (female/tone name) voice</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129915628, "stop": 1756129928392, "duration": 12764}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129928392, "stop": 1756129928392, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "change (female/tone name) voice", "status": "passed", "steps": [{"name": "执行命令: change (female/tone name) voice", "time": {"start": 1756129928393, "stop": 1756129955168, "duration": 26775}, "status": "passed", "steps": [{"name": "执行命令: change (female/tone name) voice", "time": {"start": 1756129928393, "stop": 1756129954906, "duration": 26513}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129954907, "stop": 1756129955168, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "e3d957e1964521c7", "name": "测试总结", "source": "e3d957e1964521c7.txt", "type": "text/plain", "size": 272}, {"uid": "fd4d62eaffea506d", "name": "test_completed", "source": "fd4d62eaffea506d.png", "type": "image/png", "size": 192030}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129955168, "stop": 1756129955170, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129955170, "stop": 1756129955399, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "b95e475163b489c", "name": "测试总结", "source": "b95e475163b489c.txt", "type": "text/plain", "size": 272}, {"uid": "dbb142aece8b6b0", "name": "test_completed", "source": "dbb142aece8b6b0.png", "type": "image/png", "size": 192030}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "11dbe8cf59038c46", "name": "stdout", "source": "11dbe8cf59038c46.txt", "type": "text/plain", "size": 12521}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129955400, "stop": 1756129955400, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129955402, "stop": 1756129956777, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_change_female_tone_name_voice"}, {"name": "subSuite", "value": "TestEllaChangeFemaleToneNameVoice"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_change_female_tone_name_voice"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "145067311da6cd4d.json", "parameterValues": []}