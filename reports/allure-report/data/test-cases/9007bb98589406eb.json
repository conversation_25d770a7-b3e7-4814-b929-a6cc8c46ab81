{"uid": "9007bb98589406eb", "name": "测试close power saving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode.TestEllaClosePowerSavingMode#test_close_power_saving_mode", "historyId": "1da800483d0bd7f8dbe657a8d5c37f76", "time": {"start": 1756130604446, "stop": 1756130630649, "duration": 26203}, "description": "验证close power saving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证close power saving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130591454, "stop": 1756130604443, "duration": 12989}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130604443, "stop": 1756130604444, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证close power saving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: close power saving mode", "time": {"start": 1756130604446, "stop": 1756130630361, "duration": 25915}, "status": "passed", "steps": [{"name": "执行命令: close power saving mode", "time": {"start": 1756130604446, "stop": 1756130630092, "duration": 25646}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130630092, "stop": 1756130630361, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "67123845de1f1de4", "name": "测试总结", "source": "67123845de1f1de4.txt", "type": "text/plain", "size": 352}, {"uid": "f95ec3027f0a833d", "name": "test_completed", "source": "f95ec3027f0a833d.png", "type": "image/png", "size": 192144}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130630362, "stop": 1756130630363, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130630363, "stop": 1756130630648, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "7c606a7c98032000", "name": "测试总结", "source": "7c606a7c98032000.txt", "type": "text/plain", "size": 352}, {"uid": "7106889c1bac158b", "name": "test_completed", "source": "7106889c1bac158b.png", "type": "image/png", "size": 192144}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "41258ec83f3b640b", "name": "stdout", "source": "41258ec83f3b640b.txt", "type": "text/plain", "size": 13153}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130630650, "stop": 1756130630650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130630652, "stop": 1756130632087, "duration": 1435}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaClosePowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9007bb98589406eb.json", "parameterValues": []}