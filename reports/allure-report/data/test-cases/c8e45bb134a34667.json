{"uid": "c8e45bb134a34667", "name": "测试close folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_folax.TestEllaCloseFolax#test_close_folax", "historyId": "8b2d3084bb429ea5def5db416bbf10a7", "time": {"start": 1756117259894, "stop": 1756117300069, "duration": 40175}, "description": "close folax", "descriptionHtml": "<p>close folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117247301, "stop": 1756117259893, "duration": 12592}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117259893, "stop": 1756117259893, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close folax", "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1756117259894, "stop": 1756117299837, "duration": 39943}, "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1756117259894, "stop": 1756117299590, "duration": 39696}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117299590, "stop": 1756117299837, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "f8aabb68b53386c0", "name": "测试总结", "source": "f8aabb68b53386c0.txt", "type": "text/plain", "size": 744}, {"uid": "31b6c9a4aba80308", "name": "test_completed", "source": "31b6c9a4aba80308.png", "type": "image/png", "size": 151871}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证已打开", "time": {"start": 1756117299837, "stop": 1756117299837, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117299837, "stop": 1756117300068, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "6fe3e93c51dd7df2", "name": "测试总结", "source": "6fe3e93c51dd7df2.txt", "type": "text/plain", "size": 744}, {"uid": "c31a246e3fb972a", "name": "test_completed", "source": "c31a246e3fb972a.png", "type": "image/png", "size": 151903}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9257ee26142b5639", "name": "stdout", "source": "9257ee26142b5639.txt", "type": "text/plain", "size": 16000}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117300069, "stop": 1756117300069, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117300070, "stop": 1756117301538, "duration": 1468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_folax"}, {"name": "subSuite", "value": "TestEllaCloseFolax"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c8e45bb134a34667.json", "parameterValues": []}