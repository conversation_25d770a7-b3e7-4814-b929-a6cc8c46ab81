{"uid": "f10cf9da11b10f19", "name": "测试set call back with last used sim返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim.TestEllaSetCallBackLastUsedSim#test_set_call_back_with_last_used_sim", "historyId": "d45827de1782723ad9f8cd9d38f067dc", "time": {"start": 1756136128445, "stop": 1756136163492, "duration": 35047}, "description": "验证set call back with last used sim指令返回预期的不支持响应", "descriptionHtml": "<p>验证set call back with last used sim指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136115846, "stop": 1756136128443, "duration": 12597}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136128443, "stop": 1756136128443, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set call back with last used sim指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set call back with last used sim", "time": {"start": 1756136128445, "stop": 1756136163217, "duration": 34772}, "status": "passed", "steps": [{"name": "执行命令: set call back with last used sim", "time": {"start": 1756136128445, "stop": 1756136162934, "duration": 34489}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136162934, "stop": 1756136163217, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "2c0d02d7061b0d76", "name": "测试总结", "source": "2c0d02d7061b0d76.txt", "type": "text/plain", "size": 377}, {"uid": "5baa71e70910353f", "name": "test_completed", "source": "5baa71e70910353f.png", "type": "image/png", "size": 198199}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136163217, "stop": 1756136163218, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136163218, "stop": 1756136163491, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "e0b4277aa17a5509", "name": "测试总结", "source": "e0b4277aa17a5509.txt", "type": "text/plain", "size": 377}, {"uid": "29e1ab1b8cc4cb0f", "name": "test_completed", "source": "29e1ab1b8cc4cb0f.png", "type": "image/png", "size": 198010}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "21c51fee4f4ff85e", "name": "stdout", "source": "21c51fee4f4ff85e.txt", "type": "text/plain", "size": 13156}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136163493, "stop": 1756136163493, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136163494, "stop": 1756136164953, "duration": 1459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_call_back_with_last_used_sim"}, {"name": "subSuite", "value": "TestEllaSetCallBackLastUsedSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f10cf9da11b10f19.json", "parameterValues": []}