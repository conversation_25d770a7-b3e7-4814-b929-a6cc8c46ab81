{"uid": "6ecfb780378dd93e", "name": "测试next channel能正常执行", "fullName": "testcases.test_ella.component_coupling.test_next_channel.TestEllaNextChannel#test_next_channel", "historyId": "1d15cba90ae0426fa12e3218f1c542a6", "time": {"start": 1756117637727, "stop": 1756117663471, "duration": 25744}, "description": "next channel", "descriptionHtml": "<p>next channel</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117625161, "stop": 1756117637725, "duration": 12564}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117637725, "stop": 1756117637725, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "next channel", "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1756117637727, "stop": 1756117663235, "duration": 25508}, "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1756117637727, "stop": 1756117663001, "duration": 25274}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117663001, "stop": 1756117663234, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "5775555968f58d84", "name": "测试总结", "source": "5775555968f58d84.txt", "type": "text/plain", "size": 313}, {"uid": "a9ce5030c4610bad", "name": "test_completed", "source": "a9ce5030c4610bad.png", "type": "image/png", "size": 185024}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117663235, "stop": 1756117663236, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117663236, "stop": 1756117663471, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "88e4a8a496c6ffdd", "name": "测试总结", "source": "88e4a8a496c6ffdd.txt", "type": "text/plain", "size": 313}, {"uid": "47b6f54000cd0646", "name": "test_completed", "source": "47b6f54000cd0646.png", "type": "image/png", "size": 185024}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4fc765831a5afa3a", "name": "stdout", "source": "4fc765831a5afa3a.txt", "type": "text/plain", "size": 12536}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117663472, "stop": 1756117663472, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117663474, "stop": 1756117664729, "duration": 1255}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_next_channel"}, {"name": "subSuite", "value": "TestEllaNextChannel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_next_channel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6ecfb780378dd93e.json", "parameterValues": []}