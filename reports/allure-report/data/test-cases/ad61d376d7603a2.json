{"uid": "ad61d376d7603a2", "name": "测试Switch to davido voice能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_davido_voice.TestEllaSwitchDavidoVoice#test_switch_to_davido_voice", "historyId": "cce948f7c988b6f22bd4e8d08ca74deb", "time": {"start": 1756137978128, "stop": 1756138005202, "duration": 27074}, "description": "Switch to davido voice", "descriptionHtml": "<p>Switch to davido voice</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137965248, "stop": 1756137978127, "duration": 12879}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137978128, "stop": 1756137978128, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Switch to davido voice", "status": "passed", "steps": [{"name": "执行命令: Switch to davido voice", "time": {"start": 1756137978128, "stop": 1756138004963, "duration": 26835}, "status": "passed", "steps": [{"name": "执行命令: Switch to davido voice", "time": {"start": 1756137978128, "stop": 1756138004697, "duration": 26569}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138004697, "stop": 1756138004963, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "c073597f1bc7c63a", "name": "测试总结", "source": "c073597f1bc7c63a.txt", "type": "text/plain", "size": 286}, {"uid": "4a90c0a555e4b172", "name": "test_completed", "source": "4a90c0a555e4b172.png", "type": "image/png", "size": 184754}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138004963, "stop": 1756138004965, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138004965, "stop": 1756138005201, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "48c51fb4bd67fa3b", "name": "测试总结", "source": "48c51fb4bd67fa3b.txt", "type": "text/plain", "size": 286}, {"uid": "62bb6e3611501950", "name": "test_completed", "source": "62bb6e3611501950.png", "type": "image/png", "size": 184754}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "518e582aeab912fd", "name": "stdout", "source": "518e582aeab912fd.txt", "type": "text/plain", "size": 12552}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138005202, "stop": 1756138005202, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138005203, "stop": 1756138006590, "duration": 1387}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_davido_voice"}, {"name": "subSuite", "value": "TestEllaSwitchDavidoVoice"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_davido_voice"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ad61d376d7603a2.json", "parameterValues": []}