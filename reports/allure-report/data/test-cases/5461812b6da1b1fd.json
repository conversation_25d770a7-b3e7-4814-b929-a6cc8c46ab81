{"uid": "5461812b6da1b1fd", "name": "测试open folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_folax.TestEllaCommandConcise#test_open_folax", "historyId": "9da64d3434f91a12d693ed9c71b62e87", "time": {"start": 1756117968837, "stop": 1756117995252, "duration": 26415}, "description": "open folax", "descriptionHtml": "<p>open folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117956247, "stop": 1756117968836, "duration": 12589}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117968836, "stop": 1756117968836, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1756117968838, "stop": 1756117995017, "duration": 26179}, "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1756117968838, "stop": 1756117994739, "duration": 25901}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117994739, "stop": 1756117995017, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "f51d0df35e9fa385", "name": "测试总结", "source": "f51d0df35e9fa385.txt", "type": "text/plain", "size": 256}, {"uid": "7ba7a4ad4cc12c52", "name": "test_completed", "source": "7ba7a4ad4cc12c52.png", "type": "image/png", "size": 169428}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756117995017, "stop": 1756117995018, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117995018, "stop": 1756117995251, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "222d3bdd37395097", "name": "测试总结", "source": "222d3bdd37395097.txt", "type": "text/plain", "size": 256}, {"uid": "655c0e4dad56c659", "name": "test_completed", "source": "655c0e4dad56c659.png", "type": "image/png", "size": 169428}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "68c12da8d560c04d", "name": "stdout", "source": "68c12da8d560c04d.txt", "type": "text/plain", "size": 12472}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117995252, "stop": 1756117995252, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117995254, "stop": 1756117996652, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5461812b6da1b1fd.json", "parameterValues": []}