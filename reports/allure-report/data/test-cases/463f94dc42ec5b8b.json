{"uid": "463f94dc42ec5b8b", "name": "测试download in play store", "fullName": "testcases.test_ella.unsupported_commands.test_download_in_play_store.TestEllaOpenGooglePlaystore#test_download_in_play_store", "historyId": "e1f3b35ff3714fe0225699eb84cc1b87", "time": {"start": 1756131247428, "stop": 1756131278406, "duration": 30978}, "description": "测试download in play store指令", "descriptionHtml": "<p>测试download in play store指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131234954, "stop": 1756131247427, "duration": 12473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131247427, "stop": 1756131247427, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试download in play store指令", "status": "passed", "steps": [{"name": "执行命令: download in play store", "time": {"start": 1756131247428, "stop": 1756131278172, "duration": 30744}, "status": "passed", "steps": [{"name": "执行命令: download in play store", "time": {"start": 1756131247428, "stop": 1756131277920, "duration": 30492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131277920, "stop": 1756131278171, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "ee9c3c872f050bf9", "name": "测试总结", "source": "ee9c3c872f050bf9.txt", "type": "text/plain", "size": 323}, {"uid": "509b38350c81a5f0", "name": "test_completed", "source": "509b38350c81a5f0.png", "type": "image/png", "size": 179771}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756131278172, "stop": 1756131278173, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证google_playstore已打开", "time": {"start": 1756131278173, "stop": 1756131278173, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131278173, "stop": 1756131278405, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "e0436257e79ddfe6", "name": "测试总结", "source": "e0436257e79ddfe6.txt", "type": "text/plain", "size": 323}, {"uid": "7a2add92638a4dbb", "name": "test_completed", "source": "7a2add92638a4dbb.png", "type": "image/png", "size": 179405}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "76694d598113a68a", "name": "stdout", "source": "76694d598113a68a.txt", "type": "text/plain", "size": 15232}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131278408, "stop": 1756131278408, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131278410, "stop": 1756131279836, "duration": 1426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_download_in_play_store"}, {"name": "subSuite", "value": "TestEllaOpenGooglePlaystore"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_download_in_play_store"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "463f94dc42ec5b8b.json", "parameterValues": []}