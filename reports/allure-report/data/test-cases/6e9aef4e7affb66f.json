{"uid": "6e9aef4e7affb66f", "name": "测试switch to flash notification能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification#test_switch_to_flash_notification", "historyId": "b2f52f3c587a626460e5698cac861baf", "time": {"start": 1756126766373, "stop": 1756126792589, "duration": 26216}, "description": "switch to flash notification", "descriptionHtml": "<p>switch to flash notification</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126753654, "stop": 1756126766371, "duration": 12717}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126766372, "stop": 1756126766372, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch to flash notification", "status": "passed", "steps": [{"name": "执行命令: switch to flash notification", "time": {"start": 1756126766373, "stop": 1756126792353, "duration": 25980}, "status": "passed", "steps": [{"name": "执行命令: switch to flash notification", "time": {"start": 1756126766373, "stop": 1756126792050, "duration": 25677}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126792050, "stop": 1756126792352, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "245fdd4d35a555a0", "name": "测试总结", "source": "245fdd4d35a555a0.txt", "type": "text/plain", "size": 357}, {"uid": "57e0bb2817222a2e", "name": "test_completed", "source": "57e0bb2817222a2e.png", "type": "image/png", "size": 185048}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126792353, "stop": 1756126792357, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126792357, "stop": 1756126792589, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "f80da4a717c63f94", "name": "测试总结", "source": "f80da4a717c63f94.txt", "type": "text/plain", "size": 357}, {"uid": "8620d3f237a9b37b", "name": "test_completed", "source": "8620d3f237a9b37b.png", "type": "image/png", "size": 185048}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9d55502fc50b3d87", "name": "stdout", "source": "9d55502fc50b3d87.txt", "type": "text/plain", "size": 13191}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126792591, "stop": 1756126792591, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126792592, "stop": 1756126793989, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_flash_notification"}, {"name": "subSuite", "value": "TestEllaSwitchToFlashNotification"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_flash_notification"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6e9aef4e7affb66f.json", "parameterValues": []}