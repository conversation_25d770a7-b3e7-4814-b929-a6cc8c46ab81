{"uid": "aed5af6a70cdfe0c", "name": "测试can u check the notebook", "fullName": "testcases.test_ella.unsupported_commands.test_can_u_check_the_notebook.TestEllaOpenPlayPoliticalNews#test_can_u_check_the_notebook", "historyId": "400a9b197316d3b1e59fe33ed78a836a", "time": {"start": 1756129880681, "stop": 1756129914205, "duration": 33524}, "description": "测试can u check the notebook指令", "descriptionHtml": "<p>测试can u check the notebook指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129868040, "stop": 1756129880678, "duration": 12638}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129880679, "stop": 1756129880679, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试can u check the notebook指令", "status": "passed", "steps": [{"name": "执行命令: can u check the notebook", "time": {"start": 1756129880681, "stop": 1756129913952, "duration": 33271}, "status": "passed", "steps": [{"name": "执行命令: can u check the notebook", "time": {"start": 1756129880681, "stop": 1756129913718, "duration": 33037}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129913718, "stop": 1756129913952, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "fcbfc2a6ab3e0f02", "name": "测试总结", "source": "fcbfc2a6ab3e0f02.txt", "type": "text/plain", "size": 367}, {"uid": "5e7ee1a5295efd6a", "name": "test_completed", "source": "5e7ee1a5295efd6a.png", "type": "image/png", "size": 171735}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129913952, "stop": 1756129913954, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证notes已打开", "time": {"start": 1756129913954, "stop": 1756129913954, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129913954, "stop": 1756129914205, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "f7b019e2c704d374", "name": "测试总结", "source": "f7b019e2c704d374.txt", "type": "text/plain", "size": 367}, {"uid": "1b617a373d876432", "name": "test_completed", "source": "1b617a373d876432.png", "type": "image/png", "size": 171844}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "53866d5779ce19d5", "name": "stdout", "source": "53866d5779ce19d5.txt", "type": "text/plain", "size": 15289}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129914206, "stop": 1756129914206, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129914206, "stop": 1756129915623, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_can_u_check_the_notebook"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_can_u_check_the_notebook"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "aed5af6a70cdfe0c.json", "parameterValues": []}