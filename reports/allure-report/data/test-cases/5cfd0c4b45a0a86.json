{"uid": "5cfd0c4b45a0a86", "name": "测试show my all alarms能正常执行", "fullName": "testcases.test_ella.dialogue.test_show_my_all_alarms.TestEllaHowIsWeatherToday#test_show_my_all_alarms", "historyId": "1147a84f37b71eb5b15008169cadcc53", "time": {"start": 1756121550624, "stop": 1756121602706, "duration": 52082}, "description": "show my all alarms", "descriptionHtml": "<p>show my all alarms</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121537856, "stop": 1756121550623, "duration": 12767}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121550623, "stop": 1756121550623, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "show my all alarms", "status": "passed", "steps": [{"name": "执行命令:  Set an alarm at 10 am tomorrow", "time": {"start": 1756121550624, "stop": 1756121575867, "duration": 25243}, "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "time": {"start": 1756121550624, "stop": 1756121575588, "duration": 24964}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121575588, "stop": 1756121575867, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "985c7fe29e56225f", "name": "测试总结", "source": "985c7fe29e56225f.txt", "type": "text/plain", "size": 263}, {"uid": "8ae571dbd22f0234", "name": "test_completed", "source": "8ae571dbd22f0234.png", "type": "image/png", "size": 155642}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: show my all alarms", "time": {"start": 1756121575867, "stop": 1756121602471, "duration": 26604}, "status": "passed", "steps": [{"name": "执行命令: show my all alarms", "time": {"start": 1756121575867, "stop": 1756121602175, "duration": 26308}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121602176, "stop": 1756121602469, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "b2657ba265412beb", "name": "测试总结", "source": "b2657ba265412beb.txt", "type": "text/plain", "size": 224}, {"uid": "bb815b0c2a12165e", "name": "test_completed", "source": "bb815b0c2a12165e.png", "type": "image/png", "size": 141136}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121602471, "stop": 1756121602472, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121602472, "stop": 1756121602706, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "40df0ed5d697e804", "name": "测试总结", "source": "40df0ed5d697e804.txt", "type": "text/plain", "size": 224}, {"uid": "800b571e51eee606", "name": "test_completed", "source": "800b571e51eee606.png", "type": "image/png", "size": 140637}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "954d2f3efcb176a9", "name": "stdout", "source": "954d2f3efcb176a9.txt", "type": "text/plain", "size": 22179}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 8, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121602707, "stop": 1756121602707, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121602708, "stop": 1756121604070, "duration": 1362}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_my_all_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_my_all_alarms"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5cfd0c4b45a0a86.json", "parameterValues": []}