{"uid": "24fca122ebf8aaf1", "name": "测试start running能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_start_running.TestEllaStartRunning#test_start_running", "historyId": "5c75e7ecaa2fe55a9b9666aae0ca1b5a", "time": {"start": 1756137854104, "stop": 1756137885210, "duration": 31106}, "description": "start running", "descriptionHtml": "<p>start running</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137841228, "stop": 1756137854102, "duration": 12874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137854102, "stop": 1756137854102, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "start running", "status": "passed", "steps": [{"name": "执行命令: start running", "time": {"start": 1756137854104, "stop": 1756137884986, "duration": 30882}, "status": "passed", "steps": [{"name": "执行命令: start running", "time": {"start": 1756137854104, "stop": 1756137884684, "duration": 30580}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137884684, "stop": 1756137884986, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "c6f6df282cadb3cc", "name": "测试总结", "source": "c6f6df282cadb3cc.txt", "type": "text/plain", "size": 361}, {"uid": "269b396731a9160", "name": "test_completed", "source": "269b396731a9160.png", "type": "image/png", "size": 161503}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756137884986, "stop": 1756137884987, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证Health应用已打开", "time": {"start": 1756137884987, "stop": 1756137884987, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137884987, "stop": 1756137885209, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "9bab6cecc2c570af", "name": "测试总结", "source": "9bab6cecc2c570af.txt", "type": "text/plain", "size": 361}, {"uid": "72a511cbc2c81d8c", "name": "test_completed", "source": "72a511cbc2c81d8c.png", "type": "image/png", "size": 161850}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8a45a2e1609111b6", "name": "stdout", "source": "8a45a2e1609111b6.txt", "type": "text/plain", "size": 15168}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137885211, "stop": 1756137885211, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756137885213, "stop": 1756137886575, "duration": 1362}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_start_running"}, {"name": "subSuite", "value": "TestEllaStartRunning"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_start_running"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "24fca122ebf8aaf1.json", "parameterValues": []}