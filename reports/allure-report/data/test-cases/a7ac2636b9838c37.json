{"uid": "a7ac2636b9838c37", "name": "测试create a metting schedule at tomorrow能正常执行", "fullName": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow.TestEllaCreateMettingScheduleTomorrow#test_create_a_metting_schedule_at_tomorrow", "historyId": "********************************", "time": {"start": 1756117394608, "stop": 1756117421754, "duration": 27146}, "description": "create a metting schedule at tomorrow", "descriptionHtml": "<p>create a metting schedule at tomorrow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117381630, "stop": 1756117394606, "duration": 12976}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117394606, "stop": 1756117394606, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "create a metting schedule at tomorrow", "status": "passed", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "time": {"start": 1756117394608, "stop": 1756117421530, "duration": 26922}, "status": "passed", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "time": {"start": 1756117394608, "stop": 1756117421271, "duration": 26663}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117421271, "stop": 1756117421529, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "a38beb6a5d41a33c", "name": "测试总结", "source": "a38beb6a5d41a33c.txt", "type": "text/plain", "size": 267}, {"uid": "5365022f5ba64836", "name": "test_completed", "source": "5365022f5ba64836.png", "type": "image/png", "size": 162189}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117421530, "stop": 1756117421533, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117421533, "stop": 1756117421754, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "671785096393d504", "name": "测试总结", "source": "671785096393d504.txt", "type": "text/plain", "size": 267}, {"uid": "cc3c88409d9dadb1", "name": "test_completed", "source": "cc3c88409d9dadb1.png", "type": "image/png", "size": 162280}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "af6de6fab6d73231", "name": "stdout", "source": "af6de6fab6d73231.txt", "type": "text/plain", "size": 12515}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117421755, "stop": 1756117421755, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117421757, "stop": 1756117423170, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_create_a_metting_schedule_at_tomorrow"}, {"name": "subSuite", "value": "TestEllaCreateMettingScheduleTomorrow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a7ac2636b9838c37.json", "parameterValues": []}