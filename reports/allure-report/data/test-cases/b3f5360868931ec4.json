{"uid": "b3f5360868931ec4", "name": "测试set screen timeout返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_timeout.TestEllaSetScreenTimeout#test_set_screen_timeout", "historyId": "e76af38ac3a594aa2b7d7173d57e98ad", "time": {"start": 1756137320659, "stop": 1756137357335, "duration": 36676}, "description": "验证set screen timeout指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen timeout指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen timeout', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch | Screen Resolution | 1080x2400 | Display | Screen Timeout | After 30 Seconds of inactivity | Fonts']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_screen_timeout.TestEllaSetScreenTimeout object at 0x00000292057ACAD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A819650>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_screen_timeout(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen timeout', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch | Screen Resolution | 1080x2400 | Display | Screen Timeout | After 30 Seconds of inactivity | Fonts']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_screen_timeout.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137307489, "stop": 1756137320657, "duration": 13168}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137320657, "stop": 1756137320658, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set screen timeout指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen timeout', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch | Screen Resolution | 1080x2400 | Display | Screen Timeout | After 30 Seconds of inactivity | Fonts']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_screen_timeout.TestEllaSetScreenTimeout object at 0x00000292057ACAD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A819650>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_screen_timeout(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen timeout', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch | Screen Resolution | 1080x2400 | Display | Screen Timeout | After 30 Seconds of inactivity | Fonts']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_screen_timeout.py:33: AssertionError", "steps": [{"name": "执行命令: set screen timeout", "time": {"start": 1756137320659, "stop": 1756137357331, "duration": 36672}, "status": "passed", "steps": [{"name": "执行命令: set screen timeout", "time": {"start": 1756137320659, "stop": 1756137357051, "duration": 36392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137357051, "stop": 1756137357330, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "cfd430c3ee3728b6", "name": "测试总结", "source": "cfd430c3ee3728b6.txt", "type": "text/plain", "size": 679}, {"uid": "cb23b8c55fc96ce5", "name": "test_completed", "source": "cb23b8c55fc96ce5.png", "type": "image/png", "size": 162091}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137357331, "stop": 1756137357334, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set screen timeout', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature. | Color Style | Screen Refresh Rate | Auto-Switch | Screen Resolution | 1080x2400 | Display | Screen Timeout | After 30 Seconds of inactivity | Fonts']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_screen_timeout.py\", line 33, in test_set_screen_timeout\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1003bbccab8546d0", "name": "stdout", "source": "1003bbccab8546d0.txt", "type": "text/plain", "size": 16483}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137357338, "stop": 1756137357565, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "8900f52ebb56f3e2", "name": "失败截图-TestEllaSetScreenTimeout", "source": "8900f52ebb56f3e2.png", "type": "image/png", "size": 162014}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137357565, "stop": 1756137359030, "duration": 1465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_timeout"}, {"name": "subSuite", "value": "TestEllaSetScreenTimeout"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_timeout"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b3f5360868931ec4.json", "parameterValues": []}