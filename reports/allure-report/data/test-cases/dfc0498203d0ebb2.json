{"uid": "dfc0498203d0ebb2", "name": "测试show scores between livepool and manchester city能正常执行", "fullName": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city.TestEllaShowScoresBetweenLivepoolManchesterCity#test_show_scores_between_livepool_and_manchester_city", "historyId": "83e3a1b41834e87017b680efd7c16b92", "time": {"start": 1756121616621, "stop": 1756121644430, "duration": 27809}, "description": "show scores between livepool and manchester city", "descriptionHtml": "<p>show scores between livepool and manchester city</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121604074, "stop": 1756121616620, "duration": 12546}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121616621, "stop": 1756121616621, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "show scores between livepool and manchester city", "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1756121616621, "stop": 1756121644125, "duration": 27504}, "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1756121616621, "stop": 1756121643866, "duration": 27245}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121643866, "stop": 1756121644124, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "8c2879cb80ee32d7", "name": "测试总结", "source": "8c2879cb80ee32d7.txt", "type": "text/plain", "size": 1167}, {"uid": "ba952b75d41314f0", "name": "test_completed", "source": "ba952b75d41314f0.png", "type": "image/png", "size": 209512}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121644125, "stop": 1756121644135, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121644135, "stop": 1756121644428, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "ac108dc81b39b75", "name": "测试总结", "source": "ac108dc81b39b75.txt", "type": "text/plain", "size": 1167}, {"uid": "fec4f4bb6e9cd398", "name": "test_completed", "source": "fec4f4bb6e9cd398.png", "type": "image/png", "size": 209294}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "91f4dcd25919e8e4", "name": "stdout", "source": "91f4dcd25919e8e4.txt", "type": "text/plain", "size": 17123}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121644431, "stop": 1756121644431, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121644434, "stop": 1756121645881, "duration": 1447}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_scores_between_livepool_and_manchester_city"}, {"name": "subSuite", "value": "TestEllaShowScoresBetweenLivepoolManchesterCity"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dfc0498203d0ebb2.json", "parameterValues": []}