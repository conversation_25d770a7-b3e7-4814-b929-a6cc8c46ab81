{"uid": "1e5532003ccecf8a", "name": "测试vedio call number by whatsapp能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp.TestEllaVedioCallNumberWhatsapp#test_vedio_call_number_by_whatsapp", "historyId": "600ddf60808e2a751a4a4742a65811c7", "time": {"start": 1756138723242, "stop": 1756138757087, "duration": 33845}, "description": "vedio call number by whatsapp", "descriptionHtml": "<p>vedio call number by whatsapp</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['vedio call number by whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp.TestEllaVedioCallNumberWhatsapp object at 0x0000029205940F50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920ACB6410>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_vedio_call_number_by_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['vedio call number by whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_vedio_call_number_by_whatsapp.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138710372, "stop": 1756138723240, "duration": 12868}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138723240, "stop": 1756138723240, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "vedio call number by whatsapp", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['vedio call number by whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp.TestEllaVedioCallNumberWhatsapp object at 0x0000029205940F50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920ACB6410>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_vedio_call_number_by_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['vedio call number by whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_vedio_call_number_by_whatsapp.py:36: AssertionError", "steps": [{"name": "执行命令: vedio call number by whatsapp", "time": {"start": 1756138723243, "stop": 1756138757080, "duration": 33837}, "status": "passed", "steps": [{"name": "执行命令: vedio call number by whatsapp", "time": {"start": 1756138723243, "stop": 1756138756784, "duration": 33541}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138756784, "stop": 1756138757079, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "39aff0e90408f4e5", "name": "测试总结", "source": "39aff0e90408f4e5.txt", "type": "text/plain", "size": 331}, {"uid": "ed067098259cf374", "name": "test_completed", "source": "ed067098259cf374.png", "type": "image/png", "size": 179884}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138757080, "stop": 1756138757085, "duration": 5}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['vedio call number by whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_vedio_call_number_by_whatsapp.py\", line 36, in test_vedio_call_number_by_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "58c96a076f30489d", "name": "stdout", "source": "58c96a076f30489d.txt", "type": "text/plain", "size": 14106}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138757093, "stop": 1756138757333, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "5c7380bbe3c54879", "name": "失败截图-TestEllaVedioCallNumberWhatsapp", "source": "5c7380bbe3c54879.png", "type": "image/png", "size": 179844}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756138757334, "stop": 1756138758717, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_vedio_call_number_by_whatsapp"}, {"name": "subSuite", "value": "TestEllaVedioCallNumberWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "1e5532003ccecf8a.json", "parameterValues": []}