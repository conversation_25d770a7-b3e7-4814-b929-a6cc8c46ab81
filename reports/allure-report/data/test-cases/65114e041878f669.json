{"uid": "65114e041878f669", "name": "测试min alarm clock volume", "fullName": "testcases.test_ella.system_coupling.test_min_alarm_clock_volume.TestEllaOpenAlarmVolume#test_min_alarm_clock_volume", "historyId": "963c2cd1bdb409e4cfe9589a18006e88", "time": {"start": 1756125064327, "stop": 1756125090788, "duration": 26461}, "description": "测试min alarm clock volume指令", "descriptionHtml": "<p>测试min alarm clock volume指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125051170, "stop": 1756125064326, "duration": 13156}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125064326, "stop": 1756125064326, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试min alarm clock volume指令", "status": "passed", "steps": [{"name": "执行命令: min alarm clock volume", "time": {"start": 1756125064328, "stop": 1756125090527, "duration": 26199}, "status": "passed", "steps": [{"name": "执行命令: min alarm clock volume", "time": {"start": 1756125064328, "stop": 1756125090239, "duration": 25911}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125090239, "stop": 1756125090526, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "8896fc65b059be31", "name": "测试总结", "source": "8896fc65b059be31.txt", "type": "text/plain", "size": 311}, {"uid": "4384ff9721235d54", "name": "test_completed", "source": "4384ff9721235d54.png", "type": "image/png", "size": 178357}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125090527, "stop": 1756125090529, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证alarm_volume已打开", "time": {"start": 1756125090529, "stop": 1756125090530, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125090530, "stop": 1756125090784, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "e546f05bb9efa9a2", "name": "测试总结", "source": "e546f05bb9efa9a2.txt", "type": "text/plain", "size": 311}, {"uid": "24b55ec4cd1976d1", "name": "test_completed", "source": "24b55ec4cd1976d1.png", "type": "image/png", "size": 178357}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "dae0f2172062ed84", "name": "stdout", "source": "dae0f2172062ed84.txt", "type": "text/plain", "size": 13905}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125090789, "stop": 1756125090789, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125090790, "stop": 1756125092249, "duration": 1459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_alarm_clock_volume"}, {"name": "subSuite", "value": "TestEllaOpenAlarmVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_alarm_clock_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "65114e041878f669.json", "parameterValues": []}