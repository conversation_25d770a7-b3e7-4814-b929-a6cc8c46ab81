{"uid": "b13c561de12c3122", "name": "测试how's the weather today in shanghai能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "bd4d204a449f3a4013b03af9a9101446", "time": {"start": 1756120070685, "stop": 1756120104313, "duration": 33628}, "description": "how's the weather today in shanghai", "descriptionHtml": "<p>how's the weather today in shanghai</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120057727, "stop": 1756120070683, "duration": 12956}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120070683, "stop": 1756120070683, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "how's the weather today in shanghai", "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1756120070685, "stop": 1756120104064, "duration": 33379}, "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1756120070685, "stop": 1756120103789, "duration": 33104}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120103789, "stop": 1756120104063, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "fb9aeb97e1733ea9", "name": "测试总结", "source": "fb9aeb97e1733ea9.txt", "type": "text/plain", "size": 297}, {"uid": "38920dc2956cdeaf", "name": "test_completed", "source": "38920dc2956cdeaf.png", "type": "image/png", "size": 177341}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120104064, "stop": 1756120104065, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "使用优化后的天气数据验证范围", "time": {"start": 1756120104065, "stop": 1756120104070, "duration": 5}, "status": "passed", "steps": [], "attachments": [{"uid": "cd8748284a313286", "name": "天气数据验证通过", "source": "cd8748284a313286.txt", "type": "text/plain", "size": 81}, {"uid": "fe60f0e9f923592f", "name": "温度验证", "source": "fe60f0e9f923592f.txt", "type": "text/plain", "size": 16}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "记录测试结果", "time": {"start": 1756120104070, "stop": 1756120104312, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "76b6f06c5231b447", "name": "测试总结", "source": "76b6f06c5231b447.txt", "type": "text/plain", "size": 297}, {"uid": "408d572bb38314f1", "name": "test_completed", "source": "408d572bb38314f1.png", "type": "image/png", "size": 177341}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c4878f6540b8f24b", "name": "stdout", "source": "c4878f6540b8f24b.txt", "type": "text/plain", "size": 13006}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120104314, "stop": 1756120104314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120104315, "stop": 1756120105662, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today_in_shanghai"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b13c561de12c3122.json", "parameterValues": []}