{"uid": "edc32cfdc4f6efba", "name": "测试pls open the newest whatsapp activity", "fullName": "testcases.test_ella.third_coupling.test_pls_open_the_newest_whatsapp_activity.TestEllaOpenPlsNewestWhatsappActivity#test_pls_open_the_newest_whatsapp_activity", "historyId": "fc477656b55eea3a3906a5bdcaa93554", "time": {"start": 1756129259051, "stop": 1756129286316, "duration": 27265}, "description": "测试pls open the newest whatsapp activity指令", "descriptionHtml": "<p>测试pls open the newest whatsapp activity指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129246293, "stop": 1756129259045, "duration": 12752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129259046, "stop": 1756129259046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试pls open the newest whatsapp activity指令", "status": "passed", "steps": [{"name": "执行命令: pls open the newest whatsapp activity", "time": {"start": 1756129259051, "stop": 1756129286080, "duration": 27029}, "status": "passed", "steps": [{"name": "执行命令: pls open the newest whatsapp activity", "time": {"start": 1756129259051, "stop": 1756129285800, "duration": 26749}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129285800, "stop": 1756129286080, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "7395ca05b8f607ba", "name": "测试总结", "source": "7395ca05b8f607ba.txt", "type": "text/plain", "size": 347}, {"uid": "238769aeca5fb45a", "name": "test_completed", "source": "238769aeca5fb45a.png", "type": "image/png", "size": 188743}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129286080, "stop": 1756129286082, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129286082, "stop": 1756129286314, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "738b16a4b026eae6", "name": "测试总结", "source": "738b16a4b026eae6.txt", "type": "text/plain", "size": 347}, {"uid": "42fd39afc1b620d2", "name": "test_completed", "source": "42fd39afc1b620d2.png", "type": "image/png", "size": 188764}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fb7c79ae3af03340", "name": "stdout", "source": "fb7c79ae3af03340.txt", "type": "text/plain", "size": 13246}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129286317, "stop": 1756129286317, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129286319, "stop": 1756129287790, "duration": 1471}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_pls_open_the_newest_whatsapp_activity"}, {"name": "subSuite", "value": "TestEllaOpenPlsNewestWhatsappActivity"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_pls_open_the_newest_whatsapp_activity"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "edc32cfdc4f6efba.json", "parameterValues": []}