{"uid": "839926c8ff2b840d", "name": "测试turn on bluetooth能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_bluetooth.TestEllaTurnBluetooth#test_turn_on_bluetooth", "historyId": "aff947fee562ec2636c3ce68a270b88d", "time": {"start": 1756127820955, "stop": 1756127847479, "duration": 26524}, "description": "turn on bluetooth", "descriptionHtml": "<p>turn on bluetooth</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127807778, "stop": 1756127820954, "duration": 13176}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127820954, "stop": 1756127820954, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on bluetooth", "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1756127820955, "stop": 1756127847237, "duration": 26282}, "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1756127820955, "stop": 1756127846960, "duration": 26005}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127846960, "stop": 1756127847237, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "654c22324d2f0364", "name": "测试总结", "source": "654c22324d2f0364.txt", "type": "text/plain", "size": 220}, {"uid": "8ae71e7cddde93a6", "name": "test_completed", "source": "8ae71e7cddde93a6.png", "type": "image/png", "size": 151436}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127847237, "stop": 1756127847238, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127847238, "stop": 1756127847238, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127847238, "stop": 1756127847478, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "b3edb18b93fc7fca", "name": "测试总结", "source": "b3edb18b93fc7fca.txt", "type": "text/plain", "size": 220}, {"uid": "fa640ebbd15a785b", "name": "test_completed", "source": "fa640ebbd15a785b.png", "type": "image/png", "size": 151436}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "524b7fc5e79b30a0", "name": "stdout", "source": "524b7fc5e79b30a0.txt", "type": "text/plain", "size": 13422}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127847479, "stop": 1756127847479, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127847480, "stop": 1756127848839, "duration": 1359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_bluetooth"}, {"name": "subSuite", "value": "TestEllaTurnBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "839926c8ff2b840d.json", "parameterValues": []}