{"uid": "99fa5e5a03fe7a2c", "name": "测试extend the image能正常执行", "fullName": "testcases.test_ella.self_function.test_extend_the_image.TestEllaExtendImage#test_extend_the_image", "historyId": "4dcdc98a8f38f748728aec71f673e025", "time": {"start": 1756123086120, "stop": 1756123208419, "duration": 122299}, "description": "extend the image", "descriptionHtml": "<p>extend the image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123073250, "stop": 1756123086119, "duration": 12869}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123086119, "stop": 1756123086119, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "extend the image", "status": "passed", "steps": [{"name": "执行命令: extend the image", "time": {"start": 1756123086121, "stop": 1756123208167, "duration": 122046}, "status": "passed", "steps": [{"name": "执行命令: extend the image", "time": {"start": 1756123086121, "stop": 1756123207870, "duration": 121749}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123207870, "stop": 1756123208167, "duration": 297}, "status": "passed", "steps": [], "attachments": [{"uid": "22990f476b782ed5", "name": "测试总结", "source": "22990f476b782ed5.txt", "type": "text/plain", "size": 391}, {"uid": "51d7f67cdaf0b4d8", "name": "test_completed", "source": "51d7f67cdaf0b4d8.png", "type": "image/png", "size": 251712}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123208167, "stop": 1756123208168, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123208168, "stop": 1756123208418, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "9d0c1ef518feb9b6", "name": "测试总结", "source": "9d0c1ef518feb9b6.txt", "type": "text/plain", "size": 345}, {"uid": "7d068f62a119fdcf", "name": "test_completed", "source": "7d068f62a119fdcf.png", "type": "image/png", "size": 251738}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9cc2c2a597419a4c", "name": "stdout", "source": "9cc2c2a597419a4c.txt", "type": "text/plain", "size": 19198}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123208420, "stop": 1756123208420, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123208420, "stop": 1756123209777, "duration": 1357}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_extend_the_image"}, {"name": "subSuite", "value": "TestEllaExtendImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_extend_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "99fa5e5a03fe7a2c.json", "parameterValues": []}