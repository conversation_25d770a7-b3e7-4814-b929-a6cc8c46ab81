{"uid": "f87afea6e4f5c695", "name": "测试help me generate a picture of an airplane", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_airplane.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_an_airplane", "historyId": "411d5cfcc1960041b8df4decf67232f6", "time": {"start": 1756132735808, "stop": 1756132763443, "duration": 27635}, "description": "测试help me generate a picture of an airplane指令", "descriptionHtml": "<p>测试help me generate a picture of an airplane指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132722876, "stop": 1756132735807, "duration": 12931}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132735807, "stop": 1756132735807, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试help me generate a picture of an airplane指令", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an airplane", "time": {"start": 1756132735808, "stop": 1756132763188, "duration": 27380}, "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an airplane", "time": {"start": 1756132735808, "stop": 1756132762957, "duration": 27149}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132762957, "stop": 1756132763187, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "9911dc6574098f0d", "name": "测试总结", "source": "9911dc6574098f0d.txt", "type": "text/plain", "size": 365}, {"uid": "7b60d9c6da5102b4", "name": "test_completed", "source": "7b60d9c6da5102b4.png", "type": "image/png", "size": 190627}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132763188, "stop": 1756132763189, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132763189, "stop": 1756132763442, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "4bc5ddc9081edcc4", "name": "测试总结", "source": "4bc5ddc9081edcc4.txt", "type": "text/plain", "size": 365}, {"uid": "63b4053ba51b3e8c", "name": "test_completed", "source": "63b4053ba51b3e8c.png", "type": "image/png", "size": 190316}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9e18ccd01266be74", "name": "stdout", "source": "9e18ccd01266be74.txt", "type": "text/plain", "size": 13215}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132763443, "stop": 1756132763443, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132763445, "stop": 1756132764894, "duration": 1449}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_an_airplane"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_airplane"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f87afea6e4f5c695.json", "parameterValues": []}