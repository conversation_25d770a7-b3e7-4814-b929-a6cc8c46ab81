{"uid": "4a3a275a7db82a23", "name": "测试What's the weather like in Shanghai today能正常执行", "fullName": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "53ec4c77118606257c016fc2f7b22065", "time": {"start": 1756119219637, "stop": 1756119252786, "duration": 33149}, "description": "What's the weather like in Shanghai today", "descriptionHtml": "<p>What's the weather like in Shanghai today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119206963, "stop": 1756119219635, "duration": 12672}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119219635, "stop": 1756119219635, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "What's the weather like in Shanghai today", "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1756119219637, "stop": 1756119252594, "duration": 32957}, "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1756119219637, "stop": 1756119252360, "duration": 32723}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119252360, "stop": 1756119252594, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "b4ae938b05ec1dff", "name": "测试总结", "source": "b4ae938b05ec1dff.txt", "type": "text/plain", "size": 302}, {"uid": "5565a015e63f542c", "name": "test_completed", "source": "5565a015e63f542c.png", "type": "image/png", "size": 180206}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119252594, "stop": 1756119252598, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证天气温度在有效范围内(-10℃ ~ 50℃)", "time": {"start": 1756119252598, "stop": 1756119252599, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ab8a1f596ff58d1b", "name": "温度验证", "source": "ab8a1f596ff58d1b.txt", "type": "text/plain", "size": 16}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "记录测试结果", "time": {"start": 1756119252599, "stop": 1756119252786, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "9483fcbf227678d4", "name": "测试总结", "source": "9483fcbf227678d4.txt", "type": "text/plain", "size": 302}, {"uid": "4b5bbc482ac386fc", "name": "test_completed", "source": "4b5bbc482ac386fc.png", "type": "image/png", "size": 180206}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6f8ec5b97944396e", "name": "stdout", "source": "6f8ec5b97944396e.txt", "type": "text/plain", "size": 13162}], "parameters": [], "attachmentsCount": 6, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119252787, "stop": 1756119252787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119252789, "stop": 1756119254149, "duration": 1360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_what_s_the_weather_like_in_shanghai_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4a3a275a7db82a23.json", "parameterValues": []}