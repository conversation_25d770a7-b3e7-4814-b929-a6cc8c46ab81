{"uid": "8525771734ee6dea", "name": "测试turn off smart reminder能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder.TestEllaTurnOffSmartReminder#test_turn_off_smart_reminder", "historyId": "e8f9ac327bc5f166a4c4a14509f365bf", "time": {"start": 1756127579754, "stop": 1756127605501, "duration": 25747}, "description": "turn off smart reminder", "descriptionHtml": "<p>turn off smart reminder</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127566767, "stop": 1756127579752, "duration": 12985}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127579753, "stop": 1756127579753, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn off smart reminder", "status": "passed", "steps": [{"name": "执行命令: turn off smart reminder", "time": {"start": 1756127579754, "stop": 1756127605284, "duration": 25530}, "status": "passed", "steps": [{"name": "执行命令: turn off smart reminder", "time": {"start": 1756127579754, "stop": 1756127605014, "duration": 25260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127605014, "stop": 1756127605284, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "a2506d6908ba90e7", "name": "测试总结", "source": "a2506d6908ba90e7.txt", "type": "text/plain", "size": 325}, {"uid": "114dafd450ba7168", "name": "test_completed", "source": "114dafd450ba7168.png", "type": "image/png", "size": 160301}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127605285, "stop": 1756127605287, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127605287, "stop": 1756127605287, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127605287, "stop": 1756127605499, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "ca934c76a4456438", "name": "测试总结", "source": "ca934c76a4456438.txt", "type": "text/plain", "size": 325}, {"uid": "92eac06d1e6dcb0b", "name": "test_completed", "source": "92eac06d1e6dcb0b.png", "type": "image/png", "size": 160301}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b6b8cf1365090301", "name": "stdout", "source": "b6b8cf1365090301.txt", "type": "text/plain", "size": 13224}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127605502, "stop": 1756127605502, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127605504, "stop": 1756127606884, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_smart_reminder"}, {"name": "subSuite", "value": "TestEllaTurnOffSmartReminder"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8525771734ee6dea.json", "parameterValues": []}