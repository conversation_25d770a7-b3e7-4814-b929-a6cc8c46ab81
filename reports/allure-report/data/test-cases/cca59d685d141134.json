{"uid": "cca59d685d141134", "name": "测试set a timer for 10 minutes能正常执行", "fullName": "testcases.test_ella.system_coupling.test_set_a_timer_for_minutes.TestEllaSetTimerMinutes#test_set_a_timer_for_minutes", "historyId": "f46a1c12d07d5949dbcf4b31314824ec", "time": {"start": 1756125574515, "stop": 1756125608477, "duration": 33962}, "description": "set a timer for 10 minutes", "descriptionHtml": "<p>set a timer for 10 minutes</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125561816, "stop": 1756125574512, "duration": 12696}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125574512, "stop": 1756125574512, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "set a timer for 10 minutes", "status": "passed", "steps": [{"name": "执行命令: set a timer for 10 minutes", "time": {"start": 1756125574515, "stop": 1756125608212, "duration": 33697}, "status": "passed", "steps": [{"name": "执行命令: set a timer for 10 minutes", "time": {"start": 1756125574515, "stop": 1756125607941, "duration": 33426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125607941, "stop": 1756125608211, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "4c8de6e9b2393d3f", "name": "测试总结", "source": "4c8de6e9b2393d3f.txt", "type": "text/plain", "size": 290}, {"uid": "8b52706124f49a5a", "name": "test_completed", "source": "8b52706124f49a5a.png", "type": "image/png", "size": 179169}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125608212, "stop": 1756125608213, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756125608213, "stop": 1756125608214, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125608214, "stop": 1756125608476, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "3b51fd7bd1b8d77d", "name": "测试总结", "source": "3b51fd7bd1b8d77d.txt", "type": "text/plain", "size": 290}, {"uid": "8ca349fe92e7a8e0", "name": "test_completed", "source": "8ca349fe92e7a8e0.png", "type": "image/png", "size": 179181}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e7189d997f29c44c", "name": "stdout", "source": "e7189d997f29c44c.txt", "type": "text/plain", "size": 12989}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125608478, "stop": 1756125608478, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125608479, "stop": 1756125609859, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_a_timer_for_minutes"}, {"name": "subSuite", "value": "TestEllaSetTimerMinutes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_a_timer_for_minutes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cca59d685d141134.json", "parameterValues": []}