{"uid": "1029eb98ba1d10b2", "name": "测试turn on wifi能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi#test_turn_on_wifi", "historyId": "d18ea588937139bb162adb1092a66013", "time": {"start": 1756128388460, "stop": 1756128414919, "duration": 26459}, "description": "turn on wifi", "descriptionHtml": "<p>turn on wifi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128375793, "stop": 1756128388457, "duration": 12664}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128388457, "stop": 1756128388457, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on wifi", "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1756128388460, "stop": 1756128414666, "duration": 26206}, "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1756128388460, "stop": 1756128414424, "duration": 25964}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128414424, "stop": 1756128414665, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "1768a5ed23365f2a", "name": "测试总结", "source": "1768a5ed23365f2a.txt", "type": "text/plain", "size": 202}, {"uid": "2893fa211e86bdad", "name": "test_completed", "source": "2893fa211e86bdad.png", "type": "image/png", "size": 151574}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128414666, "stop": 1756128414669, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128414669, "stop": 1756128414669, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128414669, "stop": 1756128414918, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "e10e85aca280d025", "name": "测试总结", "source": "e10e85aca280d025.txt", "type": "text/plain", "size": 202}, {"uid": "ce2656d5bc9e7620", "name": "test_completed", "source": "ce2656d5bc9e7620.png", "type": "image/png", "size": 151574}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c8d6d33e75e83e30", "name": "stdout", "source": "c8d6d33e75e83e30.txt", "type": "text/plain", "size": 12765}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128414920, "stop": 1756128414920, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128414922, "stop": 1756128416240, "duration": 1318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_wifi"}, {"name": "subSuite", "value": "TestEllaTurnWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1029eb98ba1d10b2.json", "parameterValues": []}