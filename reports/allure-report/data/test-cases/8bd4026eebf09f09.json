{"uid": "8bd4026eebf09f09", "name": "测试i want to hear a joke能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_i_want_to_hear_a_joke.TestEllaIWantHearJoke#test_i_want_to_hear_a_joke", "historyId": "5dbd6c476e40c9de0215f0509dd43986", "time": {"start": 1756133303122, "stop": 1756133330447, "duration": 27325}, "description": "i want to hear a joke", "descriptionHtml": "<p>i want to hear a joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133290234, "stop": 1756133303121, "duration": 12887}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133303121, "stop": 1756133303121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "i want to hear a joke", "status": "passed", "steps": [{"name": "执行命令: i want to hear a joke", "time": {"start": 1756133303122, "stop": 1756133330202, "duration": 27080}, "status": "passed", "steps": [{"name": "执行命令: i want to hear a joke", "time": {"start": 1756133303122, "stop": 1756133329900, "duration": 26778}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133329900, "stop": 1756133330201, "duration": 301}, "status": "passed", "steps": [], "attachments": [{"uid": "c24d1b41b3b868b5", "name": "测试总结", "source": "c24d1b41b3b868b5.txt", "type": "text/plain", "size": 803}, {"uid": "e4a5b94ae5d216bf", "name": "test_completed", "source": "e4a5b94ae5d216bf.png", "type": "image/png", "size": 182034}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133330202, "stop": 1756133330204, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133330204, "stop": 1756133330446, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "7131ceb41c76400d", "name": "测试总结", "source": "7131ceb41c76400d.txt", "type": "text/plain", "size": 803}, {"uid": "a82141276b5ee3c5", "name": "test_completed", "source": "a82141276b5ee3c5.png", "type": "image/png", "size": 182035}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "43cbe1feafcdc65c", "name": "stdout", "source": "43cbe1feafcdc65c.txt", "type": "text/plain", "size": 15476}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133330449, "stop": 1756133330450, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133330451, "stop": 1756133331895, "duration": 1444}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_want_to_hear_a_joke"}, {"name": "subSuite", "value": "TestEllaIWantHearJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_want_to_hear_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8bd4026eebf09f09.json", "parameterValues": []}