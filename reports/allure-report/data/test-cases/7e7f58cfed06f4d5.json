{"uid": "7e7f58cfed06f4d5", "name": "测试start boosting phone能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_start_boosting_phone.TestEllaStartBoostingPhone#test_start_boosting_phone", "historyId": "bda3c1f82ca5c246958b5bf50db09a67", "time": {"start": 1756137813570, "stop": 1756137839841, "duration": 26271}, "description": "start boosting phone", "descriptionHtml": "<p>start boosting phone</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137800742, "stop": 1756137813568, "duration": 12826}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137813568, "stop": 1756137813569, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "start boosting phone", "status": "passed", "steps": [{"name": "执行命令: start boosting phone", "time": {"start": 1756137813570, "stop": 1756137839607, "duration": 26037}, "status": "passed", "steps": [{"name": "执行命令: start boosting phone", "time": {"start": 1756137813570, "stop": 1756137839350, "duration": 25780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137839350, "stop": 1756137839607, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "3e1b7c2697324505", "name": "测试总结", "source": "3e1b7c2697324505.txt", "type": "text/plain", "size": 276}, {"uid": "49236c8e05e84759", "name": "test_completed", "source": "49236c8e05e84759.png", "type": "image/png", "size": 172306}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756137839607, "stop": 1756137839609, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137839610, "stop": 1756137839840, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "9fdbded9dae07535", "name": "测试总结", "source": "9fdbded9dae07535.txt", "type": "text/plain", "size": 276}, {"uid": "21a50a398863a9ec", "name": "test_completed", "source": "21a50a398863a9ec.png", "type": "image/png", "size": 172306}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a01b6d6d1571bd6a", "name": "stdout", "source": "a01b6d6d1571bd6a.txt", "type": "text/plain", "size": 12450}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137839841, "stop": 1756137839841, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756137839843, "stop": 1756137841220, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_start_boosting_phone"}, {"name": "subSuite", "value": "TestEllaStartBoostingPhone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_start_boosting_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7e7f58cfed06f4d5.json", "parameterValues": []}