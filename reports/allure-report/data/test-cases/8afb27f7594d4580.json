{"uid": "8afb27f7594d4580", "name": "测试check model information返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_model_information.TestEllaCheckModelInformation#test_check_model_information", "historyId": "49ae31ee7fe8baa7f1604fd83d56bb68", "time": {"start": 1756130241079, "stop": 1756130267879, "duration": 26800}, "description": "验证check model information指令返回预期的不支持响应", "descriptionHtml": "<p>验证check model information指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130228367, "stop": 1756130241079, "duration": 12712}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130241079, "stop": 1756130241079, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证check model information指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check model information", "time": {"start": 1756130241080, "stop": 1756130267663, "duration": 26583}, "status": "passed", "steps": [{"name": "执行命令: check model information", "time": {"start": 1756130241080, "stop": 1756130267404, "duration": 26324}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130267405, "stop": 1756130267663, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "3b7f5627d1b2fbdf", "name": "测试总结", "source": "3b7f5627d1b2fbdf.txt", "type": "text/plain", "size": 264}, {"uid": "3ff914131c8f8e14", "name": "test_completed", "source": "3ff914131c8f8e14.png", "type": "image/png", "size": 169418}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130267663, "stop": 1756130267665, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130267665, "stop": 1756130267878, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "cf68e04e847914cd", "name": "测试总结", "source": "cf68e04e847914cd.txt", "type": "text/plain", "size": 264}, {"uid": "a8d9ee9642f6b868", "name": "test_completed", "source": "a8d9ee9642f6b868.png", "type": "image/png", "size": 169418}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5ee6aee0594a1cbf", "name": "stdout", "source": "5ee6aee0594a1cbf.txt", "type": "text/plain", "size": 12436}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130267879, "stop": 1756130267879, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130267880, "stop": 1756130269300, "duration": 1420}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_model_information"}, {"name": "subSuite", "value": "TestEllaCheckModelInformation"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_model_information"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8afb27f7594d4580.json", "parameterValues": []}