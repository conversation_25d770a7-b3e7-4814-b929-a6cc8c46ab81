{"uid": "d3d2b7eac8ab608a", "name": "测试document summary能正常执行", "fullName": "testcases.test_ella.self_function.test_document_summary.TestEllaDocumentSummary#test_document_summary", "historyId": "1ed60306ec6b3749ffacc2d410664db2", "time": {"start": 1756123000261, "stop": 1756123071807, "duration": 71546}, "description": "document summary", "descriptionHtml": "<p>document summary</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122987458, "stop": 1756123000260, "duration": 12802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123000260, "stop": 1756123000260, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "document summary", "status": "passed", "steps": [{"name": "执行命令: document summary", "time": {"start": 1756123000262, "stop": 1756123071521, "duration": 71259}, "status": "passed", "steps": [{"name": "执行命令: document summary", "time": {"start": 1756123000262, "stop": 1756123071290, "duration": 71028}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123071290, "stop": 1756123071521, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "10b2b7494f0f23", "name": "测试总结", "source": "10b2b7494f0f23.txt", "type": "text/plain", "size": 1322}, {"uid": "32757ff66b227886", "name": "test_completed", "source": "32757ff66b227886.png", "type": "image/png", "size": 335364}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123071521, "stop": 1756123071523, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123071523, "stop": 1756123071769, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "d241ed41128fd56a", "name": "测试总结", "source": "d241ed41128fd56a.txt", "type": "text/plain", "size": 1275}, {"uid": "82c47debf2402ba", "name": "test_completed", "source": "82c47debf2402ba.png", "type": "image/png", "size": 335758}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "538d55ae89745568", "name": "stdout", "source": "538d55ae89745568.txt", "type": "text/plain", "size": 21631}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123071808, "stop": 1756123071808, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123071810, "stop": 1756123073240, "duration": 1430}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_document_summary"}, {"name": "subSuite", "value": "TestEllaDocumentSummary"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_document_summary"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "d3d2b7eac8ab608a.json", "parameterValues": []}