{"uid": "a919cd142203fec2", "name": "测试disable network enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement.TestEllaDisableNetworkEnhancement#test_disable_network_enhancement", "historyId": "3d685d9ca6a0d7795be3c96921595318", "time": {"start": 1756131034397, "stop": 1756131061027, "duration": 26630}, "description": "验证disable network enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable network enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131021505, "stop": 1756131034395, "duration": 12890}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131034395, "stop": 1756131034395, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable network enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1756131034397, "stop": 1756131060813, "duration": 26416}, "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1756131034397, "stop": 1756131060549, "duration": 26152}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131060549, "stop": 1756131060813, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "9aaa7cb70529e49f", "name": "测试总结", "source": "9aaa7cb70529e49f.txt", "type": "text/plain", "size": 272}, {"uid": "cafc19db8968f468", "name": "test_completed", "source": "cafc19db8968f468.png", "type": "image/png", "size": 190844}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131060813, "stop": 1756131060815, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131060816, "stop": 1756131061027, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "6f9be0decbe53d95", "name": "测试总结", "source": "6f9be0decbe53d95.txt", "type": "text/plain", "size": 272}, {"uid": "f69a67dd68b28791", "name": "test_completed", "source": "f69a67dd68b28791.png", "type": "image/png", "size": 190844}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "830cef434a15c383", "name": "stdout", "source": "830cef434a15c383.txt", "type": "text/plain", "size": 12480}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131061028, "stop": 1756131061028, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131061029, "stop": 1756131062387, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaDisableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a919cd142203fec2.json", "parameterValues": []}