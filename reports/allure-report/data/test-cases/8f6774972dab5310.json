{"uid": "8f6774972dab5310", "name": "测试turn on brightness to 80能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_brightness_to_80.TestEllaTurnBrightness#test_turn_on_brightness_to", "historyId": "8971377f4371d1ea3384cde4ed276db1", "time": {"start": 1756127861459, "stop": 1756127888357, "duration": 26898}, "description": "turn on brightness to 80", "descriptionHtml": "<p>turn on brightness to 80</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127848867, "stop": 1756127861458, "duration": 12591}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127861458, "stop": 1756127861458, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn on brightness to 80", "status": "passed", "steps": [{"name": "执行命令: turn on brightness to 80", "time": {"start": 1756127861459, "stop": 1756127888111, "duration": 26652}, "status": "passed", "steps": [{"name": "执行命令: turn on brightness to 80", "time": {"start": 1756127861460, "stop": 1756127887882, "duration": 26422}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127887882, "stop": 1756127888110, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "236a7c724ce9cf4c", "name": "测试总结", "source": "236a7c724ce9cf4c.txt", "type": "text/plain", "size": 220}, {"uid": "b2d8e8218dad9bd9", "name": "test_completed", "source": "b2d8e8218dad9bd9.png", "type": "image/png", "size": 157586}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127888111, "stop": 1756127888113, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127888113, "stop": 1756127888113, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127888113, "stop": 1756127888357, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "14ca2238b4f0e06a", "name": "测试总结", "source": "14ca2238b4f0e06a.txt", "type": "text/plain", "size": 220}, {"uid": "3fc4a29d6337cfc5", "name": "test_completed", "source": "3fc4a29d6337cfc5.png", "type": "image/png", "size": 157586}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bc02c686ab643207", "name": "stdout", "source": "bc02c686ab643207.txt", "type": "text/plain", "size": 13161}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127888360, "stop": 1756127888360, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127888362, "stop": 1756127889759, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_brightness_to_80"}, {"name": "subSuite", "value": "TestEllaTurnBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_brightness_to_80"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8f6774972dab5310.json", "parameterValues": []}