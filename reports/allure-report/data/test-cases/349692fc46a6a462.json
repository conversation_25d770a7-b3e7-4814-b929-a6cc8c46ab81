{"uid": "349692fc46a6a462", "name": "测试record audio for 5 seconds能正常执行", "fullName": "testcases.test_ella.component_coupling.test_record_audio_for_seconds.TestEllaRecordAudioSeconds#test_record_audio_for_seconds", "historyId": "17788b061637289a04732fe5840218ee", "time": {"start": 1756118600414, "stop": 1756118628159, "duration": 27745}, "description": "record audio for 5 seconds", "descriptionHtml": "<p>record audio for 5 seconds</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118587551, "stop": 1756118600412, "duration": 12861}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118600412, "stop": 1756118600413, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "record audio for 5 seconds", "status": "passed", "steps": [{"name": "执行命令: record audio for 5 seconds", "time": {"start": 1756118600414, "stop": 1756118627936, "duration": 27522}, "status": "passed", "steps": [{"name": "执行命令: record audio for 5 seconds", "time": {"start": 1756118600414, "stop": 1756118627647, "duration": 27233}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118627647, "stop": 1756118627935, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "e97ce91cddf3df35", "name": "测试总结", "source": "e97ce91cddf3df35.txt", "type": "text/plain", "size": 715}, {"uid": "eaf88a9eef3d320c", "name": "test_completed", "source": "eaf88a9eef3d320c.png", "type": "image/png", "size": 183429}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118627936, "stop": 1756118627936, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118627936, "stop": 1756118628159, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "c71095cfe5dcaf76", "name": "测试总结", "source": "c71095cfe5dcaf76.txt", "type": "text/plain", "size": 715}, {"uid": "a433596edefda6de", "name": "test_completed", "source": "a433596edefda6de.png", "type": "image/png", "size": 183077}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8b9c961c97d7a558", "name": "stdout", "source": "8b9c961c97d7a558.txt", "type": "text/plain", "size": 15785}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118628161, "stop": 1756118628161, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118628162, "stop": 1756118629546, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_record_audio_for_seconds"}, {"name": "subSuite", "value": "TestEllaRecordAudioSeconds"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_record_audio_for_seconds"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "349692fc46a6a462.json", "parameterValues": []}