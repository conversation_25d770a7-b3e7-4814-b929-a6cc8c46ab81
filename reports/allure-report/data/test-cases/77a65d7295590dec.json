{"uid": "77a65d7295590dec", "name": "测试turn up ring volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_up_ring_volume.TestEllaTurnUpRingVolume#test_turn_up_ring_volume", "historyId": "7b90ee3ed7bdd2837a37215aac61cfd9", "time": {"start": 1756128509580, "stop": 1756128536518, "duration": 26938}, "description": "turn up ring volume", "descriptionHtml": "<p>turn up ring volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128496473, "stop": 1756128509578, "duration": 13105}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128509578, "stop": 1756128509579, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn up ring volume", "status": "passed", "steps": [{"name": "执行命令: turn up ring volume", "time": {"start": 1756128509580, "stop": 1756128536262, "duration": 26682}, "status": "passed", "steps": [{"name": "执行命令: turn up ring volume", "time": {"start": 1756128509582, "stop": 1756128535986, "duration": 26404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128535986, "stop": 1756128536261, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "be57eec14440f62b", "name": "测试总结", "source": "be57eec14440f62b.txt", "type": "text/plain", "size": 298}, {"uid": "26d97bc75defd285", "name": "test_completed", "source": "26d97bc75defd285.png", "type": "image/png", "size": 187049}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128536262, "stop": 1756128536263, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证ring volume提升", "time": {"start": 1756128536263, "stop": 1756128536263, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128536263, "stop": 1756128536518, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "d8e05984df16cb02", "name": "测试总结", "source": "d8e05984df16cb02.txt", "type": "text/plain", "size": 298}, {"uid": "72563673032aae78", "name": "test_completed", "source": "72563673032aae78.png", "type": "image/png", "size": 187049}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e5dcf16b58dc5a23", "name": "stdout", "source": "e5dcf16b58dc5a23.txt", "type": "text/plain", "size": 13808}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128536519, "stop": 1756128536519, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128536519, "stop": 1756128537896, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_ring_volume"}, {"name": "subSuite", "value": "TestEllaTurnUpRingVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_ring_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "77a65d7295590dec.json", "parameterValues": []}