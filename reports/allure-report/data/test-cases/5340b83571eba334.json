{"uid": "5340b83571eba334", "name": "测试what's your name？能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_your_name.TestEllaWhatSYourName#test_what_s_your_name", "historyId": "c0d6ce0b7c5e41242c01a6e0c0186608", "time": {"start": 1756122464841, "stop": 1756122492409, "duration": 27568}, "description": "what's your name？", "descriptionHtml": "<p>what's your name？</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122451341, "stop": 1756122464839, "duration": 13498}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122464840, "stop": 1756122464840, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what's your name？", "status": "passed", "steps": [{"name": "执行命令: what's your name？", "time": {"start": 1756122464841, "stop": 1756122492104, "duration": 27263}, "status": "passed", "steps": [{"name": "执行命令: what's your name？", "time": {"start": 1756122464842, "stop": 1756122491841, "duration": 26999}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122491841, "stop": 1756122492103, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "60fc65c8949475ca", "name": "测试总结", "source": "60fc65c8949475ca.txt", "type": "text/plain", "size": 689}, {"uid": "c78ba2b0d47ada1b", "name": "test_completed", "source": "c78ba2b0d47ada1b.png", "type": "image/png", "size": 182673}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122492104, "stop": 1756122492111, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122492111, "stop": 1756122492407, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "610f0b9e92dcd90", "name": "测试总结", "source": "610f0b9e92dcd90.txt", "type": "text/plain", "size": 689}, {"uid": "ef09f3ef48e064ae", "name": "test_completed", "source": "ef09f3ef48e064ae.png", "type": "image/png", "size": 182324}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3803609c9f14aff0", "name": "stdout", "source": "3803609c9f14aff0.txt", "type": "text/plain", "size": 15228}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122492410, "stop": 1756122492410, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122492416, "stop": 1756122493874, "duration": 1458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_your_name"}, {"name": "subSuite", "value": "TestEllaWhatSYourName"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_your_name"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5340b83571eba334.json", "parameterValues": []}