{"uid": "24a9f258e0d1ca84", "name": "测试switch to power saving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode.TestEllaSwitchPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "a0efcebc4cee6024e690bd290b4f3fbb", "time": {"start": 1756138106956, "stop": 1756138133131, "duration": 26175}, "description": "验证switch to power saving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to power saving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138094057, "stop": 1756138106955, "duration": 12898}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138106955, "stop": 1756138106955, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证switch to power saving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1756138106956, "stop": 1756138132896, "duration": 25940}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1756138106956, "stop": 1756138132608, "duration": 25652}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138132608, "stop": 1756138132896, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "62c7b56da91bce89", "name": "测试总结", "source": "62c7b56da91bce89.txt", "type": "text/plain", "size": 360}, {"uid": "6e40de84adb3a278", "name": "test_completed", "source": "6e40de84adb3a278.png", "type": "image/png", "size": 190180}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138132896, "stop": 1756138132898, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138132898, "stop": 1756138133130, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "bb3fb918dbe41504", "name": "测试总结", "source": "bb3fb918dbe41504.txt", "type": "text/plain", "size": 360}, {"uid": "9691e5896419f7e4", "name": "test_completed", "source": "9691e5896419f7e4.png", "type": "image/png", "size": 190180}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "5d29694ec63ef128", "name": "stdout", "source": "5d29694ec63ef128.txt", "type": "text/plain", "size": 13191}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138133131, "stop": 1756138133131, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138133137, "stop": 1756138134521, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "24a9f258e0d1ca84.json", "parameterValues": []}