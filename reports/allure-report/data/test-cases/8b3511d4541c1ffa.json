{"uid": "8b3511d4541c1ffa", "name": "测试call mom", "fullName": "testcases.test_ella.unsupported_commands.test_call_mom.TestEllaOpenPlayPoliticalNews#test_call_mom", "historyId": "1d703b69183fa98eda60c159840c4ffd", "time": {"start": 1756129819710, "stop": 1756129819710, "duration": 0}, "description": "测试call mom指令", "descriptionHtml": "<p>测试call mom指令</p>\n", "status": "skipped", "statusMessage": "Skipped: call mom命令需要先添加联系人，才能拨打电话，无法通用化", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\unsupported_commands\\\\test_call_mom.py', 14, 'Skipped: call mom命令需要先添加联系人，才能拨打电话，无法通用化')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129832489, "stop": 1756129832489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129866405, "stop": 1756129866639, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "caa14600b439cdf3", "name": "失败截图-TestEllaCallNumberWhatsapp", "source": "caa14600b439cdf3.png", "type": "image/png", "size": 186105}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "@pytest.mark.skip(reason='call mom命令需要先添加联系人，才能拨打电话，无法通用化')"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_call_mom"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_call_mom"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke", "@pytest.mark.skip(reason='call mom命令需要先添加联系人，才能拨打电话，无法通用化')"]}, "source": "8b3511d4541c1ffa.json", "parameterValues": []}