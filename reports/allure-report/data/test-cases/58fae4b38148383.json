{"uid": "58fae4b38148383", "name": "测试play video", "fullName": "testcases.test_ella.unsupported_commands.test_play_video.TestEllaOpenPlayPoliticalNews#test_play_video", "historyId": "05cfb3e6f186373be53bb1a7166ac69f", "time": {"start": 1756135336925, "stop": 1756135369454, "duration": 32529}, "description": "测试play video指令", "descriptionHtml": "<p>测试play video指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135324032, "stop": 1756135336923, "duration": 12891}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135336923, "stop": 1756135336923, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play video指令", "status": "passed", "steps": [{"name": "执行命令: play video", "time": {"start": 1756135336925, "stop": 1756135369191, "duration": 32266}, "status": "passed", "steps": [{"name": "执行命令: play video", "time": {"start": 1756135336925, "stop": 1756135368934, "duration": 32009}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135368934, "stop": 1756135369190, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "70ae543d04c36e6e", "name": "测试总结", "source": "70ae543d04c36e6e.txt", "type": "text/plain", "size": 333}, {"uid": "846e436bdbc43e0", "name": "test_completed", "source": "846e436bdbc43e0.png", "type": "image/png", "size": 169979}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135369191, "stop": 1756135369193, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证youtube已打开", "time": {"start": 1756135369193, "stop": 1756135369193, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135369193, "stop": 1756135369453, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "4a45ea44715dfa65", "name": "测试总结", "source": "4a45ea44715dfa65.txt", "type": "text/plain", "size": 333}, {"uid": "91ceb98ace954211", "name": "test_completed", "source": "91ceb98ace954211.png", "type": "image/png", "size": 169826}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "45a8034c2cfd9ebc", "name": "stdout", "source": "45a8034c2cfd9ebc.txt", "type": "text/plain", "size": 15246}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135369455, "stop": 1756135369455, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135369456, "stop": 1756135370950, "duration": 1494}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_video"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_video"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "58fae4b38148383.json", "parameterValues": []}