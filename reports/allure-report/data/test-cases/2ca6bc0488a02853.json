{"uid": "2ca6bc0488a02853", "name": "测试play music on visha", "fullName": "testcases.test_ella.dialogue.test_play_music_on_visha.TestEllaOpenPlayPoliticalNews#test_play_music_on_visha", "historyId": "228f8713a07d6ddbb8729f2f567c21d0", "time": {"start": 1756121015745, "stop": 1756121058294, "duration": 42549}, "description": "测试play music on visha指令", "descriptionHtml": "<p>测试play music on visha指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121002731, "stop": 1756121015744, "duration": 13013}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121015744, "stop": 1756121015744, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music on visha指令", "status": "passed", "steps": [{"name": "执行命令: play music on visha", "time": {"start": 1756121015745, "stop": 1756121058047, "duration": 42302}, "status": "passed", "steps": [{"name": "执行命令: play music on visha", "time": {"start": 1756121015745, "stop": 1756121057754, "duration": 42009}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121057754, "stop": 1756121058046, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "c63f43cf730fee85", "name": "测试总结", "source": "c63f43cf730fee85.txt", "type": "text/plain", "size": 612}, {"uid": "93c14990961b3031", "name": "test_completed", "source": "93c14990961b3031.png", "type": "image/png", "size": 171593}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121058047, "stop": 1756121058048, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121058048, "stop": 1756121058293, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "820d535a3b69720c", "name": "测试总结", "source": "820d535a3b69720c.txt", "type": "text/plain", "size": 612}, {"uid": "ea44886c9e625edd", "name": "test_completed", "source": "ea44886c9e625edd.png", "type": "image/png", "size": 171539}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4b2003c843e5b250", "name": "stdout", "source": "4b2003c843e5b250.txt", "type": "text/plain", "size": 15503}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121058295, "stop": 1756121058295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121058297, "stop": 1756121059685, "duration": 1388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_on_visha"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_on_visha"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2ca6bc0488a02853.json", "parameterValues": []}