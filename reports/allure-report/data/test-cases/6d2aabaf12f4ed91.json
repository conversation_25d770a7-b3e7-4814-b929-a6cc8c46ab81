{"uid": "6d2aabaf12f4ed91", "name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up.TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp#test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "historyId": "3f0254c16b7bc20d18094d502f49138a", "time": {"start": 1756133159039, "stop": 1756133186666, "duration": 27627}, "description": "I think the screen is a bit dark now. Could you please help me brighten it up?", "descriptionHtml": "<p>I think the screen is a bit dark now. Could you please help me brighten it up?</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133146122, "stop": 1756133159038, "duration": 12916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133159038, "stop": 1756133159038, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "I think the screen is a bit dark now. Could you please help me brighten it up?", "status": "passed", "steps": [{"name": "执行命令: I think the screen is a bit dark now. Could you please help me brighten it up?", "time": {"start": 1756133159039, "stop": 1756133186420, "duration": 27381}, "status": "passed", "steps": [{"name": "执行命令: I think the screen is a bit dark now. Could you please help me brighten it up?", "time": {"start": 1756133159039, "stop": 1756133186149, "duration": 27110}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133186149, "stop": 1756133186420, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "8a01485ec5c77a9e", "name": "测试总结", "source": "8a01485ec5c77a9e.txt", "type": "text/plain", "size": 330}, {"uid": "a96498970bd2460", "name": "test_completed", "source": "a96498970bd2460.png", "type": "image/png", "size": 169017}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133186420, "stop": 1756133186422, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756133186422, "stop": 1756133186422, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133186422, "stop": 1756133186666, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "99d8c215558b0d52", "name": "测试总结", "source": "99d8c215558b0d52.txt", "type": "text/plain", "size": 330}, {"uid": "e6e69e9a820e20b7", "name": "test_completed", "source": "e6e69e9a820e20b7.png", "type": "image/png", "size": 168686}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cbfe075b522af031", "name": "stdout", "source": "cbfe075b522af031.txt", "type": "text/plain", "size": 13885}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133186667, "stop": 1756133186668, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133186668, "stop": 1756133188081, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up"}, {"name": "subSuite", "value": "TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6d2aabaf12f4ed91.json", "parameterValues": []}