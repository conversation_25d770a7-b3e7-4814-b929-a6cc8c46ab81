{"uid": "ad61eea8b0323bfb", "name": "测试previous music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_previous_music.TestEllaPreviousMusic#test_previous_music", "historyId": "0eb27e9cfa9ed24b7ee5e6be6e495cb7", "time": {"start": 1756118560310, "stop": 1756118586172, "duration": 25862}, "description": "previous music", "descriptionHtml": "<p>previous music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118547469, "stop": 1756118560309, "duration": 12840}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118560309, "stop": 1756118560309, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "previous music", "status": "passed", "steps": [{"name": "执行命令: previous music", "time": {"start": 1756118560310, "stop": 1756118585926, "duration": 25616}, "status": "passed", "steps": [{"name": "执行命令: previous music", "time": {"start": 1756118560310, "stop": 1756118585662, "duration": 25352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118585662, "stop": 1756118585926, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "f8f8750888ce3307", "name": "测试总结", "source": "f8f8750888ce3307.txt", "type": "text/plain", "size": 294}, {"uid": "f845f740569c2ab7", "name": "test_completed", "source": "f845f740569c2ab7.png", "type": "image/png", "size": 176805}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118585926, "stop": 1756118585931, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118585931, "stop": 1756118586172, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "b48a60abf082fdb8", "name": "测试总结", "source": "b48a60abf082fdb8.txt", "type": "text/plain", "size": 294}, {"uid": "1a4e1b0ce890951d", "name": "test_completed", "source": "1a4e1b0ce890951d.png", "type": "image/png", "size": 176805}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "81c20be5c365d035", "name": "stdout", "source": "81c20be5c365d035.txt", "type": "text/plain", "size": 12746}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118586173, "stop": 1756118586173, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118586174, "stop": 1756118587546, "duration": 1372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_previous_music"}, {"name": "subSuite", "value": "TestEllaPreviousMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_previous_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ad61eea8b0323bfb.json", "parameterValues": []}