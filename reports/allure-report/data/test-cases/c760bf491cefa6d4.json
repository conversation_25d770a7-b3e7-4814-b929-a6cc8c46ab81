{"uid": "c760bf491cefa6d4", "name": "测试order a burger能正常执行", "fullName": "testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise#test_order_a_burger", "historyId": "09f397887d36f3ef1e86e3c78272f1d6", "time": {"start": 1756129178250, "stop": 1756129203871, "duration": 25621}, "description": "order a burger", "descriptionHtml": "<p>order a burger</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129165346, "stop": 1756129178248, "duration": 12902}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129178248, "stop": 1756129178248, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "order a burger", "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1756129178250, "stop": 1756129203659, "duration": 25409}, "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1756129178250, "stop": 1756129203407, "duration": 25157}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129203407, "stop": 1756129203659, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "131d534b0ae77f26", "name": "测试总结", "source": "131d534b0ae77f26.txt", "type": "text/plain", "size": 302}, {"uid": "1c59b29cd50afd9", "name": "test_completed", "source": "1c59b29cd50afd9.png", "type": "image/png", "size": 186143}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756129203659, "stop": 1756129203661, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129203661, "stop": 1756129203869, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "51b3d9b3c0e17ca4", "name": "测试总结", "source": "51b3d9b3c0e17ca4.txt", "type": "text/plain", "size": 302}, {"uid": "bdc5c02a5977d017", "name": "test_completed", "source": "bdc5c02a5977d017.png", "type": "image/png", "size": 186143}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "cbdaa94ef50823dd", "name": "stdout", "source": "cbdaa94ef50823dd.txt", "type": "text/plain", "size": 13029}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129203871, "stop": 1756129203871, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129203872, "stop": 1756129205296, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_burger"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c760bf491cefa6d4.json", "parameterValues": []}