{"uid": "7bc299d2cede4cef", "name": "测试i want to watch fireworks能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks.TestEllaIWantWatchFireworks#test_i_want_to_watch_fireworks", "historyId": "4ae696581fe41611547bc10ddba4f526", "time": {"start": 1756120334194, "stop": 1756120363163, "duration": 28969}, "description": "i want to watch fireworks", "descriptionHtml": "<p>i want to watch fireworks</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120321199, "stop": 1756120334192, "duration": 12993}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120334192, "stop": 1756120334192, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "i want to watch fireworks", "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1756120334194, "stop": 1756120362924, "duration": 28730}, "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1756120334194, "stop": 1756120362644, "duration": 28450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120362644, "stop": 1756120362923, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "f7da01d4df00ae5f", "name": "测试总结", "source": "f7da01d4df00ae5f.txt", "type": "text/plain", "size": 311}, {"uid": "851bf40226841ca6", "name": "test_completed", "source": "851bf40226841ca6.png", "type": "image/png", "size": 185131}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120362924, "stop": 1756120362928, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120362928, "stop": 1756120363161, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "b70ff9830cf56281", "name": "测试总结", "source": "b70ff9830cf56281.txt", "type": "text/plain", "size": 311}, {"uid": "9d1d7ed83089a05e", "name": "test_completed", "source": "9d1d7ed83089a05e.png", "type": "image/png", "size": 185279}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c13c3932c964ff00", "name": "stdout", "source": "c13c3932c964ff00.txt", "type": "text/plain", "size": 12817}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120363164, "stop": 1756120363164, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120363166, "stop": 1756120364542, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_watch_fireworks"}, {"name": "subSuite", "value": "TestEllaIWantWatchFireworks"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7bc299d2cede4cef.json", "parameterValues": []}