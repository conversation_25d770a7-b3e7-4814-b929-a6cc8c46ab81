{"uid": "67908d4b5793b692", "name": "测试enable accelerate dialogue返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue.TestEllaEnableAccelerateDialogue#test_enable_accelerate_dialogue", "historyId": "0e5513d569c7270e4332e484218ae36b", "time": {"start": 1756131423744, "stop": 1756131450031, "duration": 26287}, "description": "验证enable accelerate dialogue指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable accelerate dialogue指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131411059, "stop": 1756131423742, "duration": 12683}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131423743, "stop": 1756131423743, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable accelerate dialogue指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable accelerate dialogue", "time": {"start": 1756131423744, "stop": 1756131449802, "duration": 26058}, "status": "passed", "steps": [{"name": "执行命令: enable accelerate dialogue", "time": {"start": 1756131423744, "stop": 1756131449516, "duration": 25772}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131449516, "stop": 1756131449801, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "dc289ca3b6d9940c", "name": "测试总结", "source": "dc289ca3b6d9940c.txt", "type": "text/plain", "size": 347}, {"uid": "bffaf4052045df32", "name": "test_completed", "source": "bffaf4052045df32.png", "type": "image/png", "size": 194092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131449802, "stop": 1756131449804, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131449804, "stop": 1756131450030, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "ee97204f6984168d", "name": "测试总结", "source": "ee97204f6984168d.txt", "type": "text/plain", "size": 347}, {"uid": "41b6b9db5d4da3c2", "name": "test_completed", "source": "41b6b9db5d4da3c2.png", "type": "image/png", "size": 194092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "22eaaf1c3ea22f5d", "name": "stdout", "source": "22eaaf1c3ea22f5d.txt", "type": "text/plain", "size": 12653}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131450031, "stop": 1756131450031, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131450032, "stop": 1756131451404, "duration": 1372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaEnableAccelerateDialogue"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "67908d4b5793b692.json", "parameterValues": []}