{"uid": "85f6fe10a44367b5", "name": "测试help me write an thanks letter能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_letter.TestEllaHelpMeWriteAnThanksLetter#test_help_me_write_an_thanks_letter", "historyId": "bfddb3863bb9971cace5dae92df6977d", "time": {"start": 1756133026315, "stop": 1756133054308, "duration": 27993}, "description": "help me write an thanks letter", "descriptionHtml": "<p>help me write an thanks letter</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133013495, "stop": 1756133026313, "duration": 12818}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133026314, "stop": 1756133026314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "help me write an thanks letter", "status": "passed", "steps": [{"name": "执行命令: help me write an thanks letter", "time": {"start": 1756133026315, "stop": 1756133054060, "duration": 27745}, "status": "passed", "steps": [{"name": "执行命令: help me write an thanks letter", "time": {"start": 1756133026315, "stop": 1756133053803, "duration": 27488}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133053803, "stop": 1756133054059, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "f94bc37a977b5410", "name": "测试总结", "source": "f94bc37a977b5410.txt", "type": "text/plain", "size": 1135}, {"uid": "aa2f817b3960505a", "name": "test_completed", "source": "aa2f817b3960505a.png", "type": "image/png", "size": 281661}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133054060, "stop": 1756133054061, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133054061, "stop": 1756133054307, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "3879444a49c24510", "name": "测试总结", "source": "3879444a49c24510.txt", "type": "text/plain", "size": 1135}, {"uid": "81c2d52d6faaebee", "name": "test_completed", "source": "81c2d52d6faaebee.png", "type": "image/png", "size": 281364}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "59581472b6bfc7f", "name": "stdout", "source": "59581472b6bfc7f.txt", "type": "text/plain", "size": 16214}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133054309, "stop": 1756133054309, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133054313, "stop": 1756133055656, "duration": 1343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_thanks_letter"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnThanksLetter"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_letter"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "85f6fe10a44367b5.json", "parameterValues": []}