{"uid": "149c51f0be1411a4", "name": "测试set date & time返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime#test_set_date_time", "historyId": "92e2909ea81e82011e43342b4fc06c3b", "time": {"start": 1756136350699, "stop": 1756136377478, "duration": 26779}, "description": "验证set date & time指令返回预期的不支持响应", "descriptionHtml": "<p>验证set date &amp; time指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set date & time', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime object at 0x000002920569ED10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097F7E10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_date_time(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set date & time', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_date_time.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136337677, "stop": 1756136350697, "duration": 13020}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136350697, "stop": 1756136350697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set date & time指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set date & time', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime object at 0x000002920569ED10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097F7E10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_date_time(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set date & time', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_date_time.py:33: AssertionError", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1756136350699, "stop": 1756136377473, "duration": 26774}, "status": "passed", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1756136350699, "stop": 1756136377193, "duration": 26494}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136377193, "stop": 1756136377472, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "7001f6d3079b03b6", "name": "测试总结", "source": "7001f6d3079b03b6.txt", "type": "text/plain", "size": 248}, {"uid": "9d94001d5e405d6c", "name": "test_completed", "source": "9d94001d5e405d6c.png", "type": "image/png", "size": 170765}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136377473, "stop": 1756136377476, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set date & time', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_date_time.py\", line 33, in test_set_date_time\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "61ef83a93b5ce0f", "name": "stdout", "source": "61ef83a93b5ce0f.txt", "type": "text/plain", "size": 13043}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136377482, "stop": 1756136377731, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "abb554d80bddd8c2", "name": "失败截图-TestEllaSetDateTime", "source": "abb554d80bddd8c2.png", "type": "image/png", "size": 170765}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136377733, "stop": 1756136379201, "duration": 1468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_date_time"}, {"name": "subSuite", "value": "TestEllaSetDateTime"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_date_time"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "149c51f0be1411a4.json", "parameterValues": []}