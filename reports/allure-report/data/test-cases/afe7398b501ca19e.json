{"uid": "afe7398b501ca19e", "name": "测试i am your voice assistant", "fullName": "testcases.test_ella.unsupported_commands.test_i_am_your_voice_assistant.TestEllaOpenPlayPoliticalNews#test_i_am_your_voice_assistant", "historyId": "c5debd3414c81b3cc4349236d4020867", "time": {"start": 1756133116819, "stop": 1756133144652, "duration": 27833}, "description": "测试i am your voice assistant指令", "descriptionHtml": "<p>测试i am your voice assistant指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133103617, "stop": 1756133116818, "duration": 13201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133116818, "stop": 1756133116818, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试i am your voice assistant指令", "status": "passed", "steps": [{"name": "执行命令: i am your voice assistant", "time": {"start": 1756133116820, "stop": 1756133144408, "duration": 27588}, "status": "passed", "steps": [{"name": "执行命令: i am your voice assistant", "time": {"start": 1756133116820, "stop": 1756133144130, "duration": 27310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133144130, "stop": 1756133144407, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "b16c2c81de8eeb3f", "name": "测试总结", "source": "b16c2c81de8eeb3f.txt", "type": "text/plain", "size": 831}, {"uid": "110bc68c07d5ec07", "name": "test_completed", "source": "110bc68c07d5ec07.png", "type": "image/png", "size": 195132}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133144408, "stop": 1756133144410, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133144410, "stop": 1756133144651, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "7704cd20c5545e45", "name": "测试总结", "source": "7704cd20c5545e45.txt", "type": "text/plain", "size": 831}, {"uid": "f75c7b0f6b6d031a", "name": "test_completed", "source": "f75c7b0f6b6d031a.png", "type": "image/png", "size": 194880}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b19295c56bafc9e0", "name": "stdout", "source": "b19295c56bafc9e0.txt", "type": "text/plain", "size": 15819}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133144652, "stop": 1756133144652, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133144655, "stop": 1756133146115, "duration": 1460}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_am_your_voice_assistant"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_am_your_voice_assistant"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "afe7398b501ca19e.json", "parameterValues": []}