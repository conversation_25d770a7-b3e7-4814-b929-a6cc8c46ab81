{"uid": "350926f8f915b19", "name": "测试set compatibility mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode.TestEllaSetCompatibilityMode#test_set_compatibility_mode", "historyId": "aecf9a6f67cd29766190cbcc133448d2", "time": {"start": 1756136228787, "stop": 1756136254898, "duration": 26111}, "description": "验证set compatibility mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证set compatibility mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136215704, "stop": 1756136228786, "duration": 13082}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136228786, "stop": 1756136228786, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set compatibility mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "time": {"start": 1756136228787, "stop": 1756136254664, "duration": 25877}, "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "time": {"start": 1756136228787, "stop": 1756136254375, "duration": 25588}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136254375, "stop": 1756136254663, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "6126f3f7030f5c60", "name": "测试总结", "source": "6126f3f7030f5c60.txt", "type": "text/plain", "size": 345}, {"uid": "f4c03ed238591a2e", "name": "test_completed", "source": "f4c03ed238591a2e.png", "type": "image/png", "size": 193202}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136254664, "stop": 1756136254665, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136254665, "stop": 1756136254897, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "792be726b5f3b09b", "name": "测试总结", "source": "792be726b5f3b09b.txt", "type": "text/plain", "size": 345}, {"uid": "45095d9a7f04f9d3", "name": "test_completed", "source": "45095d9a7f04f9d3.png", "type": "image/png", "size": 193202}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "49507b8e2cf2e143", "name": "stdout", "source": "49507b8e2cf2e143.txt", "type": "text/plain", "size": 12627}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136254899, "stop": 1756136254899, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136254901, "stop": 1756136256365, "duration": 1464}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_compatibility_mode"}, {"name": "subSuite", "value": "TestEllaSetCompatibilityMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "350926f8f915b19.json", "parameterValues": []}