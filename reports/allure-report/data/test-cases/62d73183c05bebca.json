{"uid": "62d73183c05bebca", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings.TestEllaJumpAiWallpaperGeneratorSettings#test_jump_to_ai_wallpaper_generator_settings", "historyId": "f1bf796cd6804ca9b19a3e3f949a04ba", "time": {"start": 1756133581922, "stop": 1756133608556, "duration": 26634}, "description": "验证jump to ai wallpaper generator settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to ai wallpaper generator settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to ai wallpaper generator settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings.TestEllaJumpAiWallpaperGeneratorSettings object at 0x00000292053706D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209602410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_ai_wallpaper_generator_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to ai wallpaper generator settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_ai_wallpaper_generator_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133564370, "stop": 1756133581919, "duration": 17549}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133581919, "stop": 1756133581919, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to ai wallpaper generator settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to ai wallpaper generator settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings.TestEllaJumpAiWallpaperGeneratorSettings object at 0x00000292053706D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209602410>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_ai_wallpaper_generator_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to ai wallpaper generator settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_ai_wallpaper_generator_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "time": {"start": 1756133581922, "stop": 1756133608549, "duration": 26627}, "status": "passed", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "time": {"start": 1756133581922, "stop": 1756133608257, "duration": 26335}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133608257, "stop": 1756133608549, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "7c8196a56da8ad3", "name": "测试总结", "source": "7c8196a56da8ad3.txt", "type": "text/plain", "size": 296}, {"uid": "a4a9df9abaf3178e", "name": "test_completed", "source": "a4a9df9abaf3178e.png", "type": "image/png", "size": 178637}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133608549, "stop": 1756133608554, "duration": 5}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to ai wallpaper generator settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_ai_wallpaper_generator_settings.py\", line 33, in test_jump_to_ai_wallpaper_generator_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9e4f6518ef63c860", "name": "stdout", "source": "9e4f6518ef63c860.txt", "type": "text/plain", "size": 13878}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133608560, "stop": 1756133608815, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "44053e897a6c2100", "name": "失败截图-TestEllaJumpAiWallpaperGeneratorSettings", "source": "44053e897a6c2100.png", "type": "image/png", "size": 178647}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133608822, "stop": 1756133610234, "duration": 1412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_ai_wallpaper_generator_settings"}, {"name": "subSuite", "value": "TestEllaJumpAiWallpaperGeneratorSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "62d73183c05bebca.json", "parameterValues": []}