{"uid": "fd47468e3e0f17d7", "name": "测试it wears a yellow leather collar", "fullName": "testcases.test_ella.unsupported_commands.test_it_wears_a_yellow_leather_collar.TestEllaOpenPlayPoliticalNews#test_it_wears_a_yellow_leather_collar", "historyId": "76a63bd8a63882a3a1ada32e63f1c971", "time": {"start": 1756133484652, "stop": 1756133512978, "duration": 28326}, "description": "测试it wears a yellow leather collar指令", "descriptionHtml": "<p>测试it wears a yellow leather collar指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133471809, "stop": 1756133484650, "duration": 12841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133484650, "stop": 1756133484650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试it wears a yellow leather collar指令", "status": "passed", "steps": [{"name": "执行命令: it wears a yellow leather collar", "time": {"start": 1756133484652, "stop": 1756133512731, "duration": 28079}, "status": "passed", "steps": [{"name": "执行命令: it wears a yellow leather collar", "time": {"start": 1756133484652, "stop": 1756133512475, "duration": 27823}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133512475, "stop": 1756133512731, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "ba3ec24db2d8aa2e", "name": "测试总结", "source": "ba3ec24db2d8aa2e.txt", "type": "text/plain", "size": 1033}, {"uid": "3f51cb26a364be41", "name": "test_completed", "source": "3f51cb26a364be41.png", "type": "image/png", "size": 204362}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133512731, "stop": 1756133512733, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133512733, "stop": 1756133512976, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "cbe55dd8c188ff09", "name": "测试总结", "source": "cbe55dd8c188ff09.txt", "type": "text/plain", "size": 1033}, {"uid": "10297e6c1e38a4f6", "name": "test_completed", "source": "10297e6c1e38a4f6.png", "type": "image/png", "size": 204610}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c20f0fc2070e6fd0", "name": "stdout", "source": "c20f0fc2070e6fd0.txt", "type": "text/plain", "size": 15909}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133512980, "stop": 1756133512980, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133512982, "stop": 1756133514485, "duration": 1503}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_it_wears_a_yellow_leather_collar"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_it_wears_a_yellow_leather_collar"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fd47468e3e0f17d7.json", "parameterValues": []}