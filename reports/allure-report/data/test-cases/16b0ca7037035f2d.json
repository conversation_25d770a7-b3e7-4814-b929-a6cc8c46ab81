{"uid": "16b0ca7037035f2d", "name": "测试new year wishes", "fullName": "testcases.test_ella.unsupported_commands.test_new_year_wishes.TestEllaOpenPlayPoliticalNews#test_new_year_wishes", "historyId": "94acf463e3e6d87a1e9cf7ff754044a2", "time": {"start": 1756134461993, "stop": 1756134491887, "duration": 29894}, "description": "测试new year wishes指令", "descriptionHtml": "<p>测试new year wishes指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134449550, "stop": 1756134461992, "duration": 12442}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134461992, "stop": 1756134461992, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试new year wishes指令", "status": "passed", "steps": [{"name": "执行命令: new year wishes", "time": {"start": 1756134461993, "stop": 1756134491623, "duration": 29630}, "status": "passed", "steps": [{"name": "执行命令: new year wishes", "time": {"start": 1756134461993, "stop": 1756134491344, "duration": 29351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134491345, "stop": 1756134491621, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "88ab249a6acb9024", "name": "测试总结", "source": "88ab249a6acb9024.txt", "type": "text/plain", "size": 310}, {"uid": "d823fc75d594870a", "name": "test_completed", "source": "d823fc75d594870a.png", "type": "image/png", "size": 185395}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134491623, "stop": 1756134491624, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134491624, "stop": 1756134491886, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "de5c6e7975969a69", "name": "测试总结", "source": "de5c6e7975969a69.txt", "type": "text/plain", "size": 310}, {"uid": "f9e2f83b73b9b1b3", "name": "test_completed", "source": "f9e2f83b73b9b1b3.png", "type": "image/png", "size": 185564}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "636e11fa23aa0312", "name": "stdout", "source": "636e11fa23aa0312.txt", "type": "text/plain", "size": 12901}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134491888, "stop": 1756134491888, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134491889, "stop": 1756134493316, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_new_year_wishes"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_new_year_wishes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "16b0ca7037035f2d.json", "parameterValues": []}