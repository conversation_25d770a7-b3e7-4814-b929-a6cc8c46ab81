{"uid": "facb6dff08c115f0", "name": "测试minimum volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_minimum_volume.TestEllaMinimumVolume#test_minimum_volume", "historyId": "7cd08c87d5de8ec73ac863e8a636c8aa", "time": {"start": 1756125228976, "stop": 1756125254849, "duration": 25873}, "description": "minimum volume", "descriptionHtml": "<p>minimum volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125215625, "stop": 1756125228975, "duration": 13350}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125228975, "stop": 1756125228975, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "minimum volume", "status": "passed", "steps": [{"name": "执行命令: minimum volume", "time": {"start": 1756125228976, "stop": 1756125254643, "duration": 25667}, "status": "passed", "steps": [{"name": "执行命令: minimum volume", "time": {"start": 1756125228976, "stop": 1756125254357, "duration": 25381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125254357, "stop": 1756125254643, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "ddcb1b722862e46c", "name": "测试总结", "source": "ddcb1b722862e46c.txt", "type": "text/plain", "size": 295}, {"uid": "cbae82c76820227d", "name": "test_completed", "source": "cbae82c76820227d.png", "type": "image/png", "size": 178429}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125254643, "stop": 1756125254645, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756125254645, "stop": 1756125254645, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125254645, "stop": 1756125254849, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "fc2312c8381f122f", "name": "测试总结", "source": "fc2312c8381f122f.txt", "type": "text/plain", "size": 295}, {"uid": "84043129b1a2c7da", "name": "test_completed", "source": "84043129b1a2c7da.png", "type": "image/png", "size": 178429}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "104044a7269dc09", "name": "stdout", "source": "104044a7269dc09.txt", "type": "text/plain", "size": 13414}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125254850, "stop": 1756125254850, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125254851, "stop": 1756125256199, "duration": 1348}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_minimum_volume"}, {"name": "subSuite", "value": "TestEllaMinimumVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_minimum_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "facb6dff08c115f0.json", "parameterValues": []}