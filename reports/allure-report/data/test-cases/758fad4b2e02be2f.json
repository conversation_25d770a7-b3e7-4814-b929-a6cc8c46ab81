{"uid": "758fad4b2e02be2f", "name": "测试puppy", "fullName": "testcases.test_ella.unsupported_commands.test_puppy.TestEllaOpenPlayPoliticalNews#test_puppy", "historyId": "6b4c2fb43e48aa6ef45b7a33c8b2d9ef", "time": {"start": 1756135566763, "stop": 1756135595043, "duration": 28280}, "description": "测试puppy指令", "descriptionHtml": "<p>测试puppy指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135554121, "stop": 1756135566760, "duration": 12639}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135566761, "stop": 1756135566761, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试puppy指令", "status": "passed", "steps": [{"name": "执行命令: puppy", "time": {"start": 1756135566765, "stop": 1756135594762, "duration": 27997}, "status": "passed", "steps": [{"name": "执行命令: puppy", "time": {"start": 1756135566765, "stop": 1756135594479, "duration": 27714}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135594479, "stop": 1756135594761, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "67ec84e1b0b74dd6", "name": "测试总结", "source": "67ec84e1b0b74dd6.txt", "type": "text/plain", "size": 943}, {"uid": "3bf3f97ac397fced", "name": "test_completed", "source": "3bf3f97ac397fced.png", "type": "image/png", "size": 208867}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135594762, "stop": 1756135594763, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135594763, "stop": 1756135595041, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "ee08ba80f321d641", "name": "测试总结", "source": "ee08ba80f321d641.txt", "type": "text/plain", "size": 943}, {"uid": "fe2d4062835a557b", "name": "test_completed", "source": "fe2d4062835a557b.png", "type": "image/png", "size": 209087}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f5abce94f8cb15b5", "name": "stdout", "source": "f5abce94f8cb15b5.txt", "type": "text/plain", "size": 15590}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135595044, "stop": 1756135595044, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135595045, "stop": 1756135596495, "duration": 1450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_puppy"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_puppy"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "758fad4b2e02be2f.json", "parameterValues": []}