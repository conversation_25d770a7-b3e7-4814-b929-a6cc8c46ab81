{"uid": "bd59a62eb70a98bf", "name": "测试close performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode#test_close_performance_mode", "historyId": "57c053de6acd628d4b4cd1230b702a40", "time": {"start": 1756130564198, "stop": 1756130590012, "duration": 25814}, "description": "验证close performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证close performance mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130551409, "stop": 1756130564196, "duration": 12787}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130564198, "stop": 1756130564198, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证close performance mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: close performance mode", "time": {"start": 1756130564199, "stop": 1756130589792, "duration": 25593}, "status": "passed", "steps": [{"name": "执行命令: close performance mode", "time": {"start": 1756130564199, "stop": 1756130589532, "duration": 25333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130589532, "stop": 1756130589792, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "8f0556d4190bc012", "name": "测试总结", "source": "8f0556d4190bc012.txt", "type": "text/plain", "size": 348}, {"uid": "6f6721eaec34e04e", "name": "test_completed", "source": "6f6721eaec34e04e.png", "type": "image/png", "size": 184973}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130589792, "stop": 1756130589793, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130589793, "stop": 1756130590012, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "2918beb5a841ba97", "name": "测试总结", "source": "2918beb5a841ba97.txt", "type": "text/plain", "size": 348}, {"uid": "e734a98274a71b9e", "name": "test_completed", "source": "e734a98274a71b9e.png", "type": "image/png", "size": 184973}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "66bdca74b9df15f9", "name": "stdout", "source": "66bdca74b9df15f9.txt", "type": "text/plain", "size": 12636}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130590013, "stop": 1756130590013, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130590015, "stop": 1756130591448, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_performance_mode"}, {"name": "subSuite", "value": "TestEllaClosePerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bd59a62eb70a98bf.json", "parameterValues": []}