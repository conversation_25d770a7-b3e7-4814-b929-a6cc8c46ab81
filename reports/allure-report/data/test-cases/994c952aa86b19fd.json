{"uid": "994c952aa86b19fd", "name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower.TestEllaOpenPlayPoliticalNews#test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "historyId": "08bb1ce458e948b9a8084fb3ec1f2c83", "time": {"start": 1756132899282, "stop": 1756132925195, "duration": 25913}, "description": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x' 指令", "descriptionHtml": "<p>测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x' 指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132886279, "stop": 1756132899280, "duration": 13001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132899280, "stop": 1756132899280, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x' 指令", "status": "passed", "steps": [{"name": "执行命令: Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "time": {"start": 1756132899282, "stop": 1756132924970, "duration": 25688}, "status": "passed", "steps": [{"name": "执行命令: Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "time": {"start": 1756132899282, "stop": 1756132924716, "duration": 25434}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132924716, "stop": 1756132924969, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "e726d8c051a17d88", "name": "测试总结", "source": "e726d8c051a17d88.txt", "type": "text/plain", "size": 635}, {"uid": "d95ab3fcc6b3170e", "name": "test_completed", "source": "d95ab3fcc6b3170e.png", "type": "image/png", "size": 215783}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132924970, "stop": 1756132924972, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132924972, "stop": 1756132925195, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "7b02642e9b6ebd79", "name": "测试总结", "source": "7b02642e9b6ebd79.txt", "type": "text/plain", "size": 635}, {"uid": "b8ad028d583b76de", "name": "test_completed", "source": "b8ad028d583b76de.png", "type": "image/png", "size": 215783}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "99262f7a3d7e0937", "name": "stdout", "source": "99262f7a3d7e0937.txt", "type": "text/plain", "size": 14322}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132925196, "stop": 1756132925196, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132925197, "stop": 1756132926601, "duration": 1404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "994c952aa86b19fd.json", "parameterValues": []}