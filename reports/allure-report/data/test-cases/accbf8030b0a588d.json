{"uid": "accbf8030b0a588d", "name": "测试check status updates on whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_check_status_updates_on_whatsapp.TestEllaCheckStatusUpdatesWhatsapp#test_check_status_updates_on_whatsapp", "historyId": "861f0c94cdad5a5d60cd9fc71e2429d6", "time": {"start": 1756119476839, "stop": 1756119504072, "duration": 27233}, "description": "check status updates on whatsapp", "descriptionHtml": "<p>check status updates on whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119464058, "stop": 1756119476838, "duration": 12780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119476838, "stop": 1756119476838, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "check status updates on whatsapp", "status": "passed", "steps": [{"name": "执行命令: check status updates on whatsapp", "time": {"start": 1756119476839, "stop": 1756119503821, "duration": 26982}, "status": "passed", "steps": [{"name": "执行命令: check status updates on whatsapp", "time": {"start": 1756119476839, "stop": 1756119503578, "duration": 26739}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119503578, "stop": 1756119503820, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "70be4f32134c0f61", "name": "测试总结", "source": "70be4f32134c0f61.txt", "type": "text/plain", "size": 337}, {"uid": "66368c5ff5c1b983", "name": "test_completed", "source": "66368c5ff5c1b983.png", "type": "image/png", "size": 191293}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119503821, "stop": 1756119503822, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119503822, "stop": 1756119504072, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "49481094cdf089cf", "name": "测试总结", "source": "49481094cdf089cf.txt", "type": "text/plain", "size": 337}, {"uid": "4f6582654f1da455", "name": "test_completed", "source": "4f6582654f1da455.png", "type": "image/png", "size": 191293}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7751e42170de61a1", "name": "stdout", "source": "7751e42170de61a1.txt", "type": "text/plain", "size": 13213}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119504073, "stop": 1756119504073, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119504074, "stop": 1756119505476, "duration": 1402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_check_status_updates_on_whatsapp"}, {"name": "subSuite", "value": "TestEllaCheckStatusUpdatesWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_check_status_updates_on_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "accbf8030b0a588d.json", "parameterValues": []}