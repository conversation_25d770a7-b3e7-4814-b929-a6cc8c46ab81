{"uid": "48f83f5cb028576a", "name": "测试listen to fm能正常执行", "fullName": "testcases.test_ella.dialogue.test_listen_to_fm.TestEllaHelloHello#test_listen_to_fm", "historyId": "a6002c930e7d5977f441f6060af6308d", "time": {"start": 1756120460883, "stop": 1756120486914, "duration": 26031}, "description": "listen to fm", "descriptionHtml": "<p>listen to fm</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120447838, "stop": 1756120460882, "duration": 13044}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120460882, "stop": 1756120460882, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "listen to fm", "status": "passed", "steps": [{"name": "执行命令: listen to fm", "time": {"start": 1756120460883, "stop": 1756120486699, "duration": 25816}, "status": "passed", "steps": [{"name": "执行命令: listen to fm", "time": {"start": 1756120460883, "stop": 1756120486442, "duration": 25559}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120486442, "stop": 1756120486698, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "9d08e38f69bf5fcd", "name": "测试总结", "source": "9d08e38f69bf5fcd.txt", "type": "text/plain", "size": 313}, {"uid": "5fb5d6945c33f90", "name": "test_completed", "source": "5fb5d6945c33f90.png", "type": "image/png", "size": 182864}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120486699, "stop": 1756120486700, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120486700, "stop": 1756120486913, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "86bd6fb366a2140d", "name": "测试总结", "source": "86bd6fb366a2140d.txt", "type": "text/plain", "size": 313}, {"uid": "6242937e703e37f6", "name": "test_completed", "source": "6242937e703e37f6.png", "type": "image/png", "size": 182864}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c76cb6ecb0920325", "name": "stdout", "source": "c76cb6ecb0920325.txt", "type": "text/plain", "size": 12534}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120486914, "stop": 1756120486914, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120486915, "stop": 1756120488318, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_listen_to_fm"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_listen_to_fm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "48f83f5cb028576a.json", "parameterValues": []}