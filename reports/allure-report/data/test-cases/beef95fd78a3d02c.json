{"uid": "beef95fd78a3d02c", "name": "测试order a takeaway返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_order_a_takeaway.TestEllaOrderTakeaway#test_order_a_takeaway", "historyId": "43a8e6496d8d78f2b8bc066858f8bdd9", "time": {"start": 1756134935731, "stop": 1756134961786, "duration": 26055}, "description": "验证order a takeaway指令返回预期的不支持响应", "descriptionHtml": "<p>验证order a takeaway指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134923013, "stop": 1756134935731, "duration": 12718}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134935731, "stop": 1756134935731, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证order a takeaway指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "time": {"start": 1756134935732, "stop": 1756134961562, "duration": 25830}, "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "time": {"start": 1756134935732, "stop": 1756134961316, "duration": 25584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134961316, "stop": 1756134961562, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "d0818f27663ab31f", "name": "测试总结", "source": "d0818f27663ab31f.txt", "type": "text/plain", "size": 306}, {"uid": "561944cc79dd94e1", "name": "test_completed", "source": "561944cc79dd94e1.png", "type": "image/png", "size": 184214}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756134961562, "stop": 1756134961564, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134961564, "stop": 1756134961784, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "f7078a5ce879cb9f", "name": "测试总结", "source": "f7078a5ce879cb9f.txt", "type": "text/plain", "size": 306}, {"uid": "a73c6fec4acedb6d", "name": "test_completed", "source": "a73c6fec4acedb6d.png", "type": "image/png", "size": 184214}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "38c71e8308c2a9b0", "name": "stdout", "source": "38c71e8308c2a9b0.txt", "type": "text/plain", "size": 12938}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134961787, "stop": 1756134961787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134961789, "stop": 1756134963241, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_order_a_takeaway"}, {"name": "subSuite", "value": "TestEllaOrderTakeaway"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_order_a_takeaway"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "beef95fd78a3d02c.json", "parameterValues": []}