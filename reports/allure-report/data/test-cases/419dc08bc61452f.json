{"uid": "419dc08bc61452f", "name": "测试change your voice能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_change_your_voice.TestEllaChangeYourVoice#test_change_your_voice", "historyId": "70c4e0c16f5cb3629f87876768739a8d", "time": {"start": 1756130010522, "stop": 1756130037402, "duration": 26880}, "description": "change your voice", "descriptionHtml": "<p>change your voice</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129997902, "stop": 1756130010520, "duration": 12618}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130010520, "stop": 1756130010521, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "change your voice", "status": "passed", "steps": [{"name": "执行命令: change your voice", "time": {"start": 1756130010522, "stop": 1756130037170, "duration": 26648}, "status": "passed", "steps": [{"name": "执行命令: change your voice", "time": {"start": 1756130010522, "stop": 1756130036895, "duration": 26373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130036895, "stop": 1756130037169, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "b66a362757262b71", "name": "测试总结", "source": "b66a362757262b71.txt", "type": "text/plain", "size": 244}, {"uid": "8efb37e11fe14876", "name": "test_completed", "source": "8efb37e11fe14876.png", "type": "image/png", "size": 188680}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130037170, "stop": 1756130037172, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130037172, "stop": 1756130037401, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "af438a476c8d14bd", "name": "测试总结", "source": "af438a476c8d14bd.txt", "type": "text/plain", "size": 244}, {"uid": "688f1f3c48652db3", "name": "test_completed", "source": "688f1f3c48652db3.png", "type": "image/png", "size": 188680}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ef11d6971e452008", "name": "stdout", "source": "ef11d6971e452008.txt", "type": "text/plain", "size": 12375}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130037403, "stop": 1756130037403, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130037404, "stop": 1756130038830, "duration": 1426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_change_your_voice"}, {"name": "subSuite", "value": "TestEllaChangeYourVoice"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_change_your_voice"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "419dc08bc61452f.json", "parameterValues": []}