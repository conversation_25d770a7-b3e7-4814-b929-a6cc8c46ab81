{"uid": "bbb3a5c1c4fefe6f", "name": "测试search picture in my gallery能正常执行", "fullName": "testcases.test_ella.dialogue.test_search_picture_in_my_gallery.TestEllaHowIsWeatherToday#test_search_picture_in_my_gallery", "historyId": "a1088ee9683cc60d86b0994865138921", "time": {"start": 1756121381934, "stop": 1756121411898, "duration": 29964}, "description": "search picture in my gallery", "descriptionHtml": "<p>search picture in my gallery</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121369014, "stop": 1756121381931, "duration": 12917}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121381931, "stop": 1756121381931, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "search picture in my gallery", "status": "passed", "steps": [{"name": "执行命令: search picture in my gallery", "time": {"start": 1756121381934, "stop": 1756121411658, "duration": 29724}, "status": "passed", "steps": [{"name": "执行命令: search picture in my gallery", "time": {"start": 1756121381934, "stop": 1756121411367, "duration": 29433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121411367, "stop": 1756121411657, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "7160273ebd94bc56", "name": "测试总结", "source": "7160273ebd94bc56.txt", "type": "text/plain", "size": 323}, {"uid": "1c24eef0c3954b35", "name": "test_completed", "source": "1c24eef0c3954b35.png", "type": "image/png", "size": 173017}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121411658, "stop": 1756121411660, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121411660, "stop": 1756121411898, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "d7750b24673f6abc", "name": "测试总结", "source": "d7750b24673f6abc.txt", "type": "text/plain", "size": 323}, {"uid": "5fbdf640da62dc35", "name": "test_completed", "source": "5fbdf640da62dc35.png", "type": "image/png", "size": 173017}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "58da968ec2ed619f", "name": "stdout", "source": "58da968ec2ed619f.txt", "type": "text/plain", "size": 13148}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121411899, "stop": 1756121411899, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121411900, "stop": 1756121413278, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_search_picture_in_my_gallery"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_search_picture_in_my_gallery"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bbb3a5c1c4fefe6f.json", "parameterValues": []}