{"uid": "7ea9da40519a0dab", "name": "测试increase the brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_increase_the_brightness.TestEllaIncreaseBrightness#test_increase_the_brightness", "historyId": "e78aa9affdc78502b8ad3b712ecf28b9", "time": {"start": 1756124629347, "stop": 1756124656258, "duration": 26911}, "description": "increase the brightness", "descriptionHtml": "<p>increase the brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124616090, "stop": 1756124629344, "duration": 13254}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124629344, "stop": 1756124629344, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "increase the brightness", "status": "passed", "steps": [{"name": "执行命令: increase the brightness", "time": {"start": 1756124629347, "stop": 1756124656041, "duration": 26694}, "status": "passed", "steps": [{"name": "执行命令: increase the brightness", "time": {"start": 1756124629347, "stop": 1756124655768, "duration": 26421}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124655768, "stop": 1756124656040, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "635d0b1497e46af2", "name": "测试总结", "source": "635d0b1497e46af2.txt", "type": "text/plain", "size": 213}, {"uid": "c1ffef68e402e9a", "name": "test_completed", "source": "c1ffef68e402e9a.png", "type": "image/png", "size": 151472}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124656041, "stop": 1756124656043, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124656043, "stop": 1756124656043, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124656043, "stop": 1756124656257, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "9d974146168d3b9f", "name": "测试总结", "source": "9d974146168d3b9f.txt", "type": "text/plain", "size": 213}, {"uid": "eaed166227ba1529", "name": "test_completed", "source": "eaed166227ba1529.png", "type": "image/png", "size": 151472}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e48fc6332589b923", "name": "stdout", "source": "e48fc6332589b923.txt", "type": "text/plain", "size": 13183}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124656258, "stop": 1756124656258, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124656259, "stop": 1756124657693, "duration": 1434}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_the_brightness"}, {"name": "subSuite", "value": "TestEllaIncreaseBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_the_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7ea9da40519a0dab.json", "parameterValues": []}