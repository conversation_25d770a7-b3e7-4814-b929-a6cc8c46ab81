{"uid": "dc93ebb65c6618f2", "name": "测试turn down notifications volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_down_notifications_volume.TestEllaTurnDownNotificationsVolume#test_turn_down_notifications_volume", "historyId": "8373737839693413a37e35b627a0f5de", "time": {"start": 1756127228278, "stop": 1756127281908, "duration": 53630}, "description": "turn down notifications volume", "descriptionHtml": "<p>turn down notifications volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127215650, "stop": 1756127228277, "duration": 12627}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127228277, "stop": 1756127228277, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "turn down notifications volume", "status": "passed", "steps": [{"name": "执行命令: turn down notifications volume", "time": {"start": 1756127228278, "stop": 1756127254549, "duration": 26271}, "status": "passed", "steps": [{"name": "执行命令: set notifications volume to 50", "time": {"start": 1756127228278, "stop": 1756127254276, "duration": 25998}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127254276, "stop": 1756127254548, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "4d2b77ec8db8d1a6", "name": "测试总结", "source": "4d2b77ec8db8d1a6.txt", "type": "text/plain", "size": 324}, {"uid": "52093d4c159b0f27", "name": "test_completed", "source": "52093d4c159b0f27.png", "type": "image/png", "size": 182181}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn down notifications volume", "time": {"start": 1756127254549, "stop": 1756127281667, "duration": 27118}, "status": "passed", "steps": [{"name": "执行命令: turn down notifications volume", "time": {"start": 1756127254549, "stop": 1756127281394, "duration": 26845}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127281394, "stop": 1756127281666, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "e9a1f7c2f593d165", "name": "测试总结", "source": "e9a1f7c2f593d165.txt", "type": "text/plain", "size": 326}, {"uid": "ff211df685c4f9bb", "name": "test_completed", "source": "ff211df685c4f9bb.png", "type": "image/png", "size": 177847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127281667, "stop": 1756127281668, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756127281668, "stop": 1756127281668, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127281668, "stop": 1756127281907, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "ab2a2013868ddc39", "name": "测试总结", "source": "ab2a2013868ddc39.txt", "type": "text/plain", "size": 326}, {"uid": "1afbd8c0dbda05aa", "name": "test_completed", "source": "1afbd8c0dbda05aa.png", "type": "image/png", "size": 177724}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b508655ceffbd1c1", "name": "stdout", "source": "b508655ceffbd1c1.txt", "type": "text/plain", "size": 25151}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 9, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127281908, "stop": 1756127281908, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127281909, "stop": 1756127283248, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_notifications_volume"}, {"name": "subSuite", "value": "TestEllaTurnDownNotificationsVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_notifications_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dc93ebb65c6618f2.json", "parameterValues": []}