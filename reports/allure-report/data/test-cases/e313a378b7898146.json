{"uid": "e313a378b7898146", "name": "测试turn on the alarm at 8 am", "fullName": "testcases.test_ella.component_coupling.test_turn_on_the_alarm_at_8_am.TestEllaOpenClock#test_turn_on_the_alarm_at_8_am", "historyId": "b165a17be8ab35920a6af9be7611a2c9", "time": {"start": 1756119099878, "stop": 1756119205632, "duration": 105754}, "description": "测试turn on the alarm at 8 am指令", "descriptionHtml": "<p>测试turn on the alarm at 8 am指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119087080, "stop": 1756119099877, "duration": 12797}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119099877, "stop": 1756119099877, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn on the alarm at 8 am指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756119099878, "stop": 1756119125601, "duration": 25723}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756119099878, "stop": 1756119125331, "duration": 25453}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119125331, "stop": 1756119125600, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "ad0c4dfb7c552ac5", "name": "测试总结", "source": "ad0c4dfb7c552ac5.txt", "type": "text/plain", "size": 303}, {"uid": "ccfd8154fdaf3bb2", "name": "test_completed", "source": "ccfd8154fdaf3bb2.png", "type": "image/png", "size": 177235}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756119125601, "stop": 1756119151910, "duration": 26309}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756119125601, "stop": 1756119151673, "duration": 26072}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119151673, "stop": 1756119151909, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "4eb5e1a510f8ba86", "name": "测试总结", "source": "4eb5e1a510f8ba86.txt", "type": "text/plain", "size": 242}, {"uid": "f0ccce93992f41ae", "name": "test_completed", "source": "f0ccce93992f41ae.png", "type": "image/png", "size": 147837}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn on the alarm at 8 am", "time": {"start": 1756119151910, "stop": 1756119178689, "duration": 26779}, "status": "passed", "steps": [{"name": "执行命令: turn on the alarm at 8 am", "time": {"start": 1756119151910, "stop": 1756119178411, "duration": 26501}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119178411, "stop": 1756119178689, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "93c6c01e1d575264", "name": "测试总结", "source": "93c6c01e1d575264.txt", "type": "text/plain", "size": 246}, {"uid": "c26600a6ddc2ff6d", "name": "test_completed", "source": "c26600a6ddc2ff6d.png", "type": "image/png", "size": 150833}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756119178689, "stop": 1756119205381, "duration": 26692}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756119178689, "stop": 1756119205146, "duration": 26457}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119205146, "stop": 1756119205381, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "5d2563d1a486b392", "name": "测试总结", "source": "5d2563d1a486b392.txt", "type": "text/plain", "size": 223}, {"uid": "ca8f8ecae4c10e4f", "name": "test_completed", "source": "ca8f8ecae4c10e4f.png", "type": "image/png", "size": 144174}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119205381, "stop": 1756119205383, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119205383, "stop": 1756119205631, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "b7430eb37b3d9c09", "name": "测试总结", "source": "b7430eb37b3d9c09.txt", "type": "text/plain", "size": 230}, {"uid": "2c39fd4b9c2a0904", "name": "test_completed", "source": "2c39fd4b9c2a0904.png", "type": "image/png", "size": 144081}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a46239a160a64bb3", "name": "stdout", "source": "a46239a160a64bb3.txt", "type": "text/plain", "size": 41158}], "parameters": [], "attachmentsCount": 11, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 14, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119205633, "stop": 1756119205633, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119205641, "stop": 1756119206956, "duration": 1315}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_on_the_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_on_the_alarm_at_8_am"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e313a378b7898146.json", "parameterValues": []}