{"uid": "57fdde868ca83a88", "name": "测试What languages do you support能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_languages_do_you_support.TestEllaWhatLanguagesDoYouSupport#test_what_languages_do_you_support", "historyId": "0c44c94f08feed70addcec44e96bda5a", "time": {"start": 1756122288057, "stop": 1756122314138, "duration": 26081}, "description": "What languages do you support", "descriptionHtml": "<p>What languages do you support</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122274960, "stop": 1756122288054, "duration": 13094}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122288054, "stop": 1756122288054, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "What languages do you support", "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1756122288057, "stop": 1756122313895, "duration": 25838}, "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1756122288057, "stop": 1756122313604, "duration": 25547}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122313604, "stop": 1756122313894, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "1c50513424f4e495", "name": "测试总结", "source": "1c50513424f4e495.txt", "type": "text/plain", "size": 431}, {"uid": "7244a94fba991ea7", "name": "test_completed", "source": "7244a94fba991ea7.png", "type": "image/png", "size": 189184}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122313895, "stop": 1756122313902, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122313902, "stop": 1756122314137, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "3dea5574b37a999b", "name": "测试总结", "source": "3dea5574b37a999b.txt", "type": "text/plain", "size": 431}, {"uid": "34777588cd77b5e0", "name": "test_completed", "source": "34777588cd77b5e0.png", "type": "image/png", "size": 189184}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fb1611337cf5d8a0", "name": "stdout", "source": "fb1611337cf5d8a0.txt", "type": "text/plain", "size": 13080}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122314139, "stop": 1756122314140, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122314141, "stop": 1756122315574, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_languages_do_you_support"}, {"name": "subSuite", "value": "TestEllaWhatLanguagesDoYouSupport"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_languages_do_you_support"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "57fdde868ca83a88.json", "parameterValues": []}