{"uid": "641747699244fba5", "name": "测试search the address in the image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage#test_search_the_address_in_the_image", "historyId": "c50847e2010bac3c5a9bb7ff0b690fb6", "time": {"start": 1756135818629, "stop": 1756135845947, "duration": 27318}, "description": "search the address in the image", "descriptionHtml": "<p>search the address in the image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135805982, "stop": 1756135818627, "duration": 12645}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135818627, "stop": 1756135818627, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "search the address in the image", "status": "passed", "steps": [{"name": "执行命令: search the address in the image", "time": {"start": 1756135818629, "stop": 1756135845715, "duration": 27086}, "status": "passed", "steps": [{"name": "执行命令: search the address in the image", "time": {"start": 1756135818629, "stop": 1756135845436, "duration": 26807}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135845436, "stop": 1756135845715, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "ccfc856b7883b3f4", "name": "测试总结", "source": "ccfc856b7883b3f4.txt", "type": "text/plain", "size": 775}, {"uid": "b54b1089de2c17b7", "name": "test_completed", "source": "b54b1089de2c17b7.png", "type": "image/png", "size": 189238}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135845715, "stop": 1756135845715, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135845715, "stop": 1756135845947, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "996987d4dfc06583", "name": "测试总结", "source": "996987d4dfc06583.txt", "type": "text/plain", "size": 775}, {"uid": "29247d32cd6973ff", "name": "test_completed", "source": "29247d32cd6973ff.png", "type": "image/png", "size": 189190}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d8d4f38a089e58e3", "name": "stdout", "source": "d8d4f38a089e58e3.txt", "type": "text/plain", "size": 14894}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135845948, "stop": 1756135845948, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135845949, "stop": 1756135847421, "duration": 1472}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaSearchAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "641747699244fba5.json", "parameterValues": []}