{"uid": "a523a3429482cc07", "name": "测试set smart panel返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_panel.TestEllaSetSmartPanel#test_set_smart_panel", "historyId": "5fc780d1e7f790011f0e4a521e125a16", "time": {"start": 1756137517606, "stop": 1756137552173, "duration": 34567}, "description": "验证set smart panel指令返回预期的不支持响应", "descriptionHtml": "<p>验证set smart panel指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart panel', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.smartpanel页面内容] Smart Panel | Hold and drag the top to change the position. | Smart Panel | Recent Files | Quickly view or share recent files. | Smart Hub | An efficient cross-app content transfer tool. | Vibration Switch']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_smart_panel.TestEllaSetSmartPanel object at 0x00000292057DD1D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8C4E50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_smart_panel(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart panel', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.smartpanel页面内容] Smart Panel | Hold and drag the top to change the position. | Smart Panel | Recent Files | Quickly view or share recent files. | Smart Hub | An efficient cross-app content transfer tool. | Vibration Switch']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_smart_panel.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137504449, "stop": 1756137517605, "duration": 13156}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137517605, "stop": 1756137517605, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set smart panel指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart panel', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.smartpanel页面内容] Smart Panel | Hold and drag the top to change the position. | Smart Panel | Recent Files | Quickly view or share recent files. | Smart Hub | An efficient cross-app content transfer tool. | Vibration Switch']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_smart_panel.TestEllaSetSmartPanel object at 0x00000292057DD1D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8C4E50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_smart_panel(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart panel', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.smartpanel页面内容] Smart Panel | Hold and drag the top to change the position. | Smart Panel | Recent Files | Quickly view or share recent files. | Smart Hub | An efficient cross-app content transfer tool. | Vibration Switch']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_smart_panel.py:33: AssertionError", "steps": [{"name": "执行命令: set smart panel", "time": {"start": 1756137517606, "stop": 1756137552169, "duration": 34563}, "status": "passed", "steps": [{"name": "执行命令: set smart panel", "time": {"start": 1756137517606, "stop": 1756137551916, "duration": 34310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137551916, "stop": 1756137552168, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "65088a5e448bd797", "name": "测试总结", "source": "65088a5e448bd797.txt", "type": "text/plain", "size": 514}, {"uid": "130002c22f7a3a62", "name": "test_completed", "source": "130002c22f7a3a62.png", "type": "image/png", "size": 169007}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137552169, "stop": 1756137552172, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set smart panel', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.smartpanel页面内容] Smart Panel | Hold and drag the top to change the position. | Smart Panel | Recent Files | Quickly view or share recent files. | Smart Hub | An efficient cross-app content transfer tool. | Vibration Switch']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_smart_panel.py\", line 33, in test_set_smart_panel\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e4187e0d6bc17027", "name": "stdout", "source": "e4187e0d6bc17027.txt", "type": "text/plain", "size": 15981}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137552176, "stop": 1756137552439, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "39d9ee65bc43c0df", "name": "失败截图-TestEllaSetSmartPanel", "source": "39d9ee65bc43c0df.png", "type": "image/png", "size": 168862}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137552441, "stop": 1756137553915, "duration": 1474}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_panel"}, {"name": "subSuite", "value": "TestEllaSetSmartPanel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_panel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a523a3429482cc07.json", "parameterValues": []}