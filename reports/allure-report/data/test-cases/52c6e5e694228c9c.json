{"uid": "52c6e5e694228c9c", "name": "测试what's the date today", "fullName": "testcases.test_ella.unsupported_commands.test_what_s_the_date_today.TestEllaOpenPlayPoliticalNews#test_what_s_the_date_today", "historyId": "4a00cb3818a7086991fb9f7a4d2a3bb5", "time": {"start": 1756138948586, "stop": 1756138974607, "duration": 26021}, "description": "测试what's the date today指令", "descriptionHtml": "<p>测试what's the date today指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138936031, "stop": 1756138948584, "duration": 12553}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138948584, "stop": 1756138948584, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试what's the date today指令", "status": "passed", "steps": [{"name": "执行命令: what's the date today", "time": {"start": 1756138948586, "stop": 1756138974398, "duration": 25812}, "status": "passed", "steps": [{"name": "执行命令: what's the date today", "time": {"start": 1756138948586, "stop": 1756138974109, "duration": 25523}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138974109, "stop": 1756138974398, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "5ffb99b101b1dfd6", "name": "测试总结", "source": "5ffb99b101b1dfd6.txt", "type": "text/plain", "size": 291}, {"uid": "cd1257e2f34aba9a", "name": "test_completed", "source": "cd1257e2f34aba9a.png", "type": "image/png", "size": 157800}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138974398, "stop": 1756138974400, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138974400, "stop": 1756138974606, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "8e3f298c36736f05", "name": "测试总结", "source": "8e3f298c36736f05.txt", "type": "text/plain", "size": 291}, {"uid": "9f6e715ba5b75852", "name": "test_completed", "source": "9f6e715ba5b75852.png", "type": "image/png", "size": 157800}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "99d1ecf69231f67f", "name": "stdout", "source": "99d1ecf69231f67f.txt", "type": "text/plain", "size": 12737}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138974607, "stop": 1756138974607, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138974608, "stop": 1756138975979, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_s_the_date_today"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_s_the_date_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "52c6e5e694228c9c.json", "parameterValues": []}