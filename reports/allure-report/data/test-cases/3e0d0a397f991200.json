{"uid": "3e0d0a397f991200", "name": "测试turn off driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode.TestEllaTurnOffDrivingMode#test_turn_off_driving_mode", "historyId": "984a0fd313bba0ca2f20f0bbff732eb8", "time": {"start": 1756138524351, "stop": 1756138549397, "duration": 25046}, "description": "验证turn off driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn off driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138511392, "stop": 1756138524341, "duration": 12949}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138524342, "stop": 1756138524342, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证turn off driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1756138524351, "stop": 1756138549158, "duration": 24807}, "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1756138524351, "stop": 1756138548876, "duration": 24525}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138548876, "stop": 1756138549157, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "b8fb794763df3ea2", "name": "测试总结", "source": "b8fb794763df3ea2.txt", "type": "text/plain", "size": 341}, {"uid": "c4f8cb1141314e2", "name": "test_completed", "source": "c4f8cb1141314e2.png", "type": "image/png", "size": 170397}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138549158, "stop": 1756138549159, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138549159, "stop": 1756138549395, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "a98e109fea5f4956", "name": "测试总结", "source": "a98e109fea5f4956.txt", "type": "text/plain", "size": 341}, {"uid": "bdc864a7afeb01f0", "name": "test_completed", "source": "bdc864a7afeb01f0.png", "type": "image/png", "size": 170397}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b803360b1d26eeb", "name": "stdout", "source": "b803360b1d26eeb.txt", "type": "text/plain", "size": 12508}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138549398, "stop": 1756138549398, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138549399, "stop": 1756138550813, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnOffDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3e0d0a397f991200.json", "parameterValues": []}