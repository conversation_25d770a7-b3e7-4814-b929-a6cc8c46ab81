{"uid": "e63d21b7ad8a7014", "name": "测试pause fm能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_fm.TestEllaPauseFm#test_pause_fm", "historyId": "861aa58f9a3d0d9c9861d88316e784c5", "time": {"start": 1756118061883, "stop": 1756118087982, "duration": 26099}, "description": "pause fm", "descriptionHtml": "<p>pause fm</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118049418, "stop": 1756118061882, "duration": 12464}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118061882, "stop": 1756118061882, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "pause fm", "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1756118061883, "stop": 1756118087776, "duration": 25893}, "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1756118061883, "stop": 1756118087471, "duration": 25588}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118087471, "stop": 1756118087776, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "61c14a585f4ea706", "name": "测试总结", "source": "61c14a585f4ea706.txt", "type": "text/plain", "size": 305}, {"uid": "cc5af78ea8641562", "name": "test_completed", "source": "cc5af78ea8641562.png", "type": "image/png", "size": 174291}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118087776, "stop": 1756118087777, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118087777, "stop": 1756118087982, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "c62e30635d147b46", "name": "测试总结", "source": "c62e30635d147b46.txt", "type": "text/plain", "size": 305}, {"uid": "5f323e937b935521", "name": "test_completed", "source": "5f323e937b935521.png", "type": "image/png", "size": 174291}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a1e9f8410d321859", "name": "stdout", "source": "a1e9f8410d321859.txt", "type": "text/plain", "size": 12492}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118087983, "stop": 1756118087983, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118087985, "stop": 1756118089423, "duration": 1438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_fm"}, {"name": "subSuite", "value": "TestEllaPauseFm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_fm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e63d21b7ad8a7014.json", "parameterValues": []}