{"uid": "af7f7f15c4a56df1", "name": "测试tell me joke能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_tell_me_joke.TestEllaTellMeJoke#test_tell_me_joke", "historyId": "5ad5ef8bf0f7913e709dbc1e706db1e5", "time": {"start": 1756138276811, "stop": 1756138303078, "duration": 26267}, "description": "tell me joke", "descriptionHtml": "<p>tell me joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138264157, "stop": 1756138276811, "duration": 12654}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138276811, "stop": 1756138276811, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "tell me joke", "status": "passed", "steps": [{"name": "执行命令: tell me joke", "time": {"start": 1756138276812, "stop": 1756138302851, "duration": 26039}, "status": "passed", "steps": [{"name": "执行命令: tell me joke", "time": {"start": 1756138276812, "stop": 1756138302572, "duration": 25760}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138302572, "stop": 1756138302850, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "e6d34abccc217302", "name": "测试总结", "source": "e6d34abccc217302.txt", "type": "text/plain", "size": 747}, {"uid": "7cb43a552b2024c7", "name": "test_completed", "source": "7cb43a552b2024c7.png", "type": "image/png", "size": 181074}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138302851, "stop": 1756138302852, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138302852, "stop": 1756138303077, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "ee4648aa2dd2310f", "name": "测试总结", "source": "ee4648aa2dd2310f.txt", "type": "text/plain", "size": 747}, {"uid": "e46c86358c056541", "name": "test_completed", "source": "e46c86358c056541.png", "type": "image/png", "size": 181074}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6d729220a69951b3", "name": "stdout", "source": "6d729220a69951b3.txt", "type": "text/plain", "size": 15287}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138303079, "stop": 1756138303079, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138303084, "stop": 1756138304467, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_tell_me_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_tell_me_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "af7f7f15c4a56df1.json", "parameterValues": []}