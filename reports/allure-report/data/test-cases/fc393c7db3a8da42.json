{"uid": "fc393c7db3a8da42", "name": "测试what's the wheather today?能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday#test_what_s_the_wheather_today", "historyId": "c2a64f07232d43585d1dfee25c2f9407", "time": {"start": 1756122424058, "stop": 1756122449657, "duration": 25599}, "description": "what's the wheather today?", "descriptionHtml": "<p>what's the wheather today?</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday object at 0x0000029204754990>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029205AA1610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_wheather_today.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122410759, "stop": 1756122424054, "duration": 13295}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122424055, "stop": 1756122424055, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "what's the wheather today?", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday object at 0x0000029204754990>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029205AA1610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_wheather_today.py:33: AssertionError", "steps": [{"name": "执行命令: what's the wheather today?", "time": {"start": 1756122424060, "stop": 1756122449647, "duration": 25587}, "status": "passed", "steps": [{"name": "执行命令: what's the wheather today?", "time": {"start": 1756122424060, "stop": 1756122449352, "duration": 25292}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122449352, "stop": 1756122449644, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "e8e676f9e858caf6", "name": "测试总结", "source": "e8e676f9e858caf6.txt", "type": "text/plain", "size": 320}, {"uid": "259f468e906a58", "name": "test_completed", "source": "259f468e906a58.png", "type": "image/png", "size": 168072}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122449647, "stop": 1756122449655, "duration": 8}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_wheather_today.py\", line 33, in test_what_s_the_wheather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "688cbde044545d9e", "name": "stdout", "source": "688cbde044545d9e.txt", "type": "text/plain", "size": 13386}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122449680, "stop": 1756122449949, "duration": 269}, "status": "passed", "steps": [], "attachments": [{"uid": "a53616e92b129d61", "name": "失败截图-TestEllaWhatSWheatherToday", "source": "a53616e92b129d61.png", "type": "image/png", "size": 168072}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756122449952, "stop": 1756122451313, "duration": 1361}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_wheather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "fc393c7db3a8da42.json", "parameterValues": []}