{"uid": "14c6c8a865ab97e1", "name": "测试adjustment the brightness to maximun能正常执行", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_maximun.TestEllaAdjustmentBrightnessMaximun#test_adjustment_the_brightness_to_maximun", "historyId": "ce2018f4cca8041a465d2a753ade920b", "time": {"start": 1756123709473, "stop": 1756123736484, "duration": 27011}, "description": "adjustment the brightness to maximun", "descriptionHtml": "<p>adjustment the brightness to maximun</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123696750, "stop": 1756123709471, "duration": 12721}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123709471, "stop": 1756123709471, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "adjustment the brightness to maximun", "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to maximun", "time": {"start": 1756123709473, "stop": 1756123736234, "duration": 26761}, "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to maximun", "time": {"start": 1756123709473, "stop": 1756123735978, "duration": 26505}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123735978, "stop": 1756123736233, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "b7f92f2e5883fbf3", "name": "测试总结", "source": "b7f92f2e5883fbf3.txt", "type": "text/plain", "size": 245}, {"uid": "705a35deb13f985d", "name": "test_completed", "source": "705a35deb13f985d.png", "type": "image/png", "size": 154847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123736234, "stop": 1756123736235, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756123736235, "stop": 1756123736235, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123736235, "stop": 1756123736483, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "9100464aeb65b94", "name": "测试总结", "source": "9100464aeb65b94.txt", "type": "text/plain", "size": 245}, {"uid": "3a223c182fb70ac9", "name": "test_completed", "source": "3a223c182fb70ac9.png", "type": "image/png", "size": 154847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a97cd05d9faa536d", "name": "stdout", "source": "a97cd05d9faa536d.txt", "type": "text/plain", "size": 13301}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123736485, "stop": 1756123736485, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123736486, "stop": 1756123737827, "duration": 1341}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to_maximun"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightnessMaximun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_maximun"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "14c6c8a865ab97e1.json", "parameterValues": []}