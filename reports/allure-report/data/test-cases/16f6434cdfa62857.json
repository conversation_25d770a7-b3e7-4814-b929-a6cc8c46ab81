{"uid": "16f6434cdfa62857", "name": "测试download app能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_app.TestEllaDownloadApp#test_download_app", "historyId": "578e52c6d5e868d5464682b454971c51", "time": {"start": 1756128717169, "stop": 1756128744493, "duration": 27324}, "description": "download app", "descriptionHtml": "<p>download app</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128704245, "stop": 1756128717167, "duration": 12922}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128717167, "stop": 1756128717167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "download app", "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1756128717169, "stop": 1756128744237, "duration": 27068}, "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1756128717169, "stop": 1756128743961, "duration": 26792}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128743961, "stop": 1756128744237, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "128b71637f386c66", "name": "测试总结", "source": "128b71637f386c66.txt", "type": "text/plain", "size": 285}, {"uid": "da16d2204ce6aa63", "name": "test_completed", "source": "da16d2204ce6aa63.png", "type": "image/png", "size": 161414}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756128744237, "stop": 1756128744240, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128744240, "stop": 1756128744492, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "35d2862192fb095b", "name": "测试总结", "source": "35d2862192fb095b.txt", "type": "text/plain", "size": 285}, {"uid": "3641c4cb991641ee", "name": "test_completed", "source": "3641c4cb991641ee.png", "type": "image/png", "size": 161436}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4d9c49b085152d61", "name": "stdout", "source": "4d9c49b085152d61.txt", "type": "text/plain", "size": 13085}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128744494, "stop": 1756128744494, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128744496, "stop": 1756128745978, "duration": 1482}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_app"}, {"name": "subSuite", "value": "TestEllaDownloadApp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "16f6434cdfa62857.json", "parameterValues": []}