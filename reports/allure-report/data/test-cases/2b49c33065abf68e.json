{"uid": "2b49c33065abf68e", "name": "测试say hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_say_hello.TestEllaSayHello#test_say_hello", "historyId": "f416bca94fc67372d77ac2dd1f3e4517", "time": {"start": 1756121294410, "stop": 1756121323613, "duration": 29203}, "description": "say hello", "descriptionHtml": "<p>say hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121281502, "stop": 1756121294409, "duration": 12907}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121294409, "stop": 1756121294409, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "say hello", "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1756121294410, "stop": 1756121323383, "duration": 28973}, "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1756121294410, "stop": 1756121323125, "duration": 28715}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121323125, "stop": 1756121323382, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "2aae795b82a59c8f", "name": "测试总结", "source": "2aae795b82a59c8f.txt", "type": "text/plain", "size": 753}, {"uid": "bba32764c27f39d2", "name": "test_completed", "source": "bba32764c27f39d2.png", "type": "image/png", "size": 186315}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121323383, "stop": 1756121323385, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121323385, "stop": 1756121323612, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "8b4714aa388d71ff", "name": "测试总结", "source": "8b4714aa388d71ff.txt", "type": "text/plain", "size": 753}, {"uid": "a9423915ac00ffa7", "name": "test_completed", "source": "a9423915ac00ffa7.png", "type": "image/png", "size": 186262}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "381474c0a7fa82b", "name": "stdout", "source": "381474c0a7fa82b.txt", "type": "text/plain", "size": 15345}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121323614, "stop": 1756121323614, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121323616, "stop": 1756121324974, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_say_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_say_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2b49c33065abf68e.json", "parameterValues": []}