{"uid": "a8f8e81710437e23", "name": "测试check battery information返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_battery_information.TestEllaCheckBatteryInformation#test_check_battery_information", "historyId": "613ef0933e4be87696bbedd56b4f0052", "time": {"start": 1756130051508, "stop": 1756130090436, "duration": 38928}, "description": "验证check battery information指令返回预期的不支持响应", "descriptionHtml": "<p>验证check battery information指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130038837, "stop": 1756130051507, "duration": 12670}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130051507, "stop": 1756130051507, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证check battery information指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check battery information", "time": {"start": 1756130051508, "stop": 1756130090196, "duration": 38688}, "status": "passed", "steps": [{"name": "执行命令: check battery information", "time": {"start": 1756130051508, "stop": 1756130089911, "duration": 38403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130089911, "stop": 1756130090196, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "9676672a12732f73", "name": "测试总结", "source": "9676672a12732f73.txt", "type": "text/plain", "size": 616}, {"uid": "c59953a3c3b83a5b", "name": "test_completed", "source": "c59953a3c3b83a5b.png", "type": "image/png", "size": 171501}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130090196, "stop": 1756130090198, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130090198, "stop": 1756130090434, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "ca0b19215884d446", "name": "测试总结", "source": "ca0b19215884d446.txt", "type": "text/plain", "size": 616}, {"uid": "8688e633cbb8a456", "name": "test_completed", "source": "8688e633cbb8a456.png", "type": "image/png", "size": 171329}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "69df989a514b2d07", "name": "stdout", "source": "69df989a514b2d07.txt", "type": "text/plain", "size": 15741}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130090437, "stop": 1756130090437, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130090438, "stop": 1756130091863, "duration": 1425}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_battery_information"}, {"name": "subSuite", "value": "TestEllaCheckBatteryInformation"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_battery_information"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a8f8e81710437e23.json", "parameterValues": []}