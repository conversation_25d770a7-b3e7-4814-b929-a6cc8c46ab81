{"uid": "edeaea69f52e61b", "name": "测试What's the weather like today能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_today", "historyId": "ac6792be4ebb553a5655a2c44aca218c", "time": {"start": 1756122328926, "stop": 1756122362108, "duration": 33182}, "description": "What's the weather like today", "descriptionHtml": "<p>What's the weather like today</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday object at 0x0000029204730550>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206DAEF50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_like_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py:37: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122315616, "stop": 1756122328922, "duration": 13306}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122328923, "stop": 1756122328923, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "What's the weather like today", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday object at 0x0000029204730550>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206DAEF50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_like_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py:37: AssertionError", "steps": [{"name": "执行命令: What's the weather like today", "time": {"start": 1756122328926, "stop": 1756122362084, "duration": 33158}, "status": "passed", "steps": [{"name": "执行命令: What's the weather like today", "time": {"start": 1756122328926, "stop": 1756122361793, "duration": 32867}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122361793, "stop": 1756122362083, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "7714e2f7225178c6", "name": "测试总结", "source": "7714e2f7225178c6.txt", "type": "text/plain", "size": 328}, {"uid": "6ac335d55cefcf0b", "name": "test_completed", "source": "6ac335d55cefcf0b.png", "type": "image/png", "size": 167286}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122362084, "stop": 1756122362097, "duration": 13}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py\", line 37, in test_what_s_the_weather_like_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e4ffe1e9347db259", "name": "stdout", "source": "e4ffe1e9347db259.txt", "type": "text/plain", "size": 14088}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122362134, "stop": 1756122362391, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "f01b4dc529b14a5e", "name": "失败截图-TestEllaWhatSWeatherLikeShanghaiToday", "source": "f01b4dc529b14a5e.png", "type": "image/png", "size": 167498}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756122362393, "stop": 1756122363782, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_like_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "edeaea69f52e61b.json", "parameterValues": []}