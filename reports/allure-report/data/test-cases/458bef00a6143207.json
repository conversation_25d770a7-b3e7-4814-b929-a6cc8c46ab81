{"uid": "458bef00a6143207", "name": "测试Generate a picture in the night forest for me", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_picture_in_the_night_forest_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_picture_in_the_night_forest_for_me", "historyId": "5d0a6cda1787168fa2fdaaae1dee86f3", "time": {"start": 1756132117474, "stop": 1756132143074, "duration": 25600}, "description": "测试Generate a picture in the night forest for me指令", "descriptionHtml": "<p>测试Generate a picture in the night forest for me指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132104915, "stop": 1756132117473, "duration": 12558}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132117473, "stop": 1756132117473, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate a picture in the night forest for me指令", "status": "passed", "steps": [{"name": "执行命令: Generate a picture in the night forest for me", "time": {"start": 1756132117475, "stop": 1756132142848, "duration": 25373}, "status": "passed", "steps": [{"name": "执行命令: Generate a picture in the night forest for me", "time": {"start": 1756132117475, "stop": 1756132142595, "duration": 25120}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132142595, "stop": 1756132142848, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "f1ac2d95f7807ffa", "name": "测试总结", "source": "f1ac2d95f7807ffa.txt", "type": "text/plain", "size": 373}, {"uid": "e864d4ef16844e8c", "name": "test_completed", "source": "e864d4ef16844e8c.png", "type": "image/png", "size": 184092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132142848, "stop": 1756132142850, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132142850, "stop": 1756132143073, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "62eda43231434805", "name": "测试总结", "source": "62eda43231434805.txt", "type": "text/plain", "size": 373}, {"uid": "b2b20b673da841ba", "name": "test_completed", "source": "b2b20b673da841ba.png", "type": "image/png", "size": 184092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b42fb7f37dae4e3f", "name": "stdout", "source": "b42fb7f37dae4e3f.txt", "type": "text/plain", "size": 13247}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132143075, "stop": 1756132143075, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132143077, "stop": 1756132144455, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_picture_in_the_night_forest_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_picture_in_the_night_forest_for_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "458bef00a6143207.json", "parameterValues": []}