{"uid": "143dfc40aaa453d1", "name": "测试help me generate a picture of a puppy", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_puppy.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_a_puppy", "historyId": "89e187687dfa3317845107c44a62f287", "time": {"start": 1756132655313, "stop": 1756132681036, "duration": 25723}, "description": "测试help me generate a picture of a puppy指令", "descriptionHtml": "<p>测试help me generate a picture of a puppy指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132642614, "stop": 1756132655311, "duration": 12697}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132655311, "stop": 1756132655311, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试help me generate a picture of a puppy指令", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a puppy", "time": {"start": 1756132655313, "stop": 1756132680801, "duration": 25488}, "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a puppy", "time": {"start": 1756132655313, "stop": 1756132680536, "duration": 25223}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132680536, "stop": 1756132680801, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "f151edacdc76108b", "name": "测试总结", "source": "f151edacdc76108b.txt", "type": "text/plain", "size": 357}, {"uid": "e0d82c0d4ff85044", "name": "test_completed", "source": "e0d82c0d4ff85044.png", "type": "image/png", "size": 184236}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132680801, "stop": 1756132680803, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132680803, "stop": 1756132681035, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "91cb055ace4cfe45", "name": "测试总结", "source": "91cb055ace4cfe45.txt", "type": "text/plain", "size": 357}, {"uid": "c1d18f6484395a7f", "name": "test_completed", "source": "c1d18f6484395a7f.png", "type": "image/png", "size": 184236}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "75e0f7e91e3dca3f", "name": "stdout", "source": "75e0f7e91e3dca3f.txt", "type": "text/plain", "size": 13183}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132681037, "stop": 1756132681037, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132681038, "stop": 1756132682449, "duration": 1411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_a_puppy"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_puppy"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "143dfc40aaa453d1.json", "parameterValues": []}