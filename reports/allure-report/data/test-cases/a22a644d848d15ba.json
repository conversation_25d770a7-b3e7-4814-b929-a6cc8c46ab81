{"uid": "a22a644d848d15ba", "name": "测试i want make a video call to能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_i_want_make_a_video_call_to.TestEllaIWantMakeVideoCall#test_i_want_make_a_video_call_to", "historyId": "19fa56a92b4343c1894780564290d112", "time": {"start": 1756133254112, "stop": 1756133288817, "duration": 34705}, "description": "i want make a video call to", "descriptionHtml": "<p>i want make a video call to</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133240773, "stop": 1756133254111, "duration": 13338}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133254112, "stop": 1756133254112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "i want make a video call to", "status": "passed", "steps": [{"name": "执行命令: i want make a video call to", "time": {"start": 1756133254112, "stop": 1756133288569, "duration": 34457}, "status": "passed", "steps": [{"name": "执行命令: i want make a video call to", "time": {"start": 1756133254112, "stop": 1756133288305, "duration": 34193}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133288305, "stop": 1756133288568, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "2969da544411dc41", "name": "测试总结", "source": "2969da544411dc41.txt", "type": "text/plain", "size": 327}, {"uid": "db143cc75c5d3596", "name": "test_completed", "source": "db143cc75c5d3596.png", "type": "image/png", "size": 183732}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133288569, "stop": 1756133288572, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133288572, "stop": 1756133288817, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "f2b9489241b8fd18", "name": "测试总结", "source": "f2b9489241b8fd18.txt", "type": "text/plain", "size": 327}, {"uid": "6634532441b4b565", "name": "test_completed", "source": "6634532441b4b565.png", "type": "image/png", "size": 183737}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7348c74cf4e78aaa", "name": "stdout", "source": "7348c74cf4e78aaa.txt", "type": "text/plain", "size": 13122}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133288819, "stop": 1756133288819, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133288821, "stop": 1756133290219, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_want_make_a_video_call_to"}, {"name": "subSuite", "value": "TestEllaIWantMakeVideoCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_want_make_a_video_call_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a22a644d848d15ba.json", "parameterValues": []}