{"uid": "9a2244b25984e82c", "name": "测试open camera", "fullName": "testcases.test_ella.unsupported_commands.test_open_camera.TestEllaOpenPlayPoliticalNews#test_open_camera", "historyId": "6ba20fdcdbf83dd327ea91bd93e697e0", "time": {"start": 1756134550290, "stop": 1756134584263, "duration": 33973}, "description": "测试open camera指令", "descriptionHtml": "<p>测试open camera指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134537443, "stop": 1756134550289, "duration": 12846}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134550289, "stop": 1756134550289, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open camera指令", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1756134550290, "stop": 1756134583984, "duration": 33694}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1756134550290, "stop": 1756134583703, "duration": 33413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134583703, "stop": 1756134583983, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "f53b2859995b08f1", "name": "测试总结", "source": "f53b2859995b08f1.txt", "type": "text/plain", "size": 357}, {"uid": "63e166b5998f5aef", "name": "test_completed", "source": "63e166b5998f5aef.png", "type": "image/png", "size": 155754}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134583984, "stop": 1756134583988, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证camera已打开", "time": {"start": 1756134583988, "stop": 1756134583988, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134583988, "stop": 1756134584260, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "2f2e0072132f11f3", "name": "测试总结", "source": "2f2e0072132f11f3.txt", "type": "text/plain", "size": 357}, {"uid": "aeca807132157de8", "name": "test_completed", "source": "aeca807132157de8.png", "type": "image/png", "size": 155609}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8d1e6f2fba91ffe2", "name": "stdout", "source": "8d1e6f2fba91ffe2.txt", "type": "text/plain", "size": 15278}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134584265, "stop": 1756134584265, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134584267, "stop": 1756134585759, "duration": 1492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9a2244b25984e82c.json", "parameterValues": []}