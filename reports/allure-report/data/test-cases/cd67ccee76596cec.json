{"uid": "cd67ccee76596cec", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai.TestEllaNavigateFromBeijingShanghai#test_navigate_from_beijing_to_shanghai", "historyId": "f622c7c4831272dc58cb99e6af8d9943", "time": {"start": 1756128898376, "stop": 1756128932040, "duration": 33664}, "description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "descriptionHtml": "<p>navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128885572, "stop": 1756128898374, "duration": 12802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128898374, "stop": 1756128898374, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "time": {"start": 1756128898376, "stop": 1756128931793, "duration": 33417}, "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "time": {"start": 1756128898376, "stop": 1756128931498, "duration": 33122}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128931498, "stop": 1756128931793, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "728525aab4016b17", "name": "测试总结", "source": "728525aab4016b17.txt", "type": "text/plain", "size": 530}, {"uid": "8aa1e1f8d30c842d", "name": "test_completed", "source": "8aa1e1f8d30c842d.png", "type": "image/png", "size": 183429}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128931794, "stop": 1756128931796, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756128931796, "stop": 1756128931796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128931796, "stop": 1756128932039, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "eca1ab35366ed8f4", "name": "测试总结", "source": "eca1ab35366ed8f4.txt", "type": "text/plain", "size": 530}, {"uid": "76e3f584013f20a0", "name": "test_completed", "source": "76e3f584013f20a0.png", "type": "image/png", "size": 183429}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d2031f981b2c7546", "name": "stdout", "source": "d2031f981b2c7546.txt", "type": "text/plain", "size": 15938}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128932041, "stop": 1756128932041, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128932042, "stop": 1756128933446, "duration": 1404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_beijing_to_shanghai"}, {"name": "subSuite", "value": "TestEllaNavigateFromBeijingShanghai"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cd67ccee76596cec.json", "parameterValues": []}