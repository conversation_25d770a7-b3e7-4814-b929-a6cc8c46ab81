{"uid": "a6ab930d517dded3", "name": "测试max brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_max_brightness.TestEllaMaxBrightness#test_max_brightness", "historyId": "4b3ad3bdf0873599e48d8f20d70246c9", "time": {"start": 1756124835157, "stop": 1756124862471, "duration": 27314}, "description": "max brightness", "descriptionHtml": "<p>max brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124822245, "stop": 1756124835153, "duration": 12908}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124835153, "stop": 1756124835153, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "max brightness", "status": "passed", "steps": [{"name": "执行命令: max brightness", "time": {"start": 1756124835157, "stop": 1756124862216, "duration": 27059}, "status": "passed", "steps": [{"name": "执行命令: max brightness", "time": {"start": 1756124835157, "stop": 1756124861952, "duration": 26795}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124861952, "stop": 1756124862216, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "e0521395c5bca1d8", "name": "测试总结", "source": "e0521395c5bca1d8.txt", "type": "text/plain", "size": 202}, {"uid": "56263ff02e128fa1", "name": "test_completed", "source": "56263ff02e128fa1.png", "type": "image/png", "size": 152558}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124862216, "stop": 1756124862220, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124862220, "stop": 1756124862220, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124862220, "stop": 1756124862469, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "fdeeb04d390dac3c", "name": "测试总结", "source": "fdeeb04d390dac3c.txt", "type": "text/plain", "size": 202}, {"uid": "78bb4e10e5573642", "name": "test_completed", "source": "78bb4e10e5573642.png", "type": "image/png", "size": 152558}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d9a9376cdccde599", "name": "stdout", "source": "d9a9376cdccde599.txt", "type": "text/plain", "size": 13079}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124862472, "stop": 1756124862472, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124862473, "stop": 1756124863852, "duration": 1379}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_brightness"}, {"name": "subSuite", "value": "TestEllaMaxBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a6ab930d517dded3.json", "parameterValues": []}