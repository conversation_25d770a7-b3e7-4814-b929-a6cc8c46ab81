{"uid": "bae09a3b885bec90", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "fullName": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow.TestEllaWakeMeUpAmTomorrow#test_wake_me_up_at_am_tomorrow", "historyId": "********************************", "time": {"start": 1756128632042, "stop": 1756128657420, "duration": 25378}, "description": "wake me up at 7:00 am tomorrow", "descriptionHtml": "<p>wake me up at 7:00 am tomorrow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128619180, "stop": 1756128632041, "duration": 12861}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128632041, "stop": 1756128632041, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "wake me up at 7:00 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "time": {"start": 1756128632042, "stop": 1756128657203, "duration": 25161}, "status": "passed", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "time": {"start": 1756128632076, "stop": 1756128656940, "duration": 24864}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128656940, "stop": 1756128657202, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "525f6e47830a33e4", "name": "测试总结", "source": "525f6e47830a33e4.txt", "type": "text/plain", "size": 261}, {"uid": "5fabe17bd3ebd57c", "name": "test_completed", "source": "5fabe17bd3ebd57c.png", "type": "image/png", "size": 155367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128657203, "stop": 1756128657205, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128657205, "stop": 1756128657420, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "a5c8bb2521761bb7", "name": "测试总结", "source": "a5c8bb2521761bb7.txt", "type": "text/plain", "size": 261}, {"uid": "62324e600c57767", "name": "test_completed", "source": "62324e600c57767.png", "type": "image/png", "size": 155367}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "be227a3b3e4cc3e", "name": "stdout", "source": "be227a3b3e4cc3e.txt", "type": "text/plain", "size": 12669}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128657423, "stop": 1756128657423, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128657424, "stop": 1756128658823, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_wake_me_up_at_am_tomorrow"}, {"name": "subSuite", "value": "TestEllaWakeMeUpAmTomorrow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bae09a3b885bec90.json", "parameterValues": []}