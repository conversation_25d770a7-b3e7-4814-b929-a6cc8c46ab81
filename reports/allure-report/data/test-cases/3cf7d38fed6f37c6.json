{"uid": "3cf7d38fed6f37c6", "name": "测试enable unfreeze返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_unfreeze.TestEllaEnableUnfreeze#test_enable_unfreeze", "historyId": "afa6af304cfb25a990764680de5fa777", "time": {"start": 1756131835945, "stop": 1756131862470, "duration": 26525}, "description": "验证enable unfreeze指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable unfreeze指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131823063, "stop": 1756131835944, "duration": 12881}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131835945, "stop": 1756131835945, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable unfreeze指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "time": {"start": 1756131835946, "stop": 1756131862254, "duration": 26308}, "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "time": {"start": 1756131835946, "stop": 1756131862001, "duration": 26055}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131862001, "stop": 1756131862254, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "c1eb935179db18d", "name": "测试总结", "source": "c1eb935179db18d.txt", "type": "text/plain", "size": 328}, {"uid": "c2e7f9d20bb37b30", "name": "test_completed", "source": "c2e7f9d20bb37b30.png", "type": "image/png", "size": 187090}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131862254, "stop": 1756131862256, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131862256, "stop": 1756131862470, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "fcdfda4aa51051f2", "name": "测试总结", "source": "fcdfda4aa51051f2.txt", "type": "text/plain", "size": 328}, {"uid": "52cb9f14963fe8ad", "name": "test_completed", "source": "52cb9f14963fe8ad.png", "type": "image/png", "size": 187090}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "dc55801325de4c86", "name": "stdout", "source": "dc55801325de4c86.txt", "type": "text/plain", "size": 12543}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131862470, "stop": 1756131862470, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131862471, "stop": 1756131863893, "duration": 1422}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_unfreeze"}, {"name": "subSuite", "value": "TestEllaEnableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_unfreeze"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3cf7d38fed6f37c6.json", "parameterValues": []}