{"uid": "bee46aae72a0627", "name": "测试remove alarms能正常执行", "fullName": "testcases.test_ella.dialogue.test_remove_alarms.TestEllaHowIsWeatherToday#test_remove_alarms", "historyId": "d28e2beb1494e42c051e9b99add608fb", "time": {"start": 1756121200927, "stop": 1756121280122, "duration": 79195}, "description": "remove alarms", "descriptionHtml": "<p>remove alarms</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121188069, "stop": 1756121200926, "duration": 12857}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121200926, "stop": 1756121200926, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "remove alarms", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756121200927, "stop": 1756121226447, "duration": 25520}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756121200927, "stop": 1756121226167, "duration": 25240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121226167, "stop": 1756121226447, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "742468f63ce39700", "name": "测试总结", "source": "742468f63ce39700.txt", "type": "text/plain", "size": 241}, {"uid": "3a45ac41f30ee0d3", "name": "test_completed", "source": "3a45ac41f30ee0d3.png", "type": "image/png", "size": 160250}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: remove alarms", "time": {"start": 1756121226447, "stop": 1756121252971, "duration": 26524}, "status": "passed", "steps": [{"name": "执行命令: remove alarms", "time": {"start": 1756121226447, "stop": 1756121252722, "duration": 26275}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121252722, "stop": 1756121252971, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "1ae7cf600f05015f", "name": "测试总结", "source": "1ae7cf600f05015f.txt", "type": "text/plain", "size": 237}, {"uid": "91db81e94eec2c1e", "name": "test_completed", "source": "91db81e94eec2c1e.png", "type": "image/png", "size": 152352}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756121252971, "stop": 1756121279887, "duration": 26916}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756121252971, "stop": 1756121279618, "duration": 26647}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121279618, "stop": 1756121279886, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "3bd292a99265e640", "name": "测试总结", "source": "3bd292a99265e640.txt", "type": "text/plain", "size": 240}, {"uid": "2f94d96b01283b5", "name": "test_completed", "source": "2f94d96b01283b5.png", "type": "image/png", "size": 154070}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121279887, "stop": 1756121279889, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121279889, "stop": 1756121280121, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "7221a7381c1b4aa0", "name": "测试总结", "source": "7221a7381c1b4aa0.txt", "type": "text/plain", "size": 235}, {"uid": "5db44b2ac7b26142", "name": "test_completed", "source": "5db44b2ac7b26142.png", "type": "image/png", "size": 154070}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "90ccff00e6fcb219", "name": "stdout", "source": "90ccff00e6fcb219.txt", "type": "text/plain", "size": 31355}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 11, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121280122, "stop": 1756121280122, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121280123, "stop": 1756121281494, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_remove_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_remove_alarms"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bee46aae72a0627.json", "parameterValues": []}