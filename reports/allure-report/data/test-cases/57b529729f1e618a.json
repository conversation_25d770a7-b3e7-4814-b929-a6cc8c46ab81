{"uid": "57b529729f1e618a", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings.TestEllaOpenSettings#test_jump_to_lock_screen_notification_and_display_settings", "historyId": "fca782bf64e9cf595a09003471d4cc31", "time": {"start": 1756133862567, "stop": 1756133896334, "duration": 33767}, "description": "验证jump to lock screen notification and display settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to lock screen notification and display settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to lock screen notification and display settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Lock Screen | What to Show | Lock Screen Notification Display Settings | Show Notification Content | Add Text on Lock Screen | None | Use Device Controls | Control external devices without unlocking your phone or tablet if allowed by the device controls app. | Shortcuts']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings.TestEllaOpenSettings object at 0x00000292053B2250>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209707650>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_lock_screen_notification_and_display_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to lock screen notification and display settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Lock Screen | What to Show | Lock Screen Notification Display Settings | Show Notification Content | Add Text on Lock Screen | None | Use Device Controls | Control external devices without unlocking your phone or tablet if allowed by the device controls app. | Shortcuts']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_lock_screen_notification_and_display_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133849593, "stop": 1756133862567, "duration": 12974}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133862567, "stop": 1756133862567, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to lock screen notification and display settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to lock screen notification and display settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Lock Screen | What to Show | Lock Screen Notification Display Settings | Show Notification Content | Add Text on Lock Screen | None | Use Device Controls | Control external devices without unlocking your phone or tablet if allowed by the device controls app. | Shortcuts']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings.TestEllaOpenSettings object at 0x00000292053B2250>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209707650>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_lock_screen_notification_and_display_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to lock screen notification and display settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Lock Screen | What to Show | Lock Screen Notification Display Settings | Show Notification Content | Add Text on Lock Screen | None | Use Device Controls | Control external devices without unlocking your phone or tablet if allowed by the device controls app. | Shortcuts']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_lock_screen_notification_and_display_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "time": {"start": 1756133862568, "stop": 1756133896330, "duration": 33762}, "status": "passed", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "time": {"start": 1756133862568, "stop": 1756133896055, "duration": 33487}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133896055, "stop": 1756133896330, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "6fd40238a4c05f57", "name": "测试总结", "source": "6fd40238a4c05f57.txt", "type": "text/plain", "size": 651}, {"uid": "14b18628a2f3b63c", "name": "test_completed", "source": "14b18628a2f3b63c.png", "type": "image/png", "size": 183934}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133896330, "stop": 1756133896333, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to lock screen notification and display settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Lock Screen | What to Show | Lock Screen Notification Display Settings | Show Notification Content | Add Text on Lock Screen | None | Use Device Controls | Control external devices without unlocking your phone or tablet if allowed by the device controls app. | Shortcuts']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_lock_screen_notification_and_display_settings.py\", line 33, in test_jump_to_lock_screen_notification_and_display_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "30acef2c963842c3", "name": "stdout", "source": "30acef2c963842c3.txt", "type": "text/plain", "size": 17076}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133896339, "stop": 1756133896571, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "167afedfa327760c", "name": "失败截图-TestEllaOpenSettings", "source": "167afedfa327760c.png", "type": "image/png", "size": 183919}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133896573, "stop": 1756133898038, "duration": 1465}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_lock_screen_notification_and_display_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "57b529729f1e618a.json", "parameterValues": []}