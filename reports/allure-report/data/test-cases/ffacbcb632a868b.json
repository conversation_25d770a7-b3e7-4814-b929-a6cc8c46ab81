{"uid": "ffacbcb632a868b", "name": "测试navigate to shanghai disneyland能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_to_shanghai_disneyland.TestEllaNavigateShanghaiDisneyland#test_navigate_to_shanghai_disneyland", "historyId": "039e454ca4c329751543f1bfbb5e008e", "time": {"start": 1756128993919, "stop": 1756129027681, "duration": 33762}, "description": "navigate to shanghai disneyland", "descriptionHtml": "<p>navigate to shanghai disneyland</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128981376, "stop": 1756128993918, "duration": 12542}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128993918, "stop": 1756128993918, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigate to shanghai disneyland", "status": "passed", "steps": [{"name": "执行命令: navigate to shanghai disneyland", "time": {"start": 1756128993919, "stop": 1756129027434, "duration": 33515}, "status": "passed", "steps": [{"name": "执行命令: navigate to shanghai disneyland", "time": {"start": 1756128993919, "stop": 1756129027158, "duration": 33239}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129027158, "stop": 1756129027433, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "e0640855c62c0ab", "name": "测试总结", "source": "e0640855c62c0ab.txt", "type": "text/plain", "size": 444}, {"uid": "e0b1d8aa580bdc25", "name": "test_completed", "source": "e0b1d8aa580bdc25.png", "type": "image/png", "size": 163306}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129027434, "stop": 1756129027437, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756129027437, "stop": 1756129027437, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129027437, "stop": 1756129027679, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "10c4a9ed12647482", "name": "测试总结", "source": "10c4a9ed12647482.txt", "type": "text/plain", "size": 444}, {"uid": "95da35b858d091c8", "name": "test_completed", "source": "95da35b858d091c8.png", "type": "image/png", "size": 163266}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1d03cb8c090bcf9c", "name": "stdout", "source": "1d03cb8c090bcf9c.txt", "type": "text/plain", "size": 15419}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129027682, "stop": 1756129027682, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129027683, "stop": 1756129029114, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_to_shanghai_disneyland"}, {"name": "subSuite", "value": "TestEllaNavigateShanghaiDisneyland"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_to_shanghai_disneyland"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ffacbcb632a868b.json", "parameterValues": []}