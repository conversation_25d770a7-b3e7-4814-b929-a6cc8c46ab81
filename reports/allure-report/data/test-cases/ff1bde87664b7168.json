{"uid": "ff1bde87664b7168", "name": "测试cannot login in google email box能正常执行", "fullName": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box.TestEllaCannotLoginGoogleEmailBox#test_cannot_login_in_google_email_box", "historyId": "********************************", "time": {"start": 1756119437629, "stop": 1756119462733, "duration": 25104}, "description": "cannot login in google email box", "descriptionHtml": "<p>cannot login in google email box</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119424884, "stop": 1756119437627, "duration": 12743}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119437627, "stop": 1756119437627, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "cannot login in google email box", "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1756119437631, "stop": 1756119462486, "duration": 24855}, "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1756119437631, "stop": 1756119462254, "duration": 24623}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119462254, "stop": 1756119462486, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "263c7a56ad31b3e2", "name": "测试总结", "source": "263c7a56ad31b3e2.txt", "type": "text/plain", "size": 369}, {"uid": "ae35b1692dafc837", "name": "test_completed", "source": "ae35b1692dafc837.png", "type": "image/png", "size": 167868}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119462486, "stop": 1756119462488, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119462488, "stop": 1756119462732, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "a1f5ae186c03242a", "name": "测试总结", "source": "a1f5ae186c03242a.txt", "type": "text/plain", "size": 369}, {"uid": "25ddafe2cab2ecd5", "name": "test_completed", "source": "25ddafe2cab2ecd5.png", "type": "image/png", "size": 167868}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "13f7875ece4a95d7", "name": "stdout", "source": "13f7875ece4a95d7.txt", "type": "text/plain", "size": 12806}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119462733, "stop": 1756119462733, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119462735, "stop": 1756119464046, "duration": 1311}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_cannot_login_in_google_email_box"}, {"name": "subSuite", "value": "TestEllaCannotLoginGoogleEmailBox"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ff1bde87664b7168.json", "parameterValues": []}