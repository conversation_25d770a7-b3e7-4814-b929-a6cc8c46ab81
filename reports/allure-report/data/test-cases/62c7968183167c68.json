{"uid": "62c7968183167c68", "name": "测试take a joke能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_joke.TestEllaTakeJoke#test_take_a_joke", "historyId": "543965b4120af95548616c95b1b70ef1", "time": {"start": 1756121864072, "stop": 1756121891638, "duration": 27566}, "description": "take a joke", "descriptionHtml": "<p>take a joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121851027, "stop": 1756121864070, "duration": 13043}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121864070, "stop": 1756121864070, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take a joke", "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1756121864072, "stop": 1756121891374, "duration": 27302}, "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1756121864072, "stop": 1756121891098, "duration": 27026}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121891098, "stop": 1756121891373, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "12b98fe6dbade8bc", "name": "测试总结", "source": "12b98fe6dbade8bc.txt", "type": "text/plain", "size": 703}, {"uid": "d703329425d19b0", "name": "test_completed", "source": "d703329425d19b0.png", "type": "image/png", "size": 181487}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121891374, "stop": 1756121891382, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121891382, "stop": 1756121891637, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "2b7cee3f84510ace", "name": "测试总结", "source": "2b7cee3f84510ace.txt", "type": "text/plain", "size": 703}, {"uid": "8f7d732f72838355", "name": "test_completed", "source": "8f7d732f72838355.png", "type": "image/png", "size": 181477}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e2bdd492d10b225e", "name": "stdout", "source": "e2bdd492d10b225e.txt", "type": "text/plain", "size": 15070}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121891639, "stop": 1756121891639, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121891641, "stop": 1756121893100, "duration": 1459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_joke"}, {"name": "subSuite", "value": "TestEllaTakeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "62c7968183167c68.json", "parameterValues": []}