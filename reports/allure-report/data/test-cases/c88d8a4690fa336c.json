{"uid": "c88d8a4690fa336c", "name": "测试tell me a joke能正常执行", "fullName": "testcases.test_ella.dialogue.test_tell_me_a_joke.TestEllaTellMeJoke#test_tell_me_a_joke", "historyId": "3dae350db69abded432b3e7f5f8463c8", "time": {"start": 1756121991906, "stop": 1756122019921, "duration": 28015}, "description": "tell me a joke", "descriptionHtml": "<p>tell me a joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121978923, "stop": 1756121991903, "duration": 12980}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121991903, "stop": 1756121991904, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "tell me a joke", "status": "passed", "steps": [{"name": "执行命令: tell me a joke", "time": {"start": 1756121991906, "stop": 1756122019659, "duration": 27753}, "status": "passed", "steps": [{"name": "执行命令: tell me a joke", "time": {"start": 1756121991906, "stop": 1756122019379, "duration": 27473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122019379, "stop": 1756122019659, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "805bc01f0559fca1", "name": "测试总结", "source": "805bc01f0559fca1.txt", "type": "text/plain", "size": 836}, {"uid": "27eee81c80c47d96", "name": "test_completed", "source": "27eee81c80c47d96.png", "type": "image/png", "size": 185477}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122019659, "stop": 1756122019665, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122019665, "stop": 1756122019920, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "4ee7e59fe70ab51a", "name": "测试总结", "source": "4ee7e59fe70ab51a.txt", "type": "text/plain", "size": 836}, {"uid": "ec967917fb8a7c56", "name": "test_completed", "source": "ec967917fb8a7c56.png", "type": "image/png", "size": 185277}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "398e49d6ca904b6d", "name": "stdout", "source": "398e49d6ca904b6d.txt", "type": "text/plain", "size": 15177}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122019922, "stop": 1756122019923, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122019925, "stop": 1756122021335, "duration": 1410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_tell_me_a_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_tell_me_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c88d8a4690fa336c.json", "parameterValues": []}