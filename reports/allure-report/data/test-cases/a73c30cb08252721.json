{"uid": "a73c30cb08252721", "name": "测试set lockscreen passwords返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords#test_set_lockscreen_passwords", "historyId": "89b134ac1374e88187e793daf9f8fcab", "time": {"start": *************, "stop": 1756136790295, "duration": 37951}, "description": "验证set lockscreen passwords指令返回预期的不支持响应", "descriptionHtml": "<p>验证set lockscreen passwords指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Password & Biometrics | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Security Status | Google Play Protect | Find My Device | No Google account on this device | Security update | May 5, 2025 | Security App | App Lock | Mobile Anti-Theft | Emergency']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords object at 0x0000029205714950>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8FA290>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_lockscreen_passwords(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Password & Biometrics | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Security Status | Google Play Protect | Find My Device | No Google account on this device | Security update | May 5, 2025 | Security App | App Lock | Mobile Anti-Theft | Emergency']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_lockscreen_passwords.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": *************, "stop": *************, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": *************, "stop": *************, "duration": 12669}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set lockscreen passwords指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Password & Biometrics | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Security Status | Google Play Protect | Find My Device | No Google account on this device | Security update | May 5, 2025 | Security App | App Lock | Mobile Anti-Theft | Emergency']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords object at 0x0000029205714950>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A8FA290>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_lockscreen_passwords(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Password & Biometrics | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Security Status | Google Play Protect | Find My Device | No Google account on this device | Security update | May 5, 2025 | Security App | App Lock | Mobile Anti-Theft | Emergency']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_lockscreen_passwords.py:33: AssertionError", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": *************, "stop": *************, "duration": 37947}, "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": *************, "stop": *************, "duration": 37634}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": *************, "stop": *************, "duration": 312}, "status": "passed", "steps": [], "attachments": [{"uid": "7c092aefd3cfb43a", "name": "测试总结", "source": "7c092aefd3cfb43a.txt", "type": "text/plain", "size": 643}, {"uid": "2f7da369d25a5155", "name": "test_completed", "source": "2f7da369d25a5155.png", "type": "image/png", "size": 175971}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": *************, "stop": 1756136790294, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Password & Biometrics | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Security Status | Google Play Protect | Find My Device | No Google account on this device | Security update | May 5, 2025 | Security App | App Lock | Mobile Anti-Theft | Emergency']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_lockscreen_passwords.py\", line 33, in test_set_lockscreen_passwords\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b7bfc588f62c1835", "name": "stdout", "source": "b7bfc588f62c1835.txt", "type": "text/plain", "size": 16429}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": *************, "stop": *************, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "1739296aa9e805e", "name": "失败截图-TestEllaSetLockscreenPasswords", "source": "1739296aa9e805e.png", "type": "image/png", "size": 175981}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136790559, "stop": 1756136792060, "duration": 1501}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_lockscreen_passwords"}, {"name": "subSuite", "value": "TestEllaSetLockscreenPasswords"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "a73c30cb08252721.json", "parameterValues": []}