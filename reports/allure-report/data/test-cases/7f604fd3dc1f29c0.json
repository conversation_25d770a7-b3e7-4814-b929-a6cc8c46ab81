{"uid": "7f604fd3dc1f29c0", "name": "测试switch to smart charge能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "historyId": "643a7bbfbf5c5eedbae7ae814fbc8b52", "time": {"start": 1756126927499, "stop": 1756126953725, "duration": 26226}, "description": "switch to smart charge", "descriptionHtml": "<p>switch to smart charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126914718, "stop": 1756126927498, "duration": 12780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126927498, "stop": 1756126927498, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch to smart charge", "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "time": {"start": 1756126927499, "stop": 1756126953482, "duration": 25983}, "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "time": {"start": 1756126927499, "stop": 1756126953213, "duration": 25714}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126953213, "stop": 1756126953481, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "9660410f4e2852a5", "name": "测试总结", "source": "9660410f4e2852a5.txt", "type": "text/plain", "size": 397}, {"uid": "d9da020525ade901", "name": "test_completed", "source": "d9da020525ade901.png", "type": "image/png", "size": 202938}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126953482, "stop": 1756126953483, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126953483, "stop": 1756126953725, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "37bf60c4558cc44e", "name": "测试总结", "source": "37bf60c4558cc44e.txt", "type": "text/plain", "size": 397}, {"uid": "57d5985a8ad16ae2", "name": "test_completed", "source": "57d5985a8ad16ae2.png", "type": "image/png", "size": 202938}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ebe88f8f28408459", "name": "stdout", "source": "ebe88f8f28408459.txt", "type": "text/plain", "size": 13007}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126953726, "stop": 1756126953726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126953727, "stop": 1756126955144, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_smart_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7f604fd3dc1f29c0.json", "parameterValues": []}