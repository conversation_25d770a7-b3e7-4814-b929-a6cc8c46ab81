{"uid": "f5930675a7a680f9", "name": "测试set split-screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps#test_set_split_screen_apps", "historyId": "b5e1711cce3102fc710ff74e18bf9129", "time": {"start": 1756137618657, "stop": 1756137650614, "duration": 31957}, "description": "验证set split-screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set split-screen apps指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set split-screen apps', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.ossettingsext页面内容] Split-screen Apps | Split Screen with 2 Fingers | Swipe with 2 fingers in apps that support split-screen to split the screen. | Split Screen with 2 Fingers | Swipe horizontally (vertically in landscape mode) with 2 fingers in the middle of the screen to split it.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps object at 0x00000292057F1110>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097D1610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_split_screen_apps(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set split-screen apps', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.ossettingsext页面内容] Split-screen Apps | Split Screen with 2 Fingers | Swipe with 2 fingers in apps that support split-screen to split the screen. | Split Screen with 2 Fingers | Swipe horizontally (vertically in landscape mode) with 2 fingers in the middle of the screen to split it.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_split_screen_apps.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137605415, "stop": 1756137618655, "duration": 13240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137618655, "stop": 1756137618655, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set split-screen apps指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set split-screen apps', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.ossettingsext页面内容] Split-screen Apps | Split Screen with 2 Fingers | Swipe with 2 fingers in apps that support split-screen to split the screen. | Split Screen with 2 Fingers | Swipe horizontally (vertically in landscape mode) with 2 fingers in the middle of the screen to split it.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps object at 0x00000292057F1110>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292097D1610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_split_screen_apps(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set split-screen apps', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.ossettingsext页面内容] Split-screen Apps | Split Screen with 2 Fingers | Swipe with 2 fingers in apps that support split-screen to split the screen. | Split Screen with 2 Fingers | Swipe horizontally (vertically in landscape mode) with 2 fingers in the middle of the screen to split it.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_split_screen_apps.py:33: AssertionError", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1756137618657, "stop": 1756137650609, "duration": 31952}, "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1756137618657, "stop": 1756137650329, "duration": 31672}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137650329, "stop": 1756137650609, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "20d258ca89309ec2", "name": "测试总结", "source": "20d258ca89309ec2.txt", "type": "text/plain", "size": 587}, {"uid": "3d1634006b94f52", "name": "test_completed", "source": "3d1634006b94f52.png", "type": "image/png", "size": 164969}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137650609, "stop": 1756137650613, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set split-screen apps', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.ossettingsext页面内容] Split-screen Apps | Split Screen with 2 Fingers | Swipe with 2 fingers in apps that support split-screen to split the screen. | Split Screen with 2 Fingers | Swipe horizontally (vertically in landscape mode) with 2 fingers in the middle of the screen to split it.']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_split_screen_apps.py\", line 33, in test_set_split_screen_apps\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ac25a65aacc1cbe4", "name": "stdout", "source": "ac25a65aacc1cbe4.txt", "type": "text/plain", "size": 16311}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137650618, "stop": 1756137650854, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "3bc6dab2d16b0815", "name": "失败截图-TestEllaSetSplitScreenApps", "source": "3bc6dab2d16b0815.png", "type": "image/png", "size": 165028}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137650855, "stop": 1756137652342, "duration": 1487}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_split_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetSplitScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "f5930675a7a680f9.json", "parameterValues": []}