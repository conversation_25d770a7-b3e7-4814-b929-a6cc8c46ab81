{"uid": "c52aeec7f8e8e93d", "name": "测试disable call rejection返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_call_rejection.TestEllaDisableCallRejection#test_disable_call_rejection", "historyId": "1ca8d9c300d55584cdcf637ede08bdba", "time": {"start": 1756130894816, "stop": 1756130937903, "duration": 43087}, "description": "验证disable call rejection指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable call rejection指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130881863, "stop": 1756130894813, "duration": 12950}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130894813, "stop": 1756130894813, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证disable call rejection指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable call rejection", "time": {"start": 1756130894816, "stop": 1756130937644, "duration": 42828}, "status": "passed", "steps": [{"name": "执行命令: disable call rejection", "time": {"start": 1756130894816, "stop": 1756130937345, "duration": 42529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130937345, "stop": 1756130937644, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "a74a5a007ea223b7", "name": "测试总结", "source": "a74a5a007ea223b7.txt", "type": "text/plain", "size": 558}, {"uid": "b73f2bd5f649074", "name": "test_completed", "source": "b73f2bd5f649074.png", "type": "image/png", "size": 171484}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756130937644, "stop": 1756130937647, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130937647, "stop": 1756130937902, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "872f096736d23325", "name": "测试总结", "source": "872f096736d23325.txt", "type": "text/plain", "size": 558}, {"uid": "bb5a4cd8b08a6163", "name": "test_completed", "source": "bb5a4cd8b08a6163.png", "type": "image/png", "size": 171639}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6e9d500085f6f462", "name": "stdout", "source": "6e9d500085f6f462.txt", "type": "text/plain", "size": 15503}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130937903, "stop": 1756130937903, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130937904, "stop": 1756130939347, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_call_rejection"}, {"name": "subSuite", "value": "TestEllaDisableCallRejection"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_call_rejection"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c52aeec7f8e8e93d.json", "parameterValues": []}