{"uid": "1985168dcbfbaaac", "name": "测试long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_long_screenshot.TestEllaLongScreenshot#test_long_screenshot", "historyId": "6f7052acfdd45e34e5dded44ad87416e", "time": {"start": 1756124711192, "stop": 1756124739676, "duration": 28484}, "description": "long screenshot", "descriptionHtml": "<p>long screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124698438, "stop": 1756124711191, "duration": 12753}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124711191, "stop": 1756124711191, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "long screenshot", "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1756124711192, "stop": 1756124739428, "duration": 28236}, "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1756124711192, "stop": 1756124739130, "duration": 27938}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124739130, "stop": 1756124739427, "duration": 297}, "status": "passed", "steps": [], "attachments": [{"uid": "13e4c9fcff1d6513", "name": "测试总结", "source": "13e4c9fcff1d6513.txt", "type": "text/plain", "size": 578}, {"uid": "42ca68afdfb4c00e", "name": "test_completed", "source": "42ca68afdfb4c00e.png", "type": "image/png", "size": 166074}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证文件存在", "time": {"start": 1756124739428, "stop": 1756124739428, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124739428, "stop": 1756124739675, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "ff0fcfe7ca772bbc", "name": "测试总结", "source": "ff0fcfe7ca772bbc.txt", "type": "text/plain", "size": 578}, {"uid": "e2bc4d4df9eddda7", "name": "test_completed", "source": "e2bc4d4df9eddda7.png", "type": "image/png", "size": 166449}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eda2c1b134208272", "name": "stdout", "source": "eda2c1b134208272.txt", "type": "text/plain", "size": 14813}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124739676, "stop": 1756124739676, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124739677, "stop": 1756124741091, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_long_screenshot"}, {"name": "subSuite", "value": "TestEllaLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1985168dcbfbaaac.json", "parameterValues": []}