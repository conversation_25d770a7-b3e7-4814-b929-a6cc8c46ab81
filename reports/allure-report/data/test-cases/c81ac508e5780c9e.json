{"uid": "c81ac508e5780c9e", "name": "测试puppy能正常执行", "fullName": "testcases.test_ella.self_function.test_puppy.TestEllaPuppy#test_puppy", "historyId": "359341836df6d43ec99426e6918dc6ca", "time": {"start": 1756123222378, "stop": 1756123305607, "duration": 83229}, "description": "puppy", "descriptionHtml": "<p>puppy</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123209782, "stop": 1756123222377, "duration": 12595}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123222377, "stop": 1756123222377, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "puppy", "status": "passed", "steps": [{"name": "执行命令: puppy", "time": {"start": 1756123222378, "stop": 1756123305210, "duration": 82832}, "status": "passed", "steps": [{"name": "执行命令: puppy", "time": {"start": 1756123222378, "stop": 1756123304805, "duration": 82427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123304805, "stop": 1756123305209, "duration": 404}, "status": "passed", "steps": [], "attachments": [{"uid": "fecae2c10ebb1b13", "name": "测试总结", "source": "fecae2c10ebb1b13.txt", "type": "text/plain", "size": 323}, {"uid": "ddeea0b9643e18e8", "name": "test_completed", "source": "ddeea0b9643e18e8.png", "type": "image/png", "size": 618821}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123305210, "stop": 1756123305211, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123305211, "stop": 1756123305607, "duration": 396}, "status": "passed", "steps": [], "attachments": [{"uid": "c5751a8e1537ac9", "name": "测试总结", "source": "c5751a8e1537ac9.txt", "type": "text/plain", "size": 266}, {"uid": "2896c1e34e91da1d", "name": "test_completed", "source": "2896c1e34e91da1d.png", "type": "image/png", "size": 619066}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "793a1d0d77a24f3f", "name": "stdout", "source": "793a1d0d77a24f3f.txt", "type": "text/plain", "size": 16105}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756123305608, "stop": 1756123306988, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756123305608, "stop": 1756123305608, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_puppy"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_puppy"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c81ac508e5780c9e.json", "parameterValues": []}