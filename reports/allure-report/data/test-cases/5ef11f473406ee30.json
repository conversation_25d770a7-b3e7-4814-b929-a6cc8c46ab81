{"uid": "5ef11f473406ee30", "name": "测试play carpenters'video", "fullName": "testcases.test_ella.unsupported_commands.test_play_carpenters_video.TestEllaOpenPlayPoliticalNews#test_play_carpenters_video", "historyId": "36066a5bbb1f962a6ad5842baefe4ff3", "time": {"start": 1756135016172, "stop": 1756135048147, "duration": 31975}, "description": "测试play carpenters'video指令", "descriptionHtml": "<p>测试play carpenters'video指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135003696, "stop": 1756135016169, "duration": 12473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135016169, "stop": 1756135016169, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play carpenters'video指令", "status": "passed", "steps": [{"name": "执行命令: play carpenters'video", "time": {"start": 1756135016173, "stop": 1756135047909, "duration": 31736}, "status": "passed", "steps": [{"name": "执行命令: play carpenters'video", "time": {"start": 1756135016173, "stop": 1756135047635, "duration": 31462}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135047635, "stop": 1756135047908, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "a82436cf2ad5389f", "name": "测试总结", "source": "a82436cf2ad5389f.txt", "type": "text/plain", "size": 352}, {"uid": "a1e1cd02a806cf07", "name": "test_completed", "source": "a1e1cd02a806cf07.png", "type": "image/png", "size": 170638}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135047909, "stop": 1756135047910, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证youtube已打开", "time": {"start": 1756135047910, "stop": 1756135047910, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135047910, "stop": 1756135048146, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "e419cbbc8a214a5b", "name": "测试总结", "source": "e419cbbc8a214a5b.txt", "type": "text/plain", "size": 352}, {"uid": "bd292f209ef42905", "name": "test_completed", "source": "bd292f209ef42905.png", "type": "image/png", "size": 170604}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "dfa6b60ae9e39edf", "name": "stdout", "source": "dfa6b60ae9e39edf.txt", "type": "text/plain", "size": 15371}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135048148, "stop": 1756135048148, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135048153, "stop": 1756135049643, "duration": 1490}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_carpenters_video"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_carpenters_video"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5ef11f473406ee30.json", "parameterValues": []}