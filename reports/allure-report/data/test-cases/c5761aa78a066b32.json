{"uid": "c5761aa78a066b32", "name": "测试check contact能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_check_contact.TestEllaCheckContact#test_check_contact", "historyId": "9375a7a6d7b67755564dec18857d7c65", "time": {"start": 1756130104443, "stop": 1756130138656, "duration": 34213}, "description": "check contact", "descriptionHtml": "<p>check contact</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130091881, "stop": 1756130104440, "duration": 12559}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130104441, "stop": 1756130104441, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "check contact", "status": "passed", "steps": [{"name": "执行命令: check contact", "time": {"start": 1756130104443, "stop": 1756130138435, "duration": 33992}, "status": "passed", "steps": [{"name": "执行命令: check contact", "time": {"start": 1756130104443, "stop": 1756130138148, "duration": 33705}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130138148, "stop": 1756130138434, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "42419bce58ebd4f3", "name": "测试总结", "source": "42419bce58ebd4f3.txt", "type": "text/plain", "size": 284}, {"uid": "9c1649d89e454c93", "name": "test_completed", "source": "9c1649d89e454c93.png", "type": "image/png", "size": 177298}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130138435, "stop": 1756130138437, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130138437, "stop": 1756130138656, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "3f3459f6c4f03043", "name": "测试总结", "source": "3f3459f6c4f03043.txt", "type": "text/plain", "size": 284}, {"uid": "1715402435a5a448", "name": "test_completed", "source": "1715402435a5a448.png", "type": "image/png", "size": 177298}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b8174af0f336efb5", "name": "stdout", "source": "b8174af0f336efb5.txt", "type": "text/plain", "size": 12820}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130138657, "stop": 1756130138657, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130138660, "stop": 1756130140030, "duration": 1370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_contact"}, {"name": "subSuite", "value": "TestEllaCheckContact"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_contact"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c5761aa78a066b32.json", "parameterValues": []}