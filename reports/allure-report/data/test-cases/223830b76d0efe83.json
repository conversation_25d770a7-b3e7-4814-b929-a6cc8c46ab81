{"uid": "223830b76d0efe83", "name": "测试driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_driving_mode.TestEllaDrivingMode#test_driving_mode", "historyId": "3215de286c6ddd59d6e52a44f2a9967d", "time": {"start": 1756131383782, "stop": 1756131409632, "duration": 25850}, "description": "验证driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131370916, "stop": 1756131383779, "duration": 12863}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131383779, "stop": 1756131383779, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1756131383782, "stop": 1756131409373, "duration": 25591}, "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1756131383782, "stop": 1756131409095, "duration": 25313}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131409095, "stop": 1756131409371, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "42b297d7d9c0b6d9", "name": "测试总结", "source": "42b297d7d9c0b6d9.txt", "type": "text/plain", "size": 323}, {"uid": "a5f18ab255b610d8", "name": "test_completed", "source": "a5f18ab255b610d8.png", "type": "image/png", "size": 186568}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131409373, "stop": 1756131409374, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131409374, "stop": 1756131409632, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "686c2f1cf8a2b30d", "name": "测试总结", "source": "686c2f1cf8a2b30d.txt", "type": "text/plain", "size": 323}, {"uid": "97b72df848780e1b", "name": "test_completed", "source": "97b72df848780e1b.png", "type": "image/png", "size": 186568}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "aa00eeb9334d55d7", "name": "stdout", "source": "aa00eeb9334d55d7.txt", "type": "text/plain", "size": 12513}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131409634, "stop": 1756131409634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131409636, "stop": 1756131411048, "duration": 1412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_driving_mode"}, {"name": "subSuite", "value": "TestEllaDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "223830b76d0efe83.json", "parameterValues": []}