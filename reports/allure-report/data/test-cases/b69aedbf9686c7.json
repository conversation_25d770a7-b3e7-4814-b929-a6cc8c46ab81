{"uid": "b69aedbf9686c7", "name": "测试A sports car is parked on the street side", "fullName": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side.TestEllaOpenPlayPoliticalNews#test_a_sports_car_is_parked_on_the_street_side", "historyId": "b6eee20d1fad16a048ce8490d7189be4", "time": {"start": 1756129788689, "stop": 1756129818308, "duration": 29619}, "description": "测试A sports car is parked on the street side 指令", "descriptionHtml": "<p>测试A sports car is parked on the street side 指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129775566, "stop": 1756129788688, "duration": 13122}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129788688, "stop": 1756129788688, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试A sports car is parked on the street side 指令", "status": "passed", "steps": [{"name": "执行命令: A sports car is parked on the street side", "time": {"start": 1756129788689, "stop": 1756129817976, "duration": 29287}, "status": "passed", "steps": [{"name": "执行命令: A sports car is parked on the street side", "time": {"start": 1756129788689, "stop": 1756129817597, "duration": 28908}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129817597, "stop": 1756129817976, "duration": 379}, "status": "passed", "steps": [], "attachments": [{"uid": "49d112b262788216", "name": "测试总结", "source": "49d112b262788216.txt", "type": "text/plain", "size": 498}, {"uid": "338dfc7a92c8a8b7", "name": "test_completed", "source": "338dfc7a92c8a8b7.png", "type": "image/png", "size": 538033}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129817976, "stop": 1756129817977, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129817977, "stop": 1756129818307, "duration": 330}, "status": "passed", "steps": [], "attachments": [{"uid": "f16bb21e6beb2e69", "name": "测试总结", "source": "f16bb21e6beb2e69.txt", "type": "text/plain", "size": 498}, {"uid": "42f2a62f851721ab", "name": "test_completed", "source": "42f2a62f851721ab.png", "type": "image/png", "size": 538333}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8b8cca9b21af1b5f", "name": "stdout", "source": "8b8cca9b21af1b5f.txt", "type": "text/plain", "size": 14461}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129818308, "stop": 1756129818308, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129818309, "stop": 1756129819706, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_sports_car_is_parked_on_the_street_side"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b69aedbf9686c7.json", "parameterValues": []}