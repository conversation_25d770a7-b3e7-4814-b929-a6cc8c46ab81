{"uid": "8cd40d1773ed51e9", "name": "测试open contact命令", "fullName": "testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "historyId": "acdb323f998d6127fbebbc545f6e8a59", "time": {"start": 1756118009331, "stop": 1756118048045, "duration": 38714}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117996655, "stop": 1756118009330, "duration": 12675}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118009330, "stop": 1756118009330, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1756118009332, "stop": 1756118047827, "duration": 38495}, "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1756118009332, "stop": 1756118047462, "duration": 38130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118047462, "stop": 1756118047827, "duration": 365}, "status": "passed", "steps": [], "attachments": [{"uid": "be6cbbd79bcda2a6", "name": "测试总结", "source": "be6cbbd79bcda2a6.txt", "type": "text/plain", "size": 312}, {"uid": "1421123b8aede54f", "name": "test_completed", "source": "1421123b8aede54f.png", "type": "image/png", "size": 168683}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1756118047827, "stop": 1756118047828, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证Dalier应用已打开", "time": {"start": 1756118047828, "stop": 1756118047828, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118047828, "stop": 1756118048045, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "92e546eba81b2e33", "name": "测试总结", "source": "92e546eba81b2e33.txt", "type": "text/plain", "size": 312}, {"uid": "66c3a769c561ce27", "name": "test_completed", "source": "66c3a769c561ce27.png", "type": "image/png", "size": 168639}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ab2e607b462c18d4", "name": "stdout", "source": "ab2e607b462c18d4.txt", "type": "text/plain", "size": 15148}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118048046, "stop": 1756118048046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118048047, "stop": 1756118049415, "duration": 1368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8cd40d1773ed51e9.json", "parameterValues": []}