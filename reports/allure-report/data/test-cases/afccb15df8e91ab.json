{"uid": "afccb15df8e91ab", "name": "测试Summarize what I'm reading", "fullName": "testcases.test_ella.unsupported_commands.test_summarize_what_i_m_reading.TestEllaOpenPlayPoliticalNews#test_summarize_what_i_m_reading", "historyId": "07dafe2b3e3ed9841a34e9fd19de58be", "time": {"start": 1756137938584, "stop": 1756137963890, "duration": 25306}, "description": "测试Summarize what I'm reading指令", "descriptionHtml": "<p>测试Summarize what I'm reading指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137925836, "stop": 1756137938580, "duration": 12744}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137938580, "stop": 1756137938580, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Summarize what I'm reading指令", "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "time": {"start": 1756137938584, "stop": 1756137963648, "duration": 25064}, "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "time": {"start": 1756137938584, "stop": 1756137963395, "duration": 24811}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137963395, "stop": 1756137963648, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "5679bb7d3411436e", "name": "测试总结", "source": "5679bb7d3411436e.txt", "type": "text/plain", "size": 378}, {"uid": "2ae746a6a2592313", "name": "test_completed", "source": "2ae746a6a2592313.png", "type": "image/png", "size": 178234}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756137963648, "stop": 1756137963649, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137963649, "stop": 1756137963887, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "e28e66b89232e4ed", "name": "测试总结", "source": "e28e66b89232e4ed.txt", "type": "text/plain", "size": 378}, {"uid": "ae5cc6eea58d3449", "name": "test_completed", "source": "ae5cc6eea58d3449.png", "type": "image/png", "size": 178234}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a1c7c2d1a9e317dd", "name": "stdout", "source": "a1c7c2d1a9e317dd.txt", "type": "text/plain", "size": 12842}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137963891, "stop": 1756137963891, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756137963894, "stop": 1756137965241, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_summarize_what_i_m_reading"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "afccb15df8e91ab.json", "parameterValues": []}