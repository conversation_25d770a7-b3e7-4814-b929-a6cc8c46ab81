{"uid": "ee9a08659f63b979", "name": "测试turn on high brightness mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode#test_turn_on_high_brightness_mode", "historyId": "599b7a465f619c38a4638073f59c38c0", "time": {"start": 1756138642773, "stop": 1756138668710, "duration": 25937}, "description": "验证turn on high brightness mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on high brightness mode指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on high brightness mode', 'High brightness mode is turned on now.', 'High brightness mode', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode object at 0x0000029205930F90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920AC50510>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_turn_on_high_brightness_mode(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on high brightness mode', 'High brightness mode is turned on now.', 'High brightness mode', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_turn_on_high_brightness_mode.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138630069, "stop": 1756138642772, "duration": 12703}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138642772, "stop": 1756138642772, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证turn on high brightness mode指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on high brightness mode', 'High brightness mode is turned on now.', 'High brightness mode', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode object at 0x0000029205930F90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920AC50510>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_turn_on_high_brightness_mode(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on high brightness mode', 'High brightness mode is turned on now.', 'High brightness mode', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_turn_on_high_brightness_mode.py:33: AssertionError", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1756138642773, "stop": 1756138668705, "duration": 25932}, "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1756138642773, "stop": 1756138668441, "duration": 25668}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138668441, "stop": 1756138668705, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "213f6c7760db0635", "name": "测试总结", "source": "213f6c7760db0635.txt", "type": "text/plain", "size": 262}, {"uid": "2b412a118b17b123", "name": "test_completed", "source": "2b412a118b17b123.png", "type": "image/png", "size": 157301}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756138668705, "stop": 1756138668709, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on high brightness mode', 'High brightness mode is turned on now.', 'High brightness mode', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_turn_on_high_brightness_mode.py\", line 33, in test_turn_on_high_brightness_mode\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d4a1ddb1c250548e", "name": "stdout", "source": "d4a1ddb1c250548e.txt", "type": "text/plain", "size": 13924}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138668715, "stop": 1756138668931, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "47f7ad89fc95559c", "name": "失败截图-TestEllaTurnHighBrightnessMode", "source": "47f7ad89fc95559c.png", "type": "image/png", "size": 157301}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756138668933, "stop": 1756138670282, "duration": 1349}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_high_brightness_mode"}, {"name": "subSuite", "value": "TestEllaTurnHighBrightnessMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "ee9a08659f63b979.json", "parameterValues": []}