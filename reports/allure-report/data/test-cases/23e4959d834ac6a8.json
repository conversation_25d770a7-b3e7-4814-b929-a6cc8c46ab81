{"uid": "23e4959d834ac6a8", "name": "测试Scan this QR code 能正常执行", "fullName": "testcases.test_ella.self_function.test_scan_this_qr_code.TestEllaScanThisQrCode#test_scan_this_qr_code", "historyId": "5697ea2b6f1cefef94d5b32e213e05a7", "time": {"start": 1756123451065, "stop": 1756123568833, "duration": 117768}, "description": "Scan this QR code ", "descriptionHtml": "<p>Scan this QR code</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123438423, "stop": 1756123451062, "duration": 12639}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123451062, "stop": 1756123451062, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "Scan this QR code ", "status": "passed", "steps": [{"name": "执行命令: Scan this QR code ", "time": {"start": 1756123451065, "stop": 1756123568580, "duration": 117515}, "status": "passed", "steps": [{"name": "执行命令: Scan this QR code ", "time": {"start": 1756123451065, "stop": 1756123568352, "duration": 117287}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123568352, "stop": 1756123568580, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "94e94fe7012f540a", "name": "测试总结", "source": "94e94fe7012f540a.txt", "type": "text/plain", "size": 497}, {"uid": "7cc0e1fa546e6d36", "name": "test_completed", "source": "7cc0e1fa546e6d36.png", "type": "image/png", "size": 228378}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756123568580, "stop": 1756123568582, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123568582, "stop": 1756123568833, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "8dd1346398daf20d", "name": "测试总结", "source": "8dd1346398daf20d.txt", "type": "text/plain", "size": 451}, {"uid": "24a4f4a7f9b4cca0", "name": "test_completed", "source": "24a4f4a7f9b4cca0.png", "type": "image/png", "size": 228378}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "11fe84737d227217", "name": "stdout", "source": "11fe84737d227217.txt", "type": "text/plain", "size": 18324}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123568834, "stop": 1756123568834, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123568834, "stop": 1756123570228, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_scan_this_qr_code"}, {"name": "subSuite", "value": "TestEllaScanThisQrCode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_scan_this_qr_code"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "23e4959d834ac6a8.json", "parameterValues": []}