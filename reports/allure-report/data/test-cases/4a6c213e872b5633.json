{"uid": "4a6c213e872b5633", "name": "测试start record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_record.TestEllaStartRecord#test_start_record", "historyId": "7acb737855a3a3110ed556a3e5fe1256", "time": {"start": 1756126175025, "stop": 1756126237082, "duration": 62057}, "description": "start record", "descriptionHtml": "<p>start record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126162121, "stop": 1756126175024, "duration": 12903}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126175024, "stop": 1756126175024, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "start record", "status": "passed", "steps": [{"name": "执行命令: start record", "time": {"start": 1756126175025, "stop": 1756126204937, "duration": 29912}, "status": "passed", "steps": [{"name": "执行命令: start record", "time": {"start": 1756126175025, "stop": 1756126204633, "duration": 29608}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126204633, "stop": 1756126204937, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "38792071b2eaa749", "name": "测试总结", "source": "38792071b2eaa749.txt", "type": "text/plain", "size": 281}, {"uid": "f8b8e40108fd3997", "name": "test_completed", "source": "f8b8e40108fd3997.png", "type": "image/png", "size": 179406}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756126204937, "stop": 1756126204939, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756126204939, "stop": 1756126204939, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126204939, "stop": 1756126205178, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "2924bfefe834ee64", "name": "测试总结", "source": "2924bfefe834ee64.txt", "type": "text/plain", "size": 281}, {"uid": "8ffa2154c76a905f", "name": "test_completed", "source": "8ffa2154c76a905f.png", "type": "image/png", "size": 178583}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "执行命令: stop screen recording", "time": {"start": 1756126205178, "stop": 1756126237080, "duration": 31902}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1756126205178, "stop": 1756126236826, "duration": 31648}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126236826, "stop": 1756126237080, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "3a7d086b28cad272", "name": "测试总结", "source": "3a7d086b28cad272.txt", "type": "text/plain", "size": 291}, {"uid": "b87977ed7ee11683", "name": "test_completed", "source": "b87977ed7ee11683.png", "type": "image/png", "size": 180824}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}], "attachments": [{"uid": "ba97b9a3e7cb0552", "name": "stdout", "source": "ba97b9a3e7cb0552.txt", "type": "text/plain", "size": 24202}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 9, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126237083, "stop": 1756126237083, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126237085, "stop": 1756126238541, "duration": 1456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_record"}, {"name": "subSuite", "value": "TestEllaStartRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4a6c213e872b5633.json", "parameterValues": []}