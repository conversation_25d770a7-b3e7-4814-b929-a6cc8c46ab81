{"uid": "5e1887fe63a7a9da", "name": "测试enable running lock返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_running_lock.TestEllaEnableRunningLock#test_enable_running_lock", "historyId": "57a9b2e6f318afd186b838ed42ebd55c", "time": {"start": 1756131745273, "stop": 1756131781570, "duration": 36297}, "description": "验证enable running lock指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable running lock指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131732640, "stop": 1756131745272, "duration": 12632}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131745272, "stop": 1756131745272, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证enable running lock指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable running lock", "time": {"start": 1756131745273, "stop": 1756131781282, "duration": 36009}, "status": "passed", "steps": [{"name": "执行命令: enable running lock", "time": {"start": 1756131745273, "stop": 1756131781017, "duration": 35744}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131781018, "stop": 1756131781281, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "160947d1759206a", "name": "测试总结", "source": "160947d1759206a.txt", "type": "text/plain", "size": 542}, {"uid": "a69e673f9a68bb14", "name": "test_completed", "source": "a69e673f9a68bb14.png", "type": "image/png", "size": 171520}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131781282, "stop": 1756131781284, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131781284, "stop": 1756131781570, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "14fdadd9756d9eff", "name": "测试总结", "source": "14fdadd9756d9eff.txt", "type": "text/plain", "size": 542}, {"uid": "77bede5d0194e9b5", "name": "test_completed", "source": "77bede5d0194e9b5.png", "type": "image/png", "size": 171607}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3b892a159bcd761d", "name": "stdout", "source": "3b892a159bcd761d.txt", "type": "text/plain", "size": 15486}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131781571, "stop": 1756131781571, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131781572, "stop": 1756131782985, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_running_lock"}, {"name": "subSuite", "value": "TestEllaEnableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_running_lock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5e1887fe63a7a9da.json", "parameterValues": []}