{"uid": "1d31d2faf155fd7c", "name": "测试A cute little boy is skiing", "fullName": "testcases.test_ella.unsupported_commands.test_a_cute_little_boy_is_skiing.TestEllaOpenPlayPoliticalNews#test_a_cute_little_boy_is_skiing", "historyId": "a306f4c2244f596f1ab838aa7a80dd45", "time": {"start": 1756129522897, "stop": 1756129551673, "duration": 28776}, "description": "测试A cute little boy is skiing指令", "descriptionHtml": "<p>测试A cute little boy is skiing指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129509768, "stop": 1756129522896, "duration": 13128}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129522897, "stop": 1756129522897, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试A cute little boy is skiing指令", "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing", "time": {"start": 1756129522897, "stop": 1756129551468, "duration": 28571}, "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing", "time": {"start": 1756129522897, "stop": 1756129551174, "duration": 28277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129551174, "stop": 1756129551468, "duration": 294}, "status": "passed", "steps": [], "attachments": [{"uid": "a1a45f00941a6cef", "name": "测试总结", "source": "a1a45f00941a6cef.txt", "type": "text/plain", "size": 1134}, {"uid": "449d14d8e228d652", "name": "test_completed", "source": "449d14d8e228d652.png", "type": "image/png", "size": 215241}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129551468, "stop": 1756129551469, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129551469, "stop": 1756129551671, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "463a56eab4a22df9", "name": "测试总结", "source": "463a56eab4a22df9.txt", "type": "text/plain", "size": 1134}, {"uid": "dba98b54b1e614b4", "name": "test_completed", "source": "dba98b54b1e614b4.png", "type": "image/png", "size": 215255}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "63ca220947d94b75", "name": "stdout", "source": "63ca220947d94b75.txt", "type": "text/plain", "size": 15942}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129551673, "stop": 1756129551673, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129551674, "stop": 1756129553042, "duration": 1368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_cute_little_boy_is_skiing"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_cute_little_boy_is_skiing"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1d31d2faf155fd7c.json", "parameterValues": []}