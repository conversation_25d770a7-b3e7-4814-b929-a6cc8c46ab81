{"uid": "fd1ff989fe3f9a8e", "name": "测试pause music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_music.TestEllaPauseMusic#test_pause_music", "historyId": "464c8ea2a15f7ff86b8e0a347a821945", "time": {"start": 1756118102122, "stop": 1756118127729, "duration": 25607}, "description": "pause music", "descriptionHtml": "<p>pause music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118089428, "stop": 1756118102121, "duration": 12693}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118102121, "stop": 1756118102121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1756118102122, "stop": 1756118127507, "duration": 25385}, "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1756118102122, "stop": 1756118127234, "duration": 25112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118127234, "stop": 1756118127506, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "c45f1aa6633c83ba", "name": "测试总结", "source": "c45f1aa6633c83ba.txt", "type": "text/plain", "size": 288}, {"uid": "32f0710f8a9e827c", "name": "test_completed", "source": "32f0710f8a9e827c.png", "type": "image/png", "size": 175569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118127507, "stop": 1756118127510, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118127510, "stop": 1756118127728, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "3690918deea5578f", "name": "测试总结", "source": "3690918deea5578f.txt", "type": "text/plain", "size": 288}, {"uid": "4899a123ef83bff3", "name": "test_completed", "source": "4899a123ef83bff3.png", "type": "image/png", "size": 175569}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7a9bfbb7f5a3e32b", "name": "stdout", "source": "7a9bfbb7f5a3e32b.txt", "type": "text/plain", "size": 12698}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118127730, "stop": 1756118127730, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118127740, "stop": 1756118129178, "duration": 1438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaPauseMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fd1ff989fe3f9a8e.json", "parameterValues": []}