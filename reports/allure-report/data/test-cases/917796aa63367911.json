{"uid": "917796aa63367911", "name": "测试max notifications volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_max_notifications_volume.TestEllaMaxNotificationsVolume#test_max_notifications_volume", "historyId": "a5e15bb05795c5949d67f33200f4d69b", "time": {"start": 1756124877120, "stop": 1756124903181, "duration": 26061}, "description": "max notifications volume", "descriptionHtml": "<p>max notifications volume</p>\n", "status": "failed", "statusMessage": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 15", "statusTrace": "self = <testcases.test_ella.system_coupling.test_max_notifications_volume.TestEllaMaxNotificationsVolume object at 0x00000292049B5850>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292044ABA90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_max_notifications_volume(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==15, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert 0 == 15\n\ntestcases\\test_ella\\system_coupling\\test_max_notifications_volume.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124863862, "stop": 1756124877117, "duration": 13255}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124877117, "stop": 1756124877118, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "max notifications volume", "status": "failed", "statusMessage": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 15", "statusTrace": "self = <testcases.test_ella.system_coupling.test_max_notifications_volume.TestEllaMaxNotificationsVolume object at 0x00000292049B5850>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292044ABA90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_max_notifications_volume(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==15, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert 0 == 15\n\ntestcases\\test_ella\\system_coupling\\test_max_notifications_volume.py:36: AssertionError", "steps": [{"name": "执行命令: max notifications volume", "time": {"start": 1756124877121, "stop": 1756124903170, "duration": 26049}, "status": "passed", "steps": [{"name": "执行命令: max notifications volume", "time": {"start": 1756124877121, "stop": 1756124902887, "duration": 25766}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124902887, "stop": 1756124903170, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "20da971cddd230bf", "name": "测试总结", "source": "20da971cddd230bf.txt", "type": "text/plain", "size": 321}, {"uid": "5c94ddd6fd067417", "name": "test_completed", "source": "5c94ddd6fd067417.png", "type": "image/png", "size": 178479}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124903170, "stop": 1756124903173, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124903173, "stop": 1756124903177, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 15\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_max_notifications_volume.py\", line 36, in test_max_notifications_volume\n    assert final_status==15, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bd97e32c5880e6f8", "name": "stdout", "source": "bd97e32c5880e6f8.txt", "type": "text/plain", "size": 14295}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124903190, "stop": 1756124903397, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "13ab6a05b04d5b56", "name": "失败截图-TestEllaMaxNotificationsVolume", "source": "13ab6a05b04d5b56.png", "type": "image/png", "size": 178479}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756124903398, "stop": 1756124904761, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_notifications_volume"}, {"name": "subSuite", "value": "TestEllaMaxNotificationsVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_notifications_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "917796aa63367911.json", "parameterValues": []}