{"uid": "65597eace4e3cb94", "name": "测试call number by whatsapp能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp.TestEllaCallNumberWhatsapp#test_call_number_by_whatsapp", "historyId": "4fd50eb7a7e49fc09f612442a33e3010", "time": {"start": 1756129832491, "stop": 1756129866401, "duration": 33910}, "description": "call number by whatsapp", "descriptionHtml": "<p>call number by whatsapp</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp.TestEllaCallNumberWhatsapp object at 0x0000029204F4A250>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292095740D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_call_number_by_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_call_number_by_whatsapp.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129819744, "stop": 1756129832489, "duration": 12745}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129832489, "stop": 1756129832489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "call number by whatsapp", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp.TestEllaCallNumberWhatsapp object at 0x0000029204F4A250>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292095740D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_call_number_by_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_call_number_by_whatsapp.py:35: AssertionError", "steps": [{"name": "执行命令: call number by whatsapp", "time": {"start": 1756129832491, "stop": 1756129866396, "duration": 33905}, "status": "passed", "steps": [{"name": "执行命令: call number by whatsapp", "time": {"start": 1756129832491, "stop": 1756129866121, "duration": 33630}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129866121, "stop": 1756129866395, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "241119528387b57f", "name": "测试总结", "source": "241119528387b57f.txt", "type": "text/plain", "size": 331}, {"uid": "9275c3103980a537", "name": "test_completed", "source": "9275c3103980a537.png", "type": "image/png", "size": 186105}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756129866396, "stop": 1756129866400, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_call_number_by_whatsapp.py\", line 35, in test_call_number_by_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1bcb34f313e29b62", "name": "stdout", "source": "1bcb34f313e29b62.txt", "type": "text/plain", "size": 13841}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129866405, "stop": 1756129866639, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "caa14600b439cdf3", "name": "失败截图-TestEllaCallNumberWhatsapp", "source": "caa14600b439cdf3.png", "type": "image/png", "size": 186105}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756129866640, "stop": 1756129868035, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_call_number_by_whatsapp"}, {"name": "subSuite", "value": "TestEllaCallNumberWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "65597eace4e3cb94.json", "parameterValues": []}