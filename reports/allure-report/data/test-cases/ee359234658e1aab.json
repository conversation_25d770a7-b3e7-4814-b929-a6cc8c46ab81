{"uid": "ee359234658e1aab", "name": "测试check contacts能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_check_contacts.TestEllaCheckContacts#test_check_contacts", "historyId": "a3c84dd7a2924ee198fdb33cbc4e20b6", "time": {"start": 1756130152620, "stop": 1756130187010, "duration": 34390}, "description": "check contacts", "descriptionHtml": "<p>check contacts</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130140050, "stop": 1756130152618, "duration": 12568}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130152618, "stop": 1756130152618, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "check contacts", "status": "passed", "steps": [{"name": "执行命令: check contacts", "time": {"start": 1756130152620, "stop": 1756130186757, "duration": 34137}, "status": "passed", "steps": [{"name": "执行命令: check contacts", "time": {"start": 1756130152620, "stop": 1756130186459, "duration": 33839}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130186459, "stop": 1756130186757, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "c1a518a914be62b4", "name": "测试总结", "source": "c1a518a914be62b4.txt", "type": "text/plain", "size": 286}, {"uid": "110c8b0c914751fa", "name": "test_completed", "source": "110c8b0c914751fa.png", "type": "image/png", "size": 177326}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130186757, "stop": 1756130186760, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130186760, "stop": 1756130187010, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "43eaa3541ed2bf97", "name": "测试总结", "source": "43eaa3541ed2bf97.txt", "type": "text/plain", "size": 286}, {"uid": "a16b3fcd9ebbb939", "name": "test_completed", "source": "a16b3fcd9ebbb939.png", "type": "image/png", "size": 177326}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "10b3c644903d667", "name": "stdout", "source": "10b3c644903d667.txt", "type": "text/plain", "size": 12831}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130187011, "stop": 1756130187011, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130187012, "stop": 1756130188408, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_contacts"}, {"name": "subSuite", "value": "TestEllaCheckContacts"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_contacts"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ee359234658e1aab.json", "parameterValues": []}