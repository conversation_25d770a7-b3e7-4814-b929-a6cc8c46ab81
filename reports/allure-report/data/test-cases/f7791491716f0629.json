{"uid": "f7791491716f0629", "name": "测试continue music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "time": {"start": 1756117354414, "stop": 1756117380263, "duration": 25849}, "description": "continue music", "descriptionHtml": "<p>continue music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117341346, "stop": 1756117354413, "duration": 13067}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117354413, "stop": 1756117354413, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1756117354414, "stop": 1756117380020, "duration": 25606}, "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1756117354414, "stop": 1756117379754, "duration": 25340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117379755, "stop": 1756117380020, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "e7b1c53a3850799e", "name": "测试总结", "source": "e7b1c53a3850799e.txt", "type": "text/plain", "size": 294}, {"uid": "12142de4322c8b2f", "name": "test_completed", "source": "12142de4322c8b2f.png", "type": "image/png", "size": 176092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117380021, "stop": 1756117380022, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117380022, "stop": 1756117380262, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "36f4d40a89e02835", "name": "测试总结", "source": "36f4d40a89e02835.txt", "type": "text/plain", "size": 294}, {"uid": "610338fd47b04a26", "name": "test_completed", "source": "610338fd47b04a26.png", "type": "image/png", "size": 176092}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d411dee51dd4759c", "name": "stdout", "source": "d411dee51dd4759c.txt", "type": "text/plain", "size": 12731}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117380263, "stop": 1756117380263, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117380264, "stop": 1756117381622, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f7791491716f0629.json", "parameterValues": []}