{"uid": "1efbf94a56f497b6", "name": "测试make the phone mute能正常执行", "fullName": "testcases.test_ella.system_coupling.test_make_the_phone_mute.TestEllaMakePhoneMute#test_make_the_phone_mute", "historyId": "c9c4c38d0ca341040a41178716623909", "time": {"start": 1756124753821, "stop": 1756124780278, "duration": 26457}, "description": "make the phone mute", "descriptionHtml": "<p>make the phone mute</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124741103, "stop": 1756124753819, "duration": 12716}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124753821, "stop": 1756124753821, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "make the phone mute", "status": "passed", "steps": [{"name": "执行命令: make the phone mute", "time": {"start": 1756124753821, "stop": 1756124780041, "duration": 26220}, "status": "passed", "steps": [{"name": "执行命令: make the phone mute", "time": {"start": 1756124753821, "stop": 1756124779797, "duration": 25976}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124779797, "stop": 1756124780041, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "62610827a89b8043", "name": "测试总结", "source": "62610827a89b8043.txt", "type": "text/plain", "size": 296}, {"uid": "e0e9b8b9493ee261", "name": "test_completed", "source": "e0e9b8b9493ee261.png", "type": "image/png", "size": 166324}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124780041, "stop": 1756124780043, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124780044, "stop": 1756124780044, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124780044, "stop": 1756124780277, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "676eef41d1d2f744", "name": "测试总结", "source": "676eef41d1d2f744.txt", "type": "text/plain", "size": 296}, {"uid": "43cc4afc1f13b798", "name": "test_completed", "source": "43cc4afc1f13b798.png", "type": "image/png", "size": 166324}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "eafe034c38547ba0", "name": "stdout", "source": "eafe034c38547ba0.txt", "type": "text/plain", "size": 14996}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124780281, "stop": 1756124780281, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124780283, "stop": 1756124781674, "duration": 1391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_make_the_phone_mute"}, {"name": "subSuite", "value": "TestEllaMakePhoneMute"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_make_the_phone_mute"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1efbf94a56f497b6.json", "parameterValues": []}