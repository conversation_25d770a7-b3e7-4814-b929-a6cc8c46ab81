{"uid": "55ff0d10f22bffc4", "name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo.TestEllaOpenPlayPoliticalNews#test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "historyId": "81f981a4ddbff762d2de1cd977c5568a", "time": {"start": 1756132037562, "stop": 1756132063581, "duration": 26019}, "description": "测试Generate a circular car logo image with a three-pointed star inside the logo指令", "descriptionHtml": "<p>测试Generate a circular car logo image with a three-pointed star inside the logo指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132024412, "stop": 1756132037561, "duration": 13149}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132037561, "stop": 1756132037561, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate a circular car logo image with a three-pointed star inside the logo指令", "status": "passed", "steps": [{"name": "执行命令: Generate a circular car logo image with a three-pointed star inside the logo", "time": {"start": 1756132037562, "stop": 1756132063351, "duration": 25789}, "status": "passed", "steps": [{"name": "执行命令: Generate a circular car logo image with a three-pointed star inside the logo", "time": {"start": 1756132037562, "stop": 1756132063091, "duration": 25529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132063091, "stop": 1756132063350, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "5ada8bb2f82934b1", "name": "测试总结", "source": "5ada8bb2f82934b1.txt", "type": "text/plain", "size": 435}, {"uid": "aa4f81bb047b5eee", "name": "test_completed", "source": "aa4f81bb047b5eee.png", "type": "image/png", "size": 196259}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132063351, "stop": 1756132063354, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132063354, "stop": 1756132063580, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "63de3acc38085b41", "name": "测试总结", "source": "63de3acc38085b41.txt", "type": "text/plain", "size": 435}, {"uid": "368b0cba076b2ef8", "name": "test_completed", "source": "368b0cba076b2ef8.png", "type": "image/png", "size": 196256}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9823e13286a234c6", "name": "stdout", "source": "9823e13286a234c6.txt", "type": "text/plain", "size": 13495}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132063582, "stop": 1756132063582, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132063583, "stop": 1756132065084, "duration": 1501}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "55ff0d10f22bffc4.json", "parameterValues": []}