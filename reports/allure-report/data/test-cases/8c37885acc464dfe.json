{"uid": "8c37885acc464dfe", "name": "测试end exercising能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_end_exercising.TestEllaEndExercising#test_end_exercising", "historyId": "04263bf3ad5402ebd901c9c0e6682325", "time": {"start": 1756131917110, "stop": 1756131943166, "duration": 26056}, "description": "end exercising", "descriptionHtml": "<p>end exercising</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131904236, "stop": 1756131917108, "duration": 12872}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131917109, "stop": 1756131917109, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "end exercising", "status": "passed", "steps": [{"name": "执行命令: end exercising", "time": {"start": 1756131917110, "stop": 1756131942932, "duration": 25822}, "status": "passed", "steps": [{"name": "执行命令: end exercising", "time": {"start": 1756131917110, "stop": 1756131942638, "duration": 25528}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131942638, "stop": 1756131942931, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "58bdb2a96d8b7a1a", "name": "测试总结", "source": "58bdb2a96d8b7a1a.txt", "type": "text/plain", "size": 294}, {"uid": "4d582fc7aa1de938", "name": "test_completed", "source": "4d582fc7aa1de938.png", "type": "image/png", "size": 181545}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756131942932, "stop": 1756131942933, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131942934, "stop": 1756131943165, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "c92379cd3d6eae62", "name": "测试总结", "source": "c92379cd3d6eae62.txt", "type": "text/plain", "size": 294}, {"uid": "af11467bb1164284", "name": "test_completed", "source": "af11467bb1164284.png", "type": "image/png", "size": 181550}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "6ad93cb60058bb16", "name": "stdout", "source": "6ad93cb60058bb16.txt", "type": "text/plain", "size": 12436}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131943167, "stop": 1756131943167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131943168, "stop": 1756131944595, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_end_exercising"}, {"name": "subSuite", "value": "TestEllaEndExercising"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_end_exercising"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8c37885acc464dfe.json", "parameterValues": []}