{"uid": "a3d8c20f1835e122", "name": "测试view in notebook", "fullName": "testcases.test_ella.unsupported_commands.test_view_in_notebook.TestEllaOpenPlayPoliticalNews#test_view_in_notebook", "historyId": "19df0c79ab8c9ce909771e1a9d21fed3", "time": {"start": 1756138771291, "stop": 1756138803930, "duration": 32639}, "description": "测试view in notebook指令", "descriptionHtml": "<p>测试view in notebook指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138758724, "stop": 1756138771289, "duration": 12565}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138771289, "stop": 1756138771289, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试view in notebook指令", "status": "passed", "steps": [{"name": "执行命令: view in notebook", "time": {"start": 1756138771291, "stop": 1756138803710, "duration": 32419}, "status": "passed", "steps": [{"name": "执行命令: view in notebook", "time": {"start": 1756138771291, "stop": 1756138803459, "duration": 32168}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138803459, "stop": 1756138803710, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "63737ee9430b8abf", "name": "测试总结", "source": "63737ee9430b8abf.txt", "type": "text/plain", "size": 357}, {"uid": "e1ddc3d522b34b6e", "name": "test_completed", "source": "e1ddc3d522b34b6e.png", "type": "image/png", "size": 162421}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138803710, "stop": 1756138803713, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证notes已打开", "time": {"start": 1756138803713, "stop": 1756138803713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138803713, "stop": 1756138803930, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "a7cec5be9bec686e", "name": "测试总结", "source": "a7cec5be9bec686e.txt", "type": "text/plain", "size": 357}, {"uid": "c2402228b66f6013", "name": "test_completed", "source": "c2402228b66f6013.png", "type": "image/png", "size": 162421}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4e17566374f05cac", "name": "stdout", "source": "4e17566374f05cac.txt", "type": "text/plain", "size": 15235}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138803931, "stop": 1756138803931, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756138803933, "stop": 1756138805353, "duration": 1420}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_view_in_notebook"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_view_in_notebook"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a3d8c20f1835e122.json", "parameterValues": []}