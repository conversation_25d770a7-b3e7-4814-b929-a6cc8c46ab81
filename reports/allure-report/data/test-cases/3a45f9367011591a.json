{"uid": "3a45f9367011591a", "name": "测试close phonemaster能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "historyId": "bc062eca91b16841cac5c9865921b5c1", "time": {"start": 1756117313930, "stop": 1756117339909, "duration": 25979}, "description": "close phonemaster", "descriptionHtml": "<p>close phonemaster</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117301544, "stop": 1756117313928, "duration": 12384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117313928, "stop": 1756117313928, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1756117313930, "stop": 1756117339676, "duration": 25746}, "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1756117313930, "stop": 1756117339403, "duration": 25473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117339403, "stop": 1756117339675, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "ea9d23e0f67d1471", "name": "测试总结", "source": "ea9d23e0f67d1471.txt", "type": "text/plain", "size": 267}, {"uid": "b388c34721048870", "name": "test_completed", "source": "b388c34721048870.png", "type": "image/png", "size": 166047}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117339676, "stop": 1756117339680, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117339680, "stop": 1756117339909, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "2573804a7f493c33", "name": "测试总结", "source": "2573804a7f493c33.txt", "type": "text/plain", "size": 267}, {"uid": "aeafb8b69ab14ead", "name": "test_completed", "source": "aeafb8b69ab14ead.png", "type": "image/png", "size": 166047}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "42f433a90fb2ee60", "name": "stdout", "source": "42f433a90fb2ee60.txt", "type": "text/plain", "size": 12367}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756117339910, "stop": 1756117341336, "duration": 1426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756117339910, "stop": 1756117339910, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3a45f9367011591a.json", "parameterValues": []}