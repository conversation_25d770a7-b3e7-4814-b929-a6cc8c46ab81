{"uid": "f9408800b6ac4e8c", "name": "测试switch to power saving mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "5dff8ffa0041df33df80919398086e48", "time": {"start": 1756126887050, "stop": 1756126913323, "duration": 26273}, "description": "switch to power saving mode", "descriptionHtml": "<p>switch to power saving mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126874451, "stop": 1756126887050, "duration": 12599}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126887050, "stop": 1756126887050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1756126887051, "stop": 1756126913098, "duration": 26047}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1756126887051, "stop": 1756126912819, "duration": 25768}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126912819, "stop": 1756126913098, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "ebe579929935afba", "name": "测试总结", "source": "ebe579929935afba.txt", "type": "text/plain", "size": 360}, {"uid": "35190fffb4b3d916", "name": "test_completed", "source": "35190fffb4b3d916.png", "type": "image/png", "size": 188077}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126913098, "stop": 1756126913101, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126913101, "stop": 1756126913322, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "7b2ef27c6638a8d7", "name": "测试总结", "source": "7b2ef27c6638a8d7.txt", "type": "text/plain", "size": 360}, {"uid": "4021929b667c3faa", "name": "test_completed", "source": "4021929b667c3faa.png", "type": "image/png", "size": 188077}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "61f6103bcbb1cd4", "name": "stdout", "source": "61f6103bcbb1cd4.txt", "type": "text/plain", "size": 13324}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126913324, "stop": 1756126913324, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126913325, "stop": 1756126914714, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f9408800b6ac4e8c.json", "parameterValues": []}