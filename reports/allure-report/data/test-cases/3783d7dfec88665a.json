{"uid": "3783d7dfec88665a", "name": "测试set floating windows返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows#test_set_floating_windows", "historyId": "ff945a5d436679bddd13261b231955ec", "time": {"start": 1756136514554, "stop": 1756136548765, "duration": 34211}, "description": "验证set floating windows指令返回预期的不支持响应", "descriptionHtml": "<p>验证set floating windows指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set floating windows', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.thunderback页面内容] Floating Windows | Open in Smart Panel | Tap the app icon. | Floating Windows | Swipe up with Three Fingers | Turn a full-screen app into a small window. | Notifications in Floating Windows']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows object at 0x00000292056D8410>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A86B1D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_floating_windows(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set floating windows', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.thunderback页面内容] Floating Windows | Open in Smart Panel | Tap the app icon. | Floating Windows | Swipe up with Three Fingers | Turn a full-screen app into a small window. | Notifications in Floating Windows']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_floating_windows.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136501366, "stop": 1756136514553, "duration": 13187}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136514553, "stop": 1756136514553, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set floating windows指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set floating windows', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.thunderback页面内容] Floating Windows | Open in Smart Panel | Tap the app icon. | Floating Windows | Swipe up with Three Fingers | Turn a full-screen app into a small window. | Notifications in Floating Windows']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows object at 0x00000292056D8410>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A86B1D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_floating_windows(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set floating windows', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.thunderback页面内容] Floating Windows | Open in Smart Panel | Tap the app icon. | Floating Windows | Swipe up with Three Fingers | Turn a full-screen app into a small window. | Notifications in Floating Windows']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_floating_windows.py:33: AssertionError", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1756136514554, "stop": 1756136548759, "duration": 34205}, "status": "passed", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1756136514554, "stop": 1756136548437, "duration": 33883}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136548437, "stop": 1756136548759, "duration": 322}, "status": "passed", "steps": [], "attachments": [{"uid": "e947926faefbaa81", "name": "测试总结", "source": "e947926faefbaa81.txt", "type": "text/plain", "size": 509}, {"uid": "ae9e097f336c5b5f", "name": "test_completed", "source": "ae9e097f336c5b5f.png", "type": "image/png", "size": 171178}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136548759, "stop": 1756136548763, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set floating windows', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.thunderback页面内容] Floating Windows | Open in Smart Panel | Tap the app icon. | Floating Windows | Swipe up with Three Fingers | Turn a full-screen app into a small window. | Notifications in Floating Windows']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_floating_windows.py\", line 33, in test_set_floating_windows\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "59e36c6b1c41b857", "name": "stdout", "source": "59e36c6b1c41b857.txt", "type": "text/plain", "size": 16030}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136548770, "stop": 1756136549001, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "26057c5a09c9c837", "name": "失败截图-TestEllaSetFloatingWindows", "source": "26057c5a09c9c837.png", "type": "image/png", "size": 171188}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136549002, "stop": 1756136550489, "duration": 1487}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_floating_windows"}, {"name": "subSuite", "value": "TestEllaSetFloatingWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_floating_windows"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "3783d7dfec88665a.json", "parameterValues": []}