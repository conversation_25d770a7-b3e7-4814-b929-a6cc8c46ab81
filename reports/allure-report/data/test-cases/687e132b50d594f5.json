{"uid": "687e132b50d594f5", "name": "测试navigate from to red square能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square.TestEllaNavigateFromRedSquare#test_navigate_from_to_red_square", "historyId": "e8f03971277a71512b5ebaad612bc964", "time": {"start": 1756128946377, "stop": 1756128979970, "duration": 33593}, "description": "navigate from to red square", "descriptionHtml": "<p>navigate from to red square</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128933464, "stop": 1756128946375, "duration": 12911}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128946375, "stop": 1756128946375, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "navigate from to red square", "status": "passed", "steps": [{"name": "执行命令: navigate from to red square", "time": {"start": 1756128946377, "stop": 1756128979714, "duration": 33337}, "status": "passed", "steps": [{"name": "执行命令: navigate from to red square", "time": {"start": 1756128946377, "stop": 1756128979459, "duration": 33082}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128979459, "stop": 1756128979713, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "a388b6b3ebcd6636", "name": "测试总结", "source": "a388b6b3ebcd6636.txt", "type": "text/plain", "size": 436}, {"uid": "2a41569914c77488", "name": "test_completed", "source": "2a41569914c77488.png", "type": "image/png", "size": 172220}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756128979714, "stop": 1756128979717, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128979717, "stop": 1756128979968, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "f9e798a7a32a7bee", "name": "测试总结", "source": "f9e798a7a32a7bee.txt", "type": "text/plain", "size": 436}, {"uid": "cf947c59c6b7f72a", "name": "test_completed", "source": "cf947c59c6b7f72a.png", "type": "image/png", "size": 172338}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "59834fe62a5f61fd", "name": "stdout", "source": "59834fe62a5f61fd.txt", "type": "text/plain", "size": 15373}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128979971, "stop": 1756128979971, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128979972, "stop": 1756128981371, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_to_red_square"}, {"name": "subSuite", "value": "TestEllaNavigateFromRedSquare"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "687e132b50d594f5.json", "parameterValues": []}