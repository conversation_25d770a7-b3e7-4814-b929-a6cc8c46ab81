{"uid": "42797315bee2c420", "name": "测试gold coin rain能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_gold_coin_rain.TestEllaGoldCoinRain#test_gold_coin_rain", "historyId": "a84e06839a37b7806fefd316aa632437", "time": {"start": 1756132322139, "stop": 1756132351414, "duration": 29275}, "description": "gold coin rain", "descriptionHtml": "<p>gold coin rain</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132309315, "stop": 1756132322138, "duration": 12823}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132322138, "stop": 1756132322138, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "gold coin rain", "status": "passed", "steps": [{"name": "执行命令: gold coin rain", "time": {"start": 1756132322139, "stop": 1756132351163, "duration": 29024}, "status": "passed", "steps": [{"name": "执行命令: gold coin rain", "time": {"start": 1756132322140, "stop": 1756132350908, "duration": 28768}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132350908, "stop": 1756132351162, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "1589176e78b83f93", "name": "测试总结", "source": "1589176e78b83f93.txt", "type": "text/plain", "size": 286}, {"uid": "edcfaec3736ff1bb", "name": "test_completed", "source": "edcfaec3736ff1bb.png", "type": "image/png", "size": 177634}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132351163, "stop": 1756132351165, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132351165, "stop": 1756132351413, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "79cf8ddded8dc778", "name": "测试总结", "source": "79cf8ddded8dc778.txt", "type": "text/plain", "size": 286}, {"uid": "e84a5dfee101b474", "name": "test_completed", "source": "e84a5dfee101b474.png", "type": "image/png", "size": 177255}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8de3eacb2eb05466", "name": "stdout", "source": "8de3eacb2eb05466.txt", "type": "text/plain", "size": 12696}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132351414, "stop": 1756132351414, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132351415, "stop": 1756132352767, "duration": 1352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_gold_coin_rain"}, {"name": "subSuite", "value": "TestEllaGoldCoinRain"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_gold_coin_rain"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "42797315bee2c420.json", "parameterValues": []}