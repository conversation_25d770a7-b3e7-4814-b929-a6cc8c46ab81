{"uid": "a70768f1d5bbab5a", "name": "测试Enable Network Enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement.TestEllaEnableNetworkEnhancement#test_enable_network_enhancement", "historyId": "657acdf17dda1a11abf6946763f6ed52", "time": {"start": 1756131704379, "stop": 1756131731240, "duration": 26861}, "description": "验证Enable Network Enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Network Enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131691496, "stop": 1756131704378, "duration": 12882}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131704379, "stop": 1756131704379, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证Enable Network Enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1756131704379, "stop": 1756131731019, "duration": 26640}, "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1756131704379, "stop": 1756131730762, "duration": 26383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131730762, "stop": 1756131731018, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "29d4f7a1757ae2e3", "name": "测试总结", "source": "29d4f7a1757ae2e3.txt", "type": "text/plain", "size": 270}, {"uid": "68764f696c39c127", "name": "test_completed", "source": "68764f696c39c127.png", "type": "image/png", "size": 183537}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131731019, "stop": 1756131731021, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131731021, "stop": 1756131731239, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "a7b5950b3c2fb374", "name": "测试总结", "source": "a7b5950b3c2fb374.txt", "type": "text/plain", "size": 270}, {"uid": "d38277e09dca22c", "name": "test_completed", "source": "d38277e09dca22c.png", "type": "image/png", "size": 183537}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3e37114fa810e8aa", "name": "stdout", "source": "3e37114fa810e8aa.txt", "type": "text/plain", "size": 12469}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131731240, "stop": 1756131731241, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131731241, "stop": 1756131732633, "duration": 1392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaEnableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a70768f1d5bbab5a.json", "parameterValues": []}