{"uid": "55ad84bd4d192c80", "name": "测试open settings", "fullName": "testcases.test_ella.unsupported_commands.test_i_wanna_use_sim.TestEllaOpenPlayPoliticalNews#test_i_wanna_use_sim", "historyId": "e1ef5de48cb99781fc16bc01be62dca2", "time": {"start": 1756133201328, "stop": 1756133239191, "duration": 37863}, "description": "测试open settings指令", "descriptionHtml": "<p>测试open settings指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133188088, "stop": 1756133201327, "duration": 13239}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133201327, "stop": 1756133201327, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open settings指令", "status": "passed", "steps": [{"name": "执行命令: open settings", "time": {"start": 1756133201328, "stop": 1756133238925, "duration": 37597}, "status": "passed", "steps": [{"name": "执行命令: open settings", "time": {"start": 1756133201328, "stop": 1756133238626, "duration": 37298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133238626, "stop": 1756133238925, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "a8c593180f790882", "name": "测试总结", "source": "a8c593180f790882.txt", "type": "text/plain", "size": 629}, {"uid": "158428eca65618cc", "name": "test_completed", "source": "158428eca65618cc.png", "type": "image/png", "size": 170684}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756133238925, "stop": 1756133238929, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证settings已打开", "time": {"start": 1756133238929, "stop": 1756133238929, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133238929, "stop": 1756133239190, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "c5560467196ea5c0", "name": "测试总结", "source": "c5560467196ea5c0.txt", "type": "text/plain", "size": 629}, {"uid": "3561ce5f2a143579", "name": "test_completed", "source": "3561ce5f2a143579.png", "type": "image/png", "size": 170827}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a6939460f0ad6940", "name": "stdout", "source": "a6939460f0ad6940.txt", "type": "text/plain", "size": 15839}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133239191, "stop": 1756133239191, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133239192, "stop": 1756133240696, "duration": 1504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_wanna_use_sim"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_wanna_use_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "55ad84bd4d192c80.json", "parameterValues": []}