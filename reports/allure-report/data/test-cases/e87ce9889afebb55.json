{"uid": "e87ce9889afebb55", "name": "测试Enable Call Rejection返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_rejection.TestEllaEnableCallRejection#test_enable_call_rejection", "historyId": "ff8d76af98b9fdfaa206acbf87daa843", "time": {"start": 1756131648638, "stop": 1756131690052, "duration": 41414}, "description": "验证Enable Call Rejection指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Call Rejection指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131635795, "stop": 1756131648616, "duration": 12821}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131648617, "stop": 1756131648617, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证Enable Call Rejection指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1756131648638, "stop": 1756131689812, "duration": 41174}, "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1756131648638, "stop": 1756131689548, "duration": 40910}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131689548, "stop": 1756131689812, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "43b7593df1e56024", "name": "测试总结", "source": "43b7593df1e56024.txt", "type": "text/plain", "size": 556}, {"uid": "85de137b3163a31b", "name": "test_completed", "source": "85de137b3163a31b.png", "type": "image/png", "size": 172180}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131689812, "stop": 1756131689814, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131689814, "stop": 1756131690051, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "422b79dcd7521f68", "name": "测试总结", "source": "422b79dcd7521f68.txt", "type": "text/plain", "size": 556}, {"uid": "3fb052d8c820b1dc", "name": "test_completed", "source": "3fb052d8c820b1dc.png", "type": "image/png", "size": 172551}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "fd790625447d4e25", "name": "stdout", "source": "fd790625447d4e25.txt", "type": "text/plain", "size": 15492}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131690052, "stop": 1756131690052, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131690057, "stop": 1756131691486, "duration": 1429}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_rejection"}, {"name": "subSuite", "value": "TestEllaEnableCallRejection"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_rejection"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e87ce9889afebb55.json", "parameterValues": []}