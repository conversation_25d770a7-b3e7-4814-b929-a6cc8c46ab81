{"uid": "a367116298b1a28c", "name": "测试play music by boomplay", "fullName": "testcases.test_ella.dialogue.test_play_music_by_boomplay.TestEllaOpenPlayPoliticalNews#test_play_music_by_boomplay", "historyId": "d29b58e8e7ac85336b7dc255831fd5ba", "time": {"start": 1756120832658, "stop": 1756120860889, "duration": 28231}, "description": "测试play music by boomplay指令", "descriptionHtml": "<p>测试play music by boomplay指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120819742, "stop": 1756120832656, "duration": 12914}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120832657, "stop": 1756120832657, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play music by boomplay指令", "status": "passed", "steps": [{"name": "执行命令: play music by boomplay", "time": {"start": 1756120832658, "stop": 1756120860658, "duration": 28000}, "status": "passed", "steps": [{"name": "执行命令: play music by boomplay", "time": {"start": 1756120832658, "stop": 1756120860392, "duration": 27734}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120860392, "stop": 1756120860657, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "a4a2953385e2d5b7", "name": "测试总结", "source": "a4a2953385e2d5b7.txt", "type": "text/plain", "size": 315}, {"uid": "4a7a529025ae929f", "name": "test_completed", "source": "4a7a529025ae929f.png", "type": "image/png", "size": 187607}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120860658, "stop": 1756120860663, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120860663, "stop": 1756120860888, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "b90648663ac849db", "name": "测试总结", "source": "b90648663ac849db.txt", "type": "text/plain", "size": 315}, {"uid": "b1f8860c4d44f630", "name": "test_completed", "source": "b1f8860c4d44f630.png", "type": "image/png", "size": 187749}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4aaea597ce7c81d5", "name": "stdout", "source": "4aaea597ce7c81d5.txt", "type": "text/plain", "size": 13338}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756120860889, "stop": 1756120860889, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756120860891, "stop": 1756120862254, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_boomplay"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_boomplay"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a367116298b1a28c.json", "parameterValues": []}