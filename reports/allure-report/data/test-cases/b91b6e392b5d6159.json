{"uid": "b91b6e392b5d6159", "name": "测试start countdown能正常执行", "fullName": "testcases.test_ella.dialogue.test_start_countdown.TestEllaHi#test_start_countdown", "historyId": "70b49b2a6719030c7c51e642fdaec270", "time": {"start": 1756121659316, "stop": 1756121685583, "duration": 26267}, "description": "start countdown", "descriptionHtml": "<p>start countdown</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121645896, "stop": 1756121659313, "duration": 13417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121659314, "stop": 1756121659314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "start countdown", "status": "passed", "steps": [{"name": "执行命令: start countdown", "time": {"start": 1756121659316, "stop": 1756121685314, "duration": 25998}, "status": "passed", "steps": [{"name": "执行命令: start countdown", "time": {"start": 1756121659316, "stop": 1756121685036, "duration": 25720}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121685036, "stop": 1756121685312, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "39d4a3ec8170b0cf", "name": "测试总结", "source": "39d4a3ec8170b0cf.txt", "type": "text/plain", "size": 274}, {"uid": "e318d91d02a69ae9", "name": "test_completed", "source": "e318d91d02a69ae9.png", "type": "image/png", "size": 149722}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121685314, "stop": 1756121685321, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121685321, "stop": 1756121685579, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "42a5b67c5af8720b", "name": "测试总结", "source": "42a5b67c5af8720b.txt", "type": "text/plain", "size": 274}, {"uid": "8046f0d182c433eb", "name": "test_completed", "source": "8046f0d182c433eb.png", "type": "image/png", "size": 149722}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7788bb085a294efa", "name": "stdout", "source": "7788bb085a294efa.txt", "type": "text/plain", "size": 12396}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121685586, "stop": 1756121685587, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121685592, "stop": 1756121686988, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_start_countdown"}, {"name": "subSuite", "value": "TestEllaHi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_start_countdown"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b91b6e392b5d6159.json", "parameterValues": []}