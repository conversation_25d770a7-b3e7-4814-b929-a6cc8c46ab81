{"uid": "79702a2e218996d2", "name": "测试stop music能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_music.TestEllaStopMusic#test_stop_music", "historyId": "794f685415bbcd702feae0b55a4dd537", "time": {"start": 1756121700793, "stop": 1756121726739, "duration": 25946}, "description": "stop music", "descriptionHtml": "<p>stop music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121687007, "stop": 1756121700792, "duration": 13785}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121700792, "stop": 1756121700792, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "stop music", "status": "passed", "steps": [{"name": "执行命令: stop music", "time": {"start": 1756121700793, "stop": 1756121726464, "duration": 25671}, "status": "passed", "steps": [{"name": "执行命令: stop music", "time": {"start": 1756121700793, "stop": 1756121726197, "duration": 25404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121726197, "stop": 1756121726464, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "7fb28e2e7be94ddf", "name": "测试总结", "source": "7fb28e2e7be94ddf.txt", "type": "text/plain", "size": 286}, {"uid": "b042bcce9ac39958", "name": "test_completed", "source": "b042bcce9ac39958.png", "type": "image/png", "size": 169228}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121726464, "stop": 1756121726469, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121726469, "stop": 1756121726737, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "a8e398d163a68976", "name": "测试总结", "source": "a8e398d163a68976.txt", "type": "text/plain", "size": 286}, {"uid": "f28fd9223b00ac0f", "name": "test_completed", "source": "f28fd9223b00ac0f.png", "type": "image/png", "size": 169228}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d79775c21cd1a674", "name": "stdout", "source": "d79775c21cd1a674.txt", "type": "text/plain", "size": 12688}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121726742, "stop": 1756121726742, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121726744, "stop": 1756121728122, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_music"}, {"name": "subSuite", "value": "TestEllaStopMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "79702a2e218996d2.json", "parameterValues": []}