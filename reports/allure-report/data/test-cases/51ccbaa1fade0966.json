{"uid": "51ccbaa1fade0966", "name": "测试help me generate a picture of an elegant girl", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_elegant_girl.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_an_elegant_girl", "historyId": "50e811b93ce50e5ac8364a9fea07e234", "time": {"start": 1756132777524, "stop": 1756132803502, "duration": 25978}, "description": "测试help me generate a picture of an elegant girl指令", "descriptionHtml": "<p>测试help me generate a picture of an elegant girl指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132764904, "stop": 1756132777523, "duration": 12619}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132777523, "stop": 1756132777523, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试help me generate a picture of an elegant girl指令", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an elegant girl", "time": {"start": 1756132777524, "stop": 1756132803283, "duration": 25759}, "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an elegant girl", "time": {"start": 1756132777524, "stop": 1756132803028, "duration": 25504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132803028, "stop": 1756132803283, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "c5a13f6037ab1448", "name": "测试总结", "source": "c5a13f6037ab1448.txt", "type": "text/plain", "size": 373}, {"uid": "13cd8a319a8bcd8f", "name": "test_completed", "source": "13cd8a319a8bcd8f.png", "type": "image/png", "size": 183276}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132803283, "stop": 1756132803286, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132803286, "stop": 1756132803501, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "1f3be04c31da7049", "name": "测试总结", "source": "1f3be04c31da7049.txt", "type": "text/plain", "size": 373}, {"uid": "5345c59902ed303b", "name": "test_completed", "source": "5345c59902ed303b.png", "type": "image/png", "size": 183276}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "199e411c857d1af1", "name": "stdout", "source": "199e411c857d1af1.txt", "type": "text/plain", "size": 13247}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132803503, "stop": 1756132803503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132803504, "stop": 1756132804880, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_an_elegant_girl"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_elegant_girl"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "51ccbaa1fade0966.json", "parameterValues": []}