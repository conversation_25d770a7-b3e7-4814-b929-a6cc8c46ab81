{"uid": "a63afad26f0fcac6", "name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "fullName": "testcases.test_ella.unsupported_commands.test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations.TestEllaOpenPlayPoliticalNews#test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "historyId": "a416a85ec867e3b2cfd6e23150d72859", "time": {"start": 1756130644939, "stop": 1756130671985, "duration": 27046}, "description": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations指令", "descriptionHtml": "<p>测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756130632098, "stop": 1756130644938, "duration": 12840}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756130644938, "stop": 1756130644938, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations指令", "status": "passed", "steps": [{"name": "执行命令: Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "time": {"start": 1756130644939, "stop": 1756130671749, "duration": 26810}, "status": "passed", "steps": [{"name": "执行命令: Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "time": {"start": 1756130644939, "stop": 1756130671476, "duration": 26537}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130671476, "stop": 1756130671749, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "6cc37f133c51f9f6", "name": "测试总结", "source": "6cc37f133c51f9f6.txt", "type": "text/plain", "size": 424}, {"uid": "d40f87ab38022c88", "name": "test_completed", "source": "d40f87ab38022c88.png", "type": "image/png", "size": 170398}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756130671749, "stop": 1756130671751, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756130671751, "stop": 1756130671985, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "6257c5f74600770", "name": "测试总结", "source": "6257c5f74600770.txt", "type": "text/plain", "size": 424}, {"uid": "394fb1d031a0d4c0", "name": "test_completed", "source": "394fb1d031a0d4c0.png", "type": "image/png", "size": 170409}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "57cbb41de30c876a", "name": "stdout", "source": "57cbb41de30c876a.txt", "type": "text/plain", "size": 13563}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756130671986, "stop": 1756130671986, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756130671987, "stop": 1756130673516, "duration": 1529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a63afad26f0fcac6.json", "parameterValues": []}