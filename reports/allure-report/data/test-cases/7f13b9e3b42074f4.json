{"uid": "7f13b9e3b42074f4", "name": "测试order a takeaway能正常执行", "fullName": "testcases.test_ella.third_coupling.test_order_a_takeaway.TestEllaOrderATakeaway#test_order_a_takeaway", "historyId": "de5ff490f92fc399976d91fe0edc371e", "time": {"start": 1756129218304, "stop": 1756129244934, "duration": 26630}, "description": "order a takeaway", "descriptionHtml": "<p>order a takeaway</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756129205308, "stop": 1756129218302, "duration": 12994}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756129218302, "stop": 1756129218302, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "order a takeaway", "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "time": {"start": 1756129218305, "stop": 1756129244725, "duration": 26420}, "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "time": {"start": 1756129218305, "stop": 1756129244438, "duration": 26133}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129244438, "stop": 1756129244724, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "1b5d0d702fd1f0af", "name": "测试总结", "source": "1b5d0d702fd1f0af.txt", "type": "text/plain", "size": 306}, {"uid": "667ee2037c44e721", "name": "test_completed", "source": "667ee2037c44e721.png", "type": "image/png", "size": 180712}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756129244725, "stop": 1756129244726, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756129244726, "stop": 1756129244933, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "f4cac58c66419341", "name": "测试总结", "source": "f4cac58c66419341.txt", "type": "text/plain", "size": 306}, {"uid": "a1fd84aaf46f6a21", "name": "test_completed", "source": "a1fd84aaf46f6a21.png", "type": "image/png", "size": 180703}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d5fc0dc79ba19a29", "name": "stdout", "source": "d5fc0dc79ba19a29.txt", "type": "text/plain", "size": 12589}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756129244934, "stop": 1756129244934, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756129244935, "stop": 1756129246288, "duration": 1353}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_takeaway"}, {"name": "subSuite", "value": "TestEllaOrderATakeaway"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_takeaway"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7f13b9e3b42074f4.json", "parameterValues": []}