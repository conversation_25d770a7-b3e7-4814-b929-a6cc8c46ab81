{"uid": "c0a1305b4bff069e", "name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio.TestEllaOpenPlayPoliticalNews#test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "historyId": "693bce6b8bfdefa98827246122355bf1", "time": {"start": 1756131997357, "stop": 1756132022959, "duration": 25602}, "description": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio指令", "descriptionHtml": "<p>测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131984794, "stop": 1756131997354, "duration": 12560}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131997354, "stop": 1756131997354, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio指令", "status": "passed", "steps": [{"name": "执行命令: Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "time": {"start": 1756131997357, "stop": 1756132022743, "duration": 25386}, "status": "passed", "steps": [{"name": "执行命令: Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "time": {"start": 1756131997357, "stop": 1756132022491, "duration": 25134}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132022491, "stop": 1756132022742, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "e5925bc9fdab498f", "name": "测试总结", "source": "e5925bc9fdab498f.txt", "type": "text/plain", "size": 417}, {"uid": "d130bbb0927481b", "name": "test_completed", "source": "d130bbb0927481b.png", "type": "image/png", "size": 192653}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132022743, "stop": 1756132022744, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132022744, "stop": 1756132022958, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "30be9cfc12ac1025", "name": "测试总结", "source": "30be9cfc12ac1025.txt", "type": "text/plain", "size": 417}, {"uid": "c7619a78ba70d154", "name": "test_completed", "source": "c7619a78ba70d154.png", "type": "image/png", "size": 192653}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b8b11ca7030c2b31", "name": "stdout", "source": "b8b11ca7030c2b31.txt", "type": "text/plain", "size": 13244}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132022961, "stop": 1756132022961, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132022962, "stop": 1756132024362, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c0a1305b4bff069e.json", "parameterValues": []}