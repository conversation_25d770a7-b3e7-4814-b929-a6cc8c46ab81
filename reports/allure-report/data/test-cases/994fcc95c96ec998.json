{"uid": "994fcc95c96ec998", "name": "测试set sim1 ringtone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone.TestEllaSetSimRingtone#test_set_sim_ringtone", "historyId": "a5a3cc08eb97e600c97acb65a7439ec0", "time": {"start": 1756137425373, "stop": 1756137461104, "duration": 35731}, "description": "验证set sim1 ringtone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set sim1 ringtone指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set sim1 ringtone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Do Not Disturb | Volume Adjustment | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_sim_ringtone.TestEllaSetSimRingtone object at 0x00000292057C4290>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A941AD0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_sim_ringtone(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set sim1 ringtone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Do Not Disturb | Volume Adjustment | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_sim_ringtone.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756137412267, "stop": 1756137425371, "duration": 13104}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756137425371, "stop": 1756137425371, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set sim1 ringtone指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set sim1 ringtone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Do Not Disturb | Volume Adjustment | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_sim_ringtone.TestEllaSetSimRingtone object at 0x00000292057C4290>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A941AD0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_sim_ringtone(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set sim1 ringtone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Do Not Disturb | Volume Adjustment | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_sim_ringtone.py:33: AssertionError", "steps": [{"name": "执行命令: set sim1 ringtone", "time": {"start": 1756137425373, "stop": 1756137461099, "duration": 35726}, "status": "passed", "steps": [{"name": "执行命令: set sim1 ringtone", "time": {"start": 1756137425373, "stop": 1756137460823, "duration": 35450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756137460823, "stop": 1756137461099, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "ae5686153f55f7f9", "name": "测试总结", "source": "ae5686153f55f7f9.txt", "type": "text/plain", "size": 497}, {"uid": "826275ce524c8d33", "name": "test_completed", "source": "826275ce524c8d33.png", "type": "image/png", "size": 167314}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756137461099, "stop": 1756137461103, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set sim1 ringtone', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Sound & Vibration | Do Not Disturb | Volume Adjustment | Media | Ring Volume | Notifications | Alarm | Haptic Feedback | Ringtone Settings | SIM1 Ringtone | We Can | SIM2 Ringtone | We Can']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_sim_ringtone.py\", line 33, in test_set_sim_ringtone\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b23c3db65aebb5f7", "name": "stdout", "source": "b23c3db65aebb5f7.txt", "type": "text/plain", "size": 15923}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756137461108, "stop": 1756137461334, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "eea2c82b9e39e18a", "name": "失败截图-TestEllaSetSimRingtone", "source": "eea2c82b9e39e18a.png", "type": "image/png", "size": 167300}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756137461335, "stop": 1756137462859, "duration": 1524}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_sim_ringtone"}, {"name": "subSuite", "value": "TestEllaSetSimRingtone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "994fcc95c96ec998.json", "parameterValues": []}