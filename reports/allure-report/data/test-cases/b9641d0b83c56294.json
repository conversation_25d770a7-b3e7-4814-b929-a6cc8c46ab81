{"uid": "b9641d0b83c56294", "name": "测试set alarm volume 50", "fullName": "testcases.test_ella.system_coupling.test_set_alarm_volume.TestEllaOpenAlarmVolume#test_set_alarm_volume", "historyId": "3238a485ed8e76dc2866c0b0bcd4930e", "time": {"start": 1756125690921, "stop": 1756125718048, "duration": 27127}, "description": "测试set alarm volume 50指令", "descriptionHtml": "<p>测试set alarm volume 50指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125678086, "stop": 1756125690919, "duration": 12833}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125690919, "stop": 1756125690919, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试set alarm volume 50指令", "status": "passed", "steps": [{"name": "执行命令: set alarm volume 50", "time": {"start": 1756125690921, "stop": 1756125717699, "duration": 26778}, "status": "passed", "steps": [{"name": "执行命令: set alarm volume 50", "time": {"start": 1756125690921, "stop": 1756125717445, "duration": 26524}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125717445, "stop": 1756125717698, "duration": 253}, "status": "passed", "steps": [], "attachments": [{"uid": "7c5d4c7f8c65d783", "name": "测试总结", "source": "7c5d4c7f8c65d783.txt", "type": "text/plain", "size": 295}, {"uid": "3c7411d497371568", "name": "test_completed", "source": "3c7411d497371568.png", "type": "image/png", "size": 184013}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125717699, "stop": 1756125717700, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证alarm_volume已打开", "time": {"start": 1756125717700, "stop": 1756125717700, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125717700, "stop": 1756125718047, "duration": 347}, "status": "passed", "steps": [], "attachments": [{"uid": "a509b498638fda38", "name": "测试总结", "source": "a509b498638fda38.txt", "type": "text/plain", "size": 295}, {"uid": "c5787aebf956cdee", "name": "test_completed", "source": "c5787aebf956cdee.png", "type": "image/png", "size": 184615}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f2c2b8efd74fde24", "name": "stdout", "source": "f2c2b8efd74fde24.txt", "type": "text/plain", "size": 13838}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125718050, "stop": 1756125718050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125718052, "stop": 1756125719410, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_alarm_volume"}, {"name": "subSuite", "value": "TestEllaOpenAlarmVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_alarm_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b9641d0b83c56294.json", "parameterValues": []}