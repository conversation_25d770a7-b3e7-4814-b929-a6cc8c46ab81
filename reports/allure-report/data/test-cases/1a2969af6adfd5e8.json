{"uid": "1a2969af6adfd5e8", "name": "测试jump to battery usage返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage.TestEllaJumpBatteryUsage#test_jump_to_battery_usage", "historyId": "4276e587385154206726240ad06acd24", "time": {"start": 1756133713719, "stop": 1756133740610, "duration": 26891}, "description": "验证jump to battery usage指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to battery usage指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133700938, "stop": 1756133713718, "duration": 12780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133713718, "stop": 1756133713718, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to battery usage指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to battery usage", "time": {"start": 1756133713719, "stop": 1756133740379, "duration": 26660}, "status": "passed", "steps": [{"name": "执行命令: jump to battery usage", "time": {"start": 1756133713719, "stop": 1756133740092, "duration": 26373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133740092, "stop": 1756133740379, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "564d14489aca3985", "name": "测试总结", "source": "564d14489aca3985.txt", "type": "text/plain", "size": 338}, {"uid": "697cf991d39002b1", "name": "test_completed", "source": "697cf991d39002b1.png", "type": "image/png", "size": 186731}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133740380, "stop": 1756133740382, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133740382, "stop": 1756133740609, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "2f605c0e760b8d16", "name": "测试总结", "source": "2f605c0e760b8d16.txt", "type": "text/plain", "size": 338}, {"uid": "2389c7adec73e379", "name": "test_completed", "source": "2389c7adec73e379.png", "type": "image/png", "size": 186731}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "7bfb285e43501df6", "name": "stdout", "source": "7bfb285e43501df6.txt", "type": "text/plain", "size": 13097}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133740610, "stop": 1756133740610, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756133740612, "stop": 1756133742055, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_usage"}, {"name": "subSuite", "value": "TestEllaJumpBatteryUsage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1a2969af6adfd5e8.json", "parameterValues": []}