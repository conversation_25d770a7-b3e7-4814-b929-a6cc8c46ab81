{"uid": "db5573e745bae3b8", "name": "测试book a flight to paris返回正确的不支持响应", "fullName": "testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis#test_book_a_flight_to_paris", "historyId": "7b1fce8b7d3ff59dca969b439bb82f75", "time": {"start": 1756119305961, "stop": 1756119331904, "duration": 25943}, "description": "验证book a flight to paris指令返回预期的不支持响应", "descriptionHtml": "<p>验证book a flight to paris指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119293656, "stop": 1756119305960, "duration": 12304}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119305960, "stop": 1756119305960, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证book a flight to paris指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: book a flight to paris", "time": {"start": 1756119305961, "stop": 1756119331695, "duration": 25734}, "status": "passed", "steps": [{"name": "执行命令: book a flight to paris", "time": {"start": 1756119305961, "stop": 1756119331449, "duration": 25488}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119331449, "stop": 1756119331694, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "d9276698ef755841", "name": "测试总结", "source": "d9276698ef755841.txt", "type": "text/plain", "size": 836}, {"uid": "faa46a8454e6b986", "name": "test_completed", "source": "faa46a8454e6b986.png", "type": "image/png", "size": 178478}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756119331695, "stop": 1756119331698, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119331698, "stop": 1756119331904, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "e7a9007aca5ae3f5", "name": "测试总结", "source": "e7a9007aca5ae3f5.txt", "type": "text/plain", "size": 836}, {"uid": "623c0073d40ef348", "name": "test_completed", "source": "623c0073d40ef348.png", "type": "image/png", "size": 178478}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e1e35536af5c24b4", "name": "stdout", "source": "e1e35536af5c24b4.txt", "type": "text/plain", "size": 15708}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119331905, "stop": 1756119331905, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119331907, "stop": 1756119333248, "duration": 1341}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_book_a_flight_to_paris"}, {"name": "subSuite", "value": "TestEllaBookFlightParis"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_book_a_flight_to_paris"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "db5573e745bae3b8.json", "parameterValues": []}