{"uid": "dbbf837baec55962", "name": "测试pause music能正常执行", "fullName": "testcases.test_ella.dialogue.test_pause_music.TestEllaHowIsWeatherToday#test_pause_music", "historyId": "b7bfa1ba094155307273abc83c43a0d5", "time": {"start": 1756120750104, "stop": 1756120776052, "duration": 25948}, "description": "pause music", "descriptionHtml": "<p>pause music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756120737243, "stop": 1756120750103, "duration": 12860}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756120750103, "stop": 1756120750103, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1756120750104, "stop": 1756120775830, "duration": 25726}, "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1756120750104, "stop": 1756120775556, "duration": 25452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120775556, "stop": 1756120775830, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "7bf7e90e33411509", "name": "测试总结", "source": "7bf7e90e33411509.txt", "type": "text/plain", "size": 288}, {"uid": "c9dff377bc9bf9b6", "name": "test_completed", "source": "c9dff377bc9bf9b6.png", "type": "image/png", "size": 181329}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756120775830, "stop": 1756120775832, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756120775832, "stop": 1756120776052, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "f7a486107156fcba", "name": "测试总结", "source": "f7a486107156fcba.txt", "type": "text/plain", "size": 288}, {"uid": "c977e512e4073b84", "name": "test_completed", "source": "c977e512e4073b84.png", "type": "image/png", "size": 181329}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f220dc2fde41cc32", "name": "stdout", "source": "f220dc2fde41cc32.txt", "type": "text/plain", "size": 12713}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756120776052, "stop": 1756120777462, "duration": 1410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756120776052, "stop": 1756120776052, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_pause_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dbbf837baec55962.json", "parameterValues": []}