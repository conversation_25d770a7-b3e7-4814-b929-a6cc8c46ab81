{"uid": "5319731af5b7beaf", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings#test_jump_to_auto_rotate_screen_settings", "historyId": "ab2195315637668cad08b0606ef7ff17", "time": {"start": 1756133622850, "stop": 1756133649838, "duration": 26988}, "description": "验证jump to auto rotate screen settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to auto rotate screen settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to auto rotate screen settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings object at 0x00000292053845D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096F01D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_auto_rotate_screen_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to auto rotate screen settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_auto_rotate_screen_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133610240, "stop": 1756133622847, "duration": 12607}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133622849, "stop": 1756133622849, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to auto rotate screen settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to auto rotate screen settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings object at 0x00000292053845D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000292096F01D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_auto_rotate_screen_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to auto rotate screen settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_auto_rotate_screen_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "time": {"start": 1756133622850, "stop": 1756133649835, "duration": 26985}, "status": "passed", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "time": {"start": 1756133622850, "stop": 1756133649588, "duration": 26738}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133649588, "stop": 1756133649834, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "97aa522d4062386b", "name": "测试总结", "source": "97aa522d4062386b.txt", "type": "text/plain", "size": 288}, {"uid": "9cbd5ab5ebbbd125", "name": "test_completed", "source": "9cbd5ab5ebbbd125.png", "type": "image/png", "size": 178275}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133649835, "stop": 1756133649837, "duration": 2}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to auto rotate screen settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_auto_rotate_screen_settings.py\", line 33, in test_jump_to_auto_rotate_screen_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "d4f85c8076e1f76f", "name": "stdout", "source": "d4f85c8076e1f76f.txt", "type": "text/plain", "size": 13818}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133649843, "stop": 1756133650064, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "390c31d4614507e", "name": "失败截图-TestEllaJumpAutoRotateScreenSettings", "source": "390c31d4614507e.png", "type": "image/png", "size": 178275}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133650087, "stop": 1756133651531, "duration": 1444}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_auto_rotate_screen_settings"}, {"name": "subSuite", "value": "TestEllaJumpAutoRotateScreenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "5319731af5b7beaf.json", "parameterValues": []}