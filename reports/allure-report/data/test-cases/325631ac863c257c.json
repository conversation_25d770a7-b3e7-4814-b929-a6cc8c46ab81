{"uid": "325631ac863c257c", "name": "测试Generate a landscape painting image for me", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_landscape_painting_image_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_landscape_painting_image_for_me", "historyId": "e1b97b8698ff620d6d8faf32f381c874", "time": {"start": 1756132078200, "stop": 1756132103498, "duration": 25298}, "description": "测试Generate a landscape painting image for me指令", "descriptionHtml": "<p>测试Generate a landscape painting image for me指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756132065089, "stop": 1756132078199, "duration": 13110}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756132078199, "stop": 1756132078199, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试Generate a landscape painting image for me指令", "status": "passed", "steps": [{"name": "执行命令: Generate a landscape painting image for me", "time": {"start": 1756132078200, "stop": 1756132103279, "duration": 25079}, "status": "passed", "steps": [{"name": "执行命令: Generate a landscape painting image for me", "time": {"start": 1756132078200, "stop": 1756132102994, "duration": 24794}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132102994, "stop": 1756132103279, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "8fc8b8609ccc41b8", "name": "测试总结", "source": "8fc8b8609ccc41b8.txt", "type": "text/plain", "size": 367}, {"uid": "dd650bc5e3802d64", "name": "test_completed", "source": "dd650bc5e3802d64.png", "type": "image/png", "size": 193482}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756132103279, "stop": 1756132103281, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756132103281, "stop": 1756132103496, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "6a22143017d33ba9", "name": "测试总结", "source": "6a22143017d33ba9.txt", "type": "text/plain", "size": 367}, {"uid": "2349e25a5ea40238", "name": "test_completed", "source": "2349e25a5ea40238.png", "type": "image/png", "size": 193482}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f54bd16bacbdb701", "name": "stdout", "source": "f54bd16bacbdb701.txt", "type": "text/plain", "size": 13223}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756132103498, "stop": 1756132103498, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756132103499, "stop": 1756132104904, "duration": 1405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_landscape_painting_image_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_landscape_painting_image_for_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "325631ac863c257c.json", "parameterValues": []}