{"uid": "f19098ad70138d29", "name": "测试clear junk files命令", "fullName": "testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles#test_clear_junk_files", "historyId": "7413abdce214459d0e44671ef65b660b", "time": {"start": 1756123927543, "stop": 1756123965227, "duration": 37684}, "description": "使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster", "descriptionHtml": "<p>使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756123915090, "stop": 1756123927540, "duration": 12450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756123927542, "stop": 1756123927542, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster", "status": "passed", "steps": [{"name": "执行命令: clear junk files", "time": {"start": 1756123927544, "stop": 1756123964965, "duration": 37421}, "status": "passed", "steps": [{"name": "执行命令: clear junk files", "time": {"start": 1756123927544, "stop": 1756123964709, "duration": 37165}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123964709, "stop": 1756123964965, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "e0a6d8ffec299cf5", "name": "测试总结", "source": "e0a6d8ffec299cf5.txt", "type": "text/plain", "size": 453}, {"uid": "d65fb83393c61dda", "name": "test_completed", "source": "d65fb83393c61dda.png", "type": "image/png", "size": 172847}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1756123964965, "stop": 1756123964966, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1756123964966, "stop": 1756123964966, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756123964966, "stop": 1756123965226, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "709b2d9aab8e4aae", "name": "测试总结", "source": "709b2d9aab8e4aae.txt", "type": "text/plain", "size": 453}, {"uid": "95cf25206fab9719", "name": "test_completed", "source": "95cf25206fab9719.png", "type": "image/png", "size": 173427}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "e32a3bbf88d269d4", "name": "stdout", "source": "e32a3bbf88d269d4.txt", "type": "text/plain", "size": 15496}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756123965228, "stop": 1756123965228, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756123965231, "stop": 1756123966607, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_clear_junk_files"}, {"name": "subSuite", "value": "TestEllaClearJunkFiles"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_clear_junk_files"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f19098ad70138d29.json", "parameterValues": []}