{"uid": "30cdfbec88453677", "name": "测试turn off the 8 am alarm", "fullName": "testcases.test_ella.system_coupling.test_turn_off_the_am_alarm.TestEllaOpenTurnOffAmAlarm#test_turn_off_the_am_alarm", "historyId": "c33807b5ffc8a9f3d1a58e446637a66b", "time": {"start": 1756127619721, "stop": 1756127724696, "duration": 104975}, "description": "测试turn off the 8 am alarm指令", "descriptionHtml": "<p>测试turn off the 8 am alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127606889, "stop": 1756127619716, "duration": 12827}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127619717, "stop": 1756127619717, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn off the 8 am alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756127619721, "stop": 1756127644903, "duration": 25182}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756127619721, "stop": 1756127644631, "duration": 24910}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127644631, "stop": 1756127644902, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "30d16c515d11b58d", "name": "测试总结", "source": "30d16c515d11b58d.txt", "type": "text/plain", "size": 303}, {"uid": "4abb6152e86826f", "name": "test_completed", "source": "4abb6152e86826f.png", "type": "image/png", "size": 183177}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756127644903, "stop": 1756127671334, "duration": 26431}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756127644903, "stop": 1756127671075, "duration": 26172}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127671075, "stop": 1756127671334, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "2063f43e4c9e70da", "name": "测试总结", "source": "2063f43e4c9e70da.txt", "type": "text/plain", "size": 242}, {"uid": "501406b0c886007a", "name": "test_completed", "source": "501406b0c886007a.png", "type": "image/png", "size": 147943}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn off the 8 am alarm", "time": {"start": 1756127671335, "stop": 1756127697957, "duration": 26622}, "status": "passed", "steps": [{"name": "执行命令: turn off the 8 am alarm", "time": {"start": 1756127671335, "stop": 1756127697658, "duration": 26323}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127697658, "stop": 1756127697956, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "7acecbde267a6261", "name": "测试总结", "source": "7acecbde267a6261.txt", "type": "text/plain", "size": 255}, {"uid": "22714dc687f6ba23", "name": "test_completed", "source": "22714dc687f6ba23.png", "type": "image/png", "size": 149025}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756127697957, "stop": 1756127724693, "duration": 26736}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756127697957, "stop": 1756127724402, "duration": 26445}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127724402, "stop": 1756127724693, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "d82b47dc38296854", "name": "测试总结", "source": "d82b47dc38296854.txt", "type": "text/plain", "size": 224}, {"uid": "d9f086a2c7a9d2b1", "name": "test_completed", "source": "d9f086a2c7a9d2b1.png", "type": "image/png", "size": 146665}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756127724693, "stop": 1756127724695, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "attachments": [{"uid": "b52d561a3de3533b", "name": "stdout", "source": "b52d561a3de3533b.txt", "type": "text/plain", "size": 41057}], "parameters": [], "attachmentsCount": 9, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 13, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127724697, "stop": 1756127724697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756127724697, "stop": 1756127726068, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_the_am_alarm"}, {"name": "subSuite", "value": "TestEllaOpenTurnOffAmAlarm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_the_am_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "30cdfbec88453677.json", "parameterValues": []}