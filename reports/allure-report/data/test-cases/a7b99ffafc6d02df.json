{"uid": "a7b99ffafc6d02df", "name": "测试help me take a long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot#test_help_me_take_a_long_screenshot", "historyId": "fe3d09fe0bad56e7804ef2f5ea49d283", "time": {"start": 1756124460614, "stop": 1756124489312, "duration": 28698}, "description": "help me take a long screenshot", "descriptionHtml": "<p>help me take a long screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124447300, "stop": 1756124460611, "duration": 13311}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124460612, "stop": 1756124460612, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "help me take a long screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1756124460614, "stop": 1756124489070, "duration": 28456}, "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1756124460614, "stop": 1756124488782, "duration": 28168}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124488782, "stop": 1756124489069, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "5f14966c0cb53ea3", "name": "测试总结", "source": "5f14966c0cb53ea3.txt", "type": "text/plain", "size": 616}, {"uid": "fc2afba552a6c52e", "name": "test_completed", "source": "fc2afba552a6c52e.png", "type": "image/png", "size": 168736}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证文件存在", "time": {"start": 1756124489070, "stop": 1756124489070, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124489070, "stop": 1756124489311, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "ccb84efd06c5062f", "name": "测试总结", "source": "ccb84efd06c5062f.txt", "type": "text/plain", "size": 616}, {"uid": "93e9a6694513e15f", "name": "test_completed", "source": "93e9a6694513e15f.png", "type": "image/png", "size": 168841}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "a5b9b4d63d74a41e", "name": "stdout", "source": "a5b9b4d63d74a41e.txt", "type": "text/plain", "size": 14992}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124489314, "stop": 1756124489314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124489315, "stop": 1756124490669, "duration": 1354}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_long_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a7b99ffafc6d02df.json", "parameterValues": []}