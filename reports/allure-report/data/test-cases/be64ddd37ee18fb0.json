{"uid": "be64ddd37ee18fb0", "name": "测试close flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_close_flashlight.TestEllaCloseFlashlight#test_close_flashlight", "historyId": "d18ed3013dc7867440fb611fb474ca05", "time": {"start": 1756124061270, "stop": 1756124088329, "duration": 27059}, "description": "close flashlight", "descriptionHtml": "<p>close flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124048424, "stop": 1756124061269, "duration": 12845}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124061269, "stop": 1756124061269, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close flashlight", "status": "passed", "steps": [{"name": "执行命令: close flashlight", "time": {"start": 1756124061270, "stop": 1756124088074, "duration": 26804}, "status": "passed", "steps": [{"name": "执行命令: close flashlight", "time": {"start": 1756124061270, "stop": 1756124087830, "duration": 26560}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124087830, "stop": 1756124088074, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "48d7ecf429f6618b", "name": "测试总结", "source": "48d7ecf429f6618b.txt", "type": "text/plain", "size": 304}, {"uid": "9d3f48c2984db203", "name": "test_completed", "source": "9d3f48c2984db203.png", "type": "image/png", "size": 167776}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124088074, "stop": 1756124088076, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124088076, "stop": 1756124088076, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124088076, "stop": 1756124088328, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "e32ac26207ef397a", "name": "测试总结", "source": "e32ac26207ef397a.txt", "type": "text/plain", "size": 304}, {"uid": "fe9505250ca690dd", "name": "test_completed", "source": "fe9505250ca690dd.png", "type": "image/png", "size": 167776}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "bb9ea8f86d97eed3", "name": "stdout", "source": "bb9ea8f86d97eed3.txt", "type": "text/plain", "size": 13125}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124088330, "stop": 1756124088330, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124088332, "stop": 1756124089693, "duration": 1361}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_flashlight"}, {"name": "subSuite", "value": "TestEllaCloseFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "be64ddd37ee18fb0.json", "parameterValues": []}