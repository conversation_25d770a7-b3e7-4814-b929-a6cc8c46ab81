{"uid": "caff1acb4c8a2f11", "name": "测试play sun be song of jide chord", "fullName": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord.TestEllaOpenPlaySunBeSongOfJideChord#test_play_sun_be_song_of_jide_chord", "historyId": "d41a9c89a400d807309a9cecf36c0728", "time": {"start": 1756118503441, "stop": 1756118546040, "duration": 42599}, "description": "测试play sun be song of jide chord指令", "descriptionHtml": "<p>测试play sun be song of jide chord指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118490461, "stop": 1756118503440, "duration": 12979}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118503440, "stop": 1756118503440, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play sun be song of jide chord指令", "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1756118503441, "stop": 1756118545787, "duration": 42346}, "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1756118503441, "stop": 1756118545532, "duration": 42091}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118545532, "stop": 1756118545786, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "cfe5fd44ce8fe8d9", "name": "测试总结", "source": "cfe5fd44ce8fe8d9.txt", "type": "text/plain", "size": 634}, {"uid": "8f15ad8cacaf3b1", "name": "test_completed", "source": "8f15ad8cacaf3b1.png", "type": "image/png", "size": 173157}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118545787, "stop": 1756118545788, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证已打开", "time": {"start": 1756118545788, "stop": 1756118545788, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118545788, "stop": 1756118546039, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "914fa06e585c2fce", "name": "测试总结", "source": "914fa06e585c2fce.txt", "type": "text/plain", "size": 634}, {"uid": "94533be821913606", "name": "test_completed", "source": "94533be821913606.png", "type": "image/png", "size": 173202}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "191052a2bcfa812e", "name": "stdout", "source": "191052a2bcfa812e.txt", "type": "text/plain", "size": 15990}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118546041, "stop": 1756118546041, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118546042, "stop": 1756118547459, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_sun_be_song_of_jide_chord"}, {"name": "subSuite", "value": "TestEllaOpenPlaySunBeSongOfJideChord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "caff1acb4c8a2f11.json", "parameterValues": []}