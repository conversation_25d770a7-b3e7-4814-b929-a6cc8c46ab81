{"uid": "f73687b4de35ea46", "name": "测试redial", "fullName": "testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews#test_redial", "historyId": "b846e0492742bba04cf4a26ee4530889", "time": {"start": 1756135596505, "stop": 1756135596505, "duration": 0}, "description": "测试redial指令", "descriptionHtml": "<p>测试redial指令</p>\n", "status": "skipped", "statusMessage": "Skipped: redial命令需要先拨打电话，才能重拨，无法通用化", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\unsupported_commands\\\\test_redial.py', 14, 'Skipped: redial命令需要先拨打电话，才能重拨，无法通用化')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135609339, "stop": 1756135609339, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135635686, "stop": 1756135635686, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "@pytest.mark.skip(reason='redial命令需要先拨打电话，才能重拨，无法通用化')"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_redial"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_redial"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["@pytest.mark.skip(reason='redial命令需要先拨打电话，才能重拨，无法通用化')", "smoke"]}, "source": "f73687b4de35ea46.json", "parameterValues": []}