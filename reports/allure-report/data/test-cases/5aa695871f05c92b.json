{"uid": "5aa695871f05c92b", "name": "测试Enable Call on Hold返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold.TestEllaEnableCallHold#test_enable_call_on_hold", "historyId": "2bf170e8c0013ab361afb23f8f059db8", "time": {"start": 1756131599376, "stop": 1756131634320, "duration": 34944}, "description": "验证Enable Call on Hold指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Call on Hold指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756131586481, "stop": 1756131599372, "duration": 12891}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756131599372, "stop": 1756131599372, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证Enable Call on Hold指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "time": {"start": 1756131599376, "stop": 1756131634083, "duration": 34707}, "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "time": {"start": 1756131599376, "stop": 1756131633832, "duration": 34456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131633833, "stop": 1756131634083, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "7b655e66729d0719", "name": "测试总结", "source": "7b655e66729d0719.txt", "type": "text/plain", "size": 335}, {"uid": "2f5e586ca15f7f0a", "name": "test_completed", "source": "2f5e586ca15f7f0a.png", "type": "image/png", "size": 185117}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756131634083, "stop": 1756131634085, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756131634085, "stop": 1756131634319, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "78dddcc5a9940792", "name": "测试总结", "source": "78dddcc5a9940792.txt", "type": "text/plain", "size": 335}, {"uid": "78ce739b2ef09df7", "name": "test_completed", "source": "78ce739b2ef09df7.png", "type": "image/png", "size": 185117}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b9474a5757b381b", "name": "stdout", "source": "b9474a5757b381b.txt", "type": "text/plain", "size": 12975}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756131634321, "stop": 1756131634321, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756131634325, "stop": 1756131635716, "duration": 1391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaEnableCallHold"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5aa695871f05c92b.json", "parameterValues": []}