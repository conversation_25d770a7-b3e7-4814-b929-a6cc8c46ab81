{"uid": "8e09913c7063dfee", "name": "测试take a screenshot能正常执行", "fullName": "testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot#test_take_a_screenshot", "historyId": "6bdbaaabff6497c5d3be4727f1a7cd8d", "time": {"start": 1756118817576, "stop": 1756118846188, "duration": 28612}, "description": "take a screenshot", "descriptionHtml": "<p>take a screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118804601, "stop": 1756118817574, "duration": 12973}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118817575, "stop": 1756118817575, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take a screenshot", "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1756118817576, "stop": 1756118845939, "duration": 28363}, "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1756118817576, "stop": 1756118845659, "duration": 28083}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118845660, "stop": 1756118845939, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "8119a4a02d81b080", "name": "测试总结", "source": "8119a4a02d81b080.txt", "type": "text/plain", "size": 559}, {"uid": "b629157c3031e3cc", "name": "test_completed", "source": "b629157c3031e3cc.png", "type": "image/png", "size": 161772}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证文件存在", "time": {"start": 1756118845939, "stop": 1756118845939, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118845939, "stop": 1756118846188, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "286eb16ac0d5a386", "name": "测试总结", "source": "286eb16ac0d5a386.txt", "type": "text/plain", "size": 559}, {"uid": "72f107ece4492465", "name": "test_completed", "source": "72f107ece4492465.png", "type": "image/png", "size": 161933}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "4655c83e6d16195d", "name": "stdout", "source": "4655c83e6d16195d.txt", "type": "text/plain", "size": 14761}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1756118846189, "stop": 1756118847558, "duration": 1369}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1756118846189, "stop": 1756118846189, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8e09913c7063dfee.json", "parameterValues": []}