{"uid": "be0959652b4e867", "name": "测试power saving能正常执行", "fullName": "testcases.test_ella.system_coupling.test_power_saving.TestEllaPowerSaving#test_power_saving", "historyId": "8acd3c85f9c9d0b7f252da4466c049e6", "time": {"start": 1756125439441, "stop": 1756125473058, "duration": 33617}, "description": "power saving", "descriptionHtml": "<p>power saving</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125426521, "stop": 1756125439439, "duration": 12918}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125439439, "stop": 1756125439439, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "power saving", "status": "passed", "steps": [{"name": "执行命令: power saving", "time": {"start": 1756125439441, "stop": 1756125472801, "duration": 33360}, "status": "passed", "steps": [{"name": "执行命令: power saving", "time": {"start": 1756125439441, "stop": 1756125472542, "duration": 33101}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125472542, "stop": 1756125472800, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "6155f92b9b6e20dc", "name": "测试总结", "source": "6155f92b9b6e20dc.txt", "type": "text/plain", "size": 349}, {"uid": "299936bca15dcdee", "name": "test_completed", "source": "299936bca15dcdee.png", "type": "image/png", "size": 169703}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125472801, "stop": 1756125472804, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1756125472804, "stop": 1756125472804, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125472804, "stop": 1756125473058, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "4574c488950d077f", "name": "测试总结", "source": "4574c488950d077f.txt", "type": "text/plain", "size": 349}, {"uid": "7987985b430cd473", "name": "test_completed", "source": "7987985b430cd473.png", "type": "image/png", "size": 169255}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "b94b3c6117ec4c2a", "name": "stdout", "source": "b94b3c6117ec4c2a.txt", "type": "text/plain", "size": 15170}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125473059, "stop": 1756125473059, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125473060, "stop": 1756125474468, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_power_saving"}, {"name": "subSuite", "value": "TestEllaPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "be0959652b4e867.json", "parameterValues": []}