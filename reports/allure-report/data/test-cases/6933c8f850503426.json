{"uid": "6933c8f850503426", "name": "测试play love sotry", "fullName": "testcases.test_ella.unsupported_commands.test_play_love_sotry.TestEllaOpenPlayPoliticalNews#test_play_love_sotry", "historyId": "a8ceb9cec2faa4662cde95140569091b", "time": {"start": 1756135109855, "stop": 1756135159786, "duration": 49931}, "description": "测试play love sotry指令", "descriptionHtml": "<p>测试play love sotry指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135096773, "stop": 1756135109806, "duration": 13033}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135109806, "stop": 1756135109806, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play love sotry指令", "status": "passed", "steps": [{"name": "执行命令: play love sotry", "time": {"start": 1756135109856, "stop": 1756135159539, "duration": 49683}, "status": "passed", "steps": [{"name": "执行命令: play love sotry", "time": {"start": 1756135109856, "stop": 1756135159268, "duration": 49412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135159268, "stop": 1756135159539, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "91557cd93f5fa760", "name": "测试总结", "source": "91557cd93f5fa760.txt", "type": "text/plain", "size": 601}, {"uid": "a8d57e2eb235e7d4", "name": "test_completed", "source": "a8d57e2eb235e7d4.png", "type": "image/png", "size": 170992}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135159540, "stop": 1756135159541, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证visha已打开", "time": {"start": 1756135159541, "stop": 1756135159541, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135159541, "stop": 1756135159785, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "85038aa043d57eb0", "name": "测试总结", "source": "85038aa043d57eb0.txt", "type": "text/plain", "size": 601}, {"uid": "d13a028b42e71d37", "name": "test_completed", "source": "d13a028b42e71d37.png", "type": "image/png", "size": 170992}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "8a7057c5fe7f8e18", "name": "stdout", "source": "8a7057c5fe7f8e18.txt", "type": "text/plain", "size": 15786}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135159787, "stop": 1756135159787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135159789, "stop": 1756135161279, "duration": 1490}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_love_sotry"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_love_sotry"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6933c8f850503426.json", "parameterValues": []}