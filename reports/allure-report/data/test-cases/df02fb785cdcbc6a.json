{"uid": "df02fb785cdcbc6a", "name": "测试set flex-still mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode.TestEllaSetFlexStillMode#test_set_flex_still_mode", "historyId": "969451307307a13b4d89a24bb46ad0bb", "time": {"start": 1756136432884, "stop": 1756136459034, "duration": 26150}, "description": "验证set flex-still mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证set flex-still mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136419988, "stop": 1756136432882, "duration": 12894}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136432882, "stop": 1756136432882, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set flex-still mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1756136432884, "stop": 1756136458804, "duration": 25920}, "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1756136432884, "stop": 1756136458521, "duration": 25637}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136458521, "stop": 1756136458803, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "c37b661bbae8e4ef", "name": "测试总结", "source": "c37b661bbae8e4ef.txt", "type": "text/plain", "size": 336}, {"uid": "47dc8be220a67384", "name": "test_completed", "source": "47dc8be220a67384.png", "type": "image/png", "size": 190150}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136458804, "stop": 1756136458805, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136458805, "stop": 1756136459033, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "d810093727a1124d", "name": "测试总结", "source": "d810093727a1124d.txt", "type": "text/plain", "size": 336}, {"uid": "66b6f01c257c1f90", "name": "test_completed", "source": "66b6f01c257c1f90.png", "type": "image/png", "size": 190150}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ff14b6e7b48da7f7", "name": "stdout", "source": "ff14b6e7b48da7f7.txt", "type": "text/plain", "size": 12583}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136459037, "stop": 1756136459037, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136459038, "stop": 1756136460469, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flex_still_mode"}, {"name": "subSuite", "value": "TestEllaSetFlexStillMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "df02fb785cdcbc6a.json", "parameterValues": []}