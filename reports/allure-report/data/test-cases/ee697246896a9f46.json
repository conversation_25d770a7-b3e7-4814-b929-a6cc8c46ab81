{"uid": "ee697246896a9f46", "name": "测试open bluetooth", "fullName": "testcases.test_ella.system_coupling.test_open_bluetooth.TestEllaOpenBluetooth#test_open_bluetooth", "historyId": "733cc57b9e666f7c16017a85f41c410d", "time": {"start": 1756125269949, "stop": 1756125298731, "duration": 28782}, "description": "测试open bluetooth指令", "descriptionHtml": "<p>测试open bluetooth指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125256249, "stop": 1756125269947, "duration": 13698}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125269947, "stop": 1756125269947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试open bluetooth指令", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1756125269949, "stop": 1756125298488, "duration": 28539}, "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1756125269949, "stop": 1756125298208, "duration": 28259}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125298208, "stop": 1756125298487, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "a048ed38a0c5811f", "name": "测试总结", "source": "a048ed38a0c5811f.txt", "type": "text/plain", "size": 215}, {"uid": "c4159ca63b41b8de", "name": "test_completed", "source": "c4159ca63b41b8de.png", "type": "image/png", "size": 155501}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125298488, "stop": 1756125298490, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证bluetooth已打开", "time": {"start": 1756125298490, "stop": 1756125298490, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125298490, "stop": 1756125298729, "duration": 239}, "status": "passed", "steps": [], "attachments": [{"uid": "83130af0ef31d291", "name": "测试总结", "source": "83130af0ef31d291.txt", "type": "text/plain", "size": 215}, {"uid": "cc598b3a1052ff92", "name": "test_completed", "source": "cc598b3a1052ff92.png", "type": "image/png", "size": 155804}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "462fed93057db71f", "name": "stdout", "source": "462fed93057db71f.txt", "type": "text/plain", "size": 13432}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125298732, "stop": 1756125298732, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125298733, "stop": 1756125300093, "duration": 1360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ee697246896a9f46.json", "parameterValues": []}