{"uid": "acd93fdad8aaf9f", "name": "测试switching charging speed能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_switching_charging_speed.TestEllaSwitchingChargingSpeed#test_switching_charging_speed", "historyId": "e8c3c7bb72cf538a9e89a7b790c5e689", "time": {"start": 1756138147076, "stop": 1756138179097, "duration": 32021}, "description": "switching charging speed", "descriptionHtml": "<p>switching charging speed</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['switching charging speed', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Health | Charging Speed | Reverse Wired Charging | Bypass Charging | Charging Safety Protection | Battery Health & Charging']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_switching_charging_speed.TestEllaSwitchingChargingSpeed object at 0x000002920588CF10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A895C90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_switching_charging_speed(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['switching charging speed', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Health | Charging Speed | Reverse Wired Charging | Bypass Charging | Charging Safety Protection | Battery Health & Charging']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_switching_charging_speed.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756138134572, "stop": 1756138147076, "duration": 12504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756138147076, "stop": 1756138147076, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switching charging speed", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['switching charging speed', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Health | Charging Speed | Reverse Wired Charging | Bypass Charging | Charging Safety Protection | Battery Health & Charging']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_switching_charging_speed.TestEllaSwitchingChargingSpeed object at 0x000002920588CF10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A895C90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_switching_charging_speed(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['switching charging speed', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Health | Charging Speed | Reverse Wired Charging | Bypass Charging | Charging Safety Protection | Battery Health & Charging']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_switching_charging_speed.py:33: AssertionError", "steps": [{"name": "执行命令: switching charging speed", "time": {"start": 1756138147077, "stop": 1756138179092, "duration": 32015}, "status": "passed", "steps": [{"name": "执行命令: switching charging speed", "time": {"start": 1756138147077, "stop": 1756138178811, "duration": 31734}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756138178811, "stop": 1756138179091, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "6b3de230669291f0", "name": "测试总结", "source": "6b3de230669291f0.txt", "type": "text/plain", "size": 458}, {"uid": "5c061a69ffcab21b", "name": "test_completed", "source": "5c061a69ffcab21b.png", "type": "image/png", "size": 167666}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756138179092, "stop": 1756138179096, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['switching charging speed', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.batterylab页面内容] Battery Health | Charging Speed | Reverse Wired Charging | Bypass Charging | Charging Safety Protection | Battery Health & Charging']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_switching_charging_speed.py\", line 33, in test_switching_charging_speed\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ceb44cc7c67ecb5d", "name": "stdout", "source": "ceb44cc7c67ecb5d.txt", "type": "text/plain", "size": 15973}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756138179101, "stop": 1756138179341, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "c5fe385d7d8bf247", "name": "失败截图-TestEllaSwitchingChargingSpeed", "source": "c5fe385d7d8bf247.png", "type": "image/png", "size": 167775}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756138179342, "stop": 1756138180693, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switching_charging_speed"}, {"name": "subSuite", "value": "TestEllaSwitchingChargingSpeed"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switching_charging_speed"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "acd93fdad8aaf9f.json", "parameterValues": []}