{"uid": "6757f6ce32cda936", "name": "测试play afro strut", "fullName": "testcases.test_ella.component_coupling.test_play_afro_strut.TestEllaOpenPlayAfroStrut#test_play_afro_strut", "historyId": "ffb0a39af30beaa699329479ec564117", "time": {"start": 1756118222804, "stop": 1756118272237, "duration": 49433}, "description": "测试play afro strut指令", "descriptionHtml": "<p>测试play afro strut指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118210178, "stop": 1756118222802, "duration": 12624}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118222803, "stop": 1756118222803, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play afro strut指令", "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1756118222805, "stop": 1756118272002, "duration": 49197}, "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1756118222805, "stop": 1756118271734, "duration": 48929}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118271734, "stop": 1756118272002, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "9bf32908d51ac975", "name": "测试总结", "source": "9bf32908d51ac975.txt", "type": "text/plain", "size": 605}, {"uid": "197d7a63ff0c108c", "name": "test_completed", "source": "197d7a63ff0c108c.png", "type": "image/png", "size": 174254}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756118272002, "stop": 1756118272004, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证play afro strut已打开", "time": {"start": 1756118272004, "stop": 1756118272004, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756118272004, "stop": 1756118272236, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "b33cca0fce3a877e", "name": "测试总结", "source": "b33cca0fce3a877e.txt", "type": "text/plain", "size": 605}, {"uid": "1695834b6f62b5b8", "name": "test_completed", "source": "1695834b6f62b5b8.png", "type": "image/png", "size": 174605}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1317d745f3b16875", "name": "stdout", "source": "1317d745f3b16875.txt", "type": "text/plain", "size": 15786}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756118272238, "stop": 1756118272238, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756118272239, "stop": 1756118273576, "duration": 1337}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_afro_strut"}, {"name": "subSuite", "value": "TestEllaOpenPlayAfroStrut"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_afro_strut"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6757f6ce32cda936.json", "parameterValues": []}