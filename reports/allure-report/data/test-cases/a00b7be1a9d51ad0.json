{"uid": "a00b7be1a9d51ad0", "name": "open clock", "fullName": "testcases.test_ella.component_coupling.test_open_clock.TestEllaCommandConcise#test_open_clock", "historyId": "169e5b613c0fec2cebd053175998bf17", "time": {"start": 1756117728276, "stop": 1756117764952, "duration": 36676}, "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "descriptionHtml": "<p>使用open clock命令，验证响应包含Done且实际打开clock命令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117713620, "stop": 1756117728274, "duration": 14654}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117728274, "stop": 1756117728274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1756117728276, "stop": 1756117764729, "duration": 36453}, "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1756117728276, "stop": 1756117764429, "duration": 36153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117764430, "stop": 1756117764728, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "401fe7120134e99d", "name": "测试总结", "source": "401fe7120134e99d.txt", "type": "text/plain", "size": 368}, {"uid": "7e8621983cfd2673", "name": "test_completed", "source": "7e8621983cfd2673.png", "type": "image/png", "size": 174439}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1756117764729, "stop": 1756117764730, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证clock已打开", "time": {"start": 1756117764730, "stop": 1756117764730, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117764730, "stop": 1756117764951, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "ff3619847f718d4c", "name": "测试总结", "source": "ff3619847f718d4c.txt", "type": "text/plain", "size": 368}, {"uid": "a69426db290bf07f", "name": "test_completed", "source": "a69426db290bf07f.png", "type": "image/png", "size": 174831}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "18f020490322e05e", "name": "stdout", "source": "18f020490322e05e.txt", "type": "text/plain", "size": 15250}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117764952, "stop": 1756117764952, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117764959, "stop": 1756117766343, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "open clock"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_clock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a00b7be1a9d51ad0.json", "parameterValues": []}