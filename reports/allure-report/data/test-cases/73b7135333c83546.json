{"uid": "73b7135333c83546", "name": "测试display the route go company", "fullName": "testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps#test_display_the_route_go_company", "historyId": "f4d12b1367b35df96178a58e48fe8f5e", "time": {"start": 1756117551767, "stop": 1756117583665, "duration": 31898}, "description": "测试display the route go company指令", "descriptionHtml": "<p>测试display the route go company指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756117539053, "stop": 1756117551765, "duration": 12712}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756117551765, "stop": 1756117551765, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试display the route go company指令", "status": "passed", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1756117551768, "stop": 1756117583415, "duration": 31647}, "status": "passed", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1756117551768, "stop": 1756117583141, "duration": 31373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117583141, "stop": 1756117583415, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "9a9535f6f6794e70", "name": "测试总结", "source": "9a9535f6f6794e70.txt", "type": "text/plain", "size": 238}, {"uid": "1378adaec0f7dd7e", "name": "test_completed", "source": "1378adaec0f7dd7e.png", "type": "image/png", "size": 157263}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756117583415, "stop": 1756117583418, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756117583418, "stop": 1756117583665, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "3832db9ba60aaa5b", "name": "测试总结", "source": "3832db9ba60aaa5b.txt", "type": "text/plain", "size": 238}, {"uid": "ed17e69c6fc5c525", "name": "test_completed", "source": "ed17e69c6fc5c525.png", "type": "image/png", "size": 157263}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "74937e5aafbba63c", "name": "stdout", "source": "74937e5aafbba63c.txt", "type": "text/plain", "size": 12867}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756117583667, "stop": 1756117583667, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756117583668, "stop": 1756117585103, "duration": 1435}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_display_the_route_go_company"}, {"name": "subSuite", "value": "TestEllaOpenMaps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_display_the_route_go_company"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "73b7135333c83546.json", "parameterValues": []}