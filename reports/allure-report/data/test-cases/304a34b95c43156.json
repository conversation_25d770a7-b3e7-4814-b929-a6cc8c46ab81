{"uid": "304a34b95c43156", "name": "测试video call mom through whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_video_call_mom_through_whatsapp.TestEllaVideoCallMomThroughWhatsapp#test_video_call_mom_through_whatsapp", "historyId": "e865942f74e70950eccebd8243dd6035", "time": {"start": 1756122101693, "stop": 1756122136270, "duration": 34577}, "description": "video call mom through whatsapp", "descriptionHtml": "<p>video call mom through whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756122088608, "stop": 1756122101692, "duration": 13084}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756122101692, "stop": 1756122101692, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "video call mom through whatsapp", "status": "passed", "steps": [{"name": "执行命令: video call mom through whatsapp", "time": {"start": 1756122101693, "stop": 1756122136000, "duration": 34307}, "status": "passed", "steps": [{"name": "执行命令: video call mom through whatsapp", "time": {"start": 1756122101693, "stop": 1756122135696, "duration": 34003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122135696, "stop": 1756122135998, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "bb1bb01a1428f9e0", "name": "测试总结", "source": "bb1bb01a1428f9e0.txt", "type": "text/plain", "size": 335}, {"uid": "cda218715f8d44e6", "name": "test_completed", "source": "cda218715f8d44e6.png", "type": "image/png", "size": 187071}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756122136000, "stop": 1756122136003, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756122136003, "stop": 1756122136270, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "6aec24f12eda8542", "name": "测试总结", "source": "6aec24f12eda8542.txt", "type": "text/plain", "size": 335}, {"uid": "65503bb31f0b6ca9", "name": "test_completed", "source": "65503bb31f0b6ca9.png", "type": "image/png", "size": 187071}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2b6899e6c45cfd79", "name": "stdout", "source": "2b6899e6c45cfd79.txt", "type": "text/plain", "size": 13026}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756122136273, "stop": 1756122136273, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756122136274, "stop": 1756122137676, "duration": 1402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_video_call_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaVideoCallMomThroughWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_video_call_mom_through_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "304a34b95c43156.json", "parameterValues": []}