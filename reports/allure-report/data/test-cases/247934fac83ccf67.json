{"uid": "247934fac83ccf67", "name": "测试global gdp trends能正常执行", "fullName": "testcases.test_ella.dialogue.test_global_gdp_trends.TestEllaGlobalGdpTrends#test_global_gdp_trends", "historyId": "dee08db8cb0f1293bf864f56326992d5", "time": {"start": 1756119725053, "stop": 1756119754196, "duration": 29143}, "description": "global gdp trends", "descriptionHtml": "<p>global gdp trends</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119711975, "stop": 1756119725051, "duration": 13076}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119725051, "stop": 1756119725051, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "global gdp trends", "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1756119725053, "stop": 1756119753933, "duration": 28880}, "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1756119725053, "stop": 1756119753651, "duration": 28598}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119753652, "stop": 1756119753933, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "f5b942d841a82e89", "name": "测试总结", "source": "f5b942d841a82e89.txt", "type": "text/plain", "size": 1572}, {"uid": "d4a47354890472ea", "name": "test_completed", "source": "d4a47354890472ea.png", "type": "image/png", "size": 267814}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119753933, "stop": 1756119753937, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119753937, "stop": 1756119754196, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "397224b9808affe3", "name": "测试总结", "source": "397224b9808affe3.txt", "type": "text/plain", "size": 1572}, {"uid": "6bbd9c63ce9322e8", "name": "test_completed", "source": "6bbd9c63ce9322e8.png", "type": "image/png", "size": 267369}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "29467ae9f037b23b", "name": "stdout", "source": "29467ae9f037b23b.txt", "type": "text/plain", "size": 18122}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119754197, "stop": 1756119754197, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119754199, "stop": 1756119755563, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_global_gdp_trends"}, {"name": "subSuite", "value": "TestEllaGlobalGdpTrends"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_global_gdp_trends"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "247934fac83ccf67.json", "parameterValues": []}