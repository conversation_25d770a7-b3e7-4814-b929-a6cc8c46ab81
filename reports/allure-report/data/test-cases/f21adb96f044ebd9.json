{"uid": "f21adb96f044ebd9", "name": "测试download basketball能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "historyId": "6f2c4144233271771cdd01a5c48ea3ca", "time": {"start": 1756128759009, "stop": 1756128787343, "duration": 28334}, "description": "download basketball", "descriptionHtml": "<p>download basketball</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756128746007, "stop": 1756128759008, "duration": 13001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756128759008, "stop": 1756128759009, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "download basketball", "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1756128759010, "stop": 1756128787112, "duration": 28102}, "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1756128759010, "stop": 1756128786812, "duration": 27802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128786812, "stop": 1756128787111, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "c3a7718a9d3d0323", "name": "测试总结", "source": "c3a7718a9d3d0323.txt", "type": "text/plain", "size": 259}, {"uid": "72bc2f737efe5a6a", "name": "test_completed", "source": "72bc2f737efe5a6a.png", "type": "image/png", "size": 167102}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756128787112, "stop": 1756128787117, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756128787117, "stop": 1756128787342, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "6aadbb56600037a4", "name": "测试总结", "source": "6aadbb56600037a4.txt", "type": "text/plain", "size": 259}, {"uid": "d2939b5e6fe37857", "name": "test_completed", "source": "d2939b5e6fe37857.png", "type": "image/png", "size": 167468}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "63de233819b3f01e", "name": "stdout", "source": "63de233819b3f01e.txt", "type": "text/plain", "size": 13067}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756128787343, "stop": 1756128787343, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756128787345, "stop": 1756128788800, "duration": 1455}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_basketball"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f21adb96f044ebd9.json", "parameterValues": []}