{"uid": "28437e56ae43580e", "name": "测试set alarm for 10 o'clock", "fullName": "testcases.test_ella.system_coupling.test_set_alarm_for_10_o_clock.TestEllaOpenClock#test_set_alarm_for_10_o_clock", "historyId": "5b1e68388004fe021690469c3d83b485", "time": {"start": 1756125622725, "stop": 1756125676747, "duration": 54022}, "description": "测试set alarm for 10 o'clock指令", "descriptionHtml": "<p>测试set alarm for 10 o'clock指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756125609868, "stop": 1756125622724, "duration": 12856}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756125622724, "stop": 1756125622724, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试set alarm for 10 o'clock指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756125622725, "stop": 1756125649227, "duration": 26502}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756125622725, "stop": 1756125648965, "duration": 26240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125648965, "stop": 1756125649227, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "81273b6597d60c55", "name": "测试总结", "source": "81273b6597d60c55.txt", "type": "text/plain", "size": 303}, {"uid": "6445c569fe987dd8", "name": "test_completed", "source": "6445c569fe987dd8.png", "type": "image/png", "size": 193297}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set alarm for 10 o'clock", "time": {"start": 1756125649227, "stop": 1756125676508, "duration": 27281}, "status": "passed", "steps": [{"name": "执行命令: set alarm for 10 o'clock", "time": {"start": 1756125649227, "stop": 1756125676221, "duration": 26994}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125676221, "stop": 1756125676507, "duration": 286}, "status": "passed", "steps": [], "attachments": [{"uid": "b0315ff5c89316f1", "name": "测试总结", "source": "b0315ff5c89316f1.txt", "type": "text/plain", "size": 248}, {"uid": "e5eba0c2cb317bdd", "name": "test_completed", "source": "e5eba0c2cb317bdd.png", "type": "image/png", "size": 154494}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756125676508, "stop": 1756125676512, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756125676512, "stop": 1756125676747, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "1f8f7e8750731450", "name": "测试总结", "source": "1f8f7e8750731450.txt", "type": "text/plain", "size": 248}, {"uid": "150b7bd2ea01d22f", "name": "test_completed", "source": "150b7bd2ea01d22f.png", "type": "image/png", "size": 155421}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "59ff4800fceec312", "name": "stdout", "source": "59ff4800fceec312.txt", "type": "text/plain", "size": 22185}], "parameters": [], "attachmentsCount": 7, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 8, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756125676749, "stop": 1756125676749, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756125676750, "stop": 1756125678078, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_alarm_for_10_o_clock"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_alarm_for_10_o_clock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "28437e56ae43580e.json", "parameterValues": []}