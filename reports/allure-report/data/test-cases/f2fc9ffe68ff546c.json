{"uid": "f2fc9ffe68ff546c", "name": "测试turn off the 8 am alarm", "fullName": "testcases.test_ella.component_coupling.test_turn_off_the_8_am_alarm.TestEllaOpenClock#test_turn_off_the_8_am_alarm", "historyId": "098126ed77f375b3e0f5370b3ec7d0b7", "time": {"start": 1756118980168, "stop": 1756119085739, "duration": 105571}, "description": "测试turn off the 8 am alarm指令", "descriptionHtml": "<p>测试turn off the 8 am alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756118967394, "stop": 1756118980166, "duration": 12772}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756118980167, "stop": 1756118980167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试turn off the 8 am alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118980168, "stop": 1756119005653, "duration": 25485}, "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "time": {"start": 1756118980168, "stop": 1756119005379, "duration": 25211}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119005379, "stop": 1756119005653, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "72cf7f6606ae1cbc", "name": "测试总结", "source": "72cf7f6606ae1cbc.txt", "type": "text/plain", "size": 303}, {"uid": "32151db0cdc69b7e", "name": "test_completed", "source": "32151db0cdc69b7e.png", "type": "image/png", "size": 176913}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756119005653, "stop": 1756119032523, "duration": 26870}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1756119005653, "stop": 1756119032289, "duration": 26636}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119032289, "stop": 1756119032522, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "2338fd308bff7624", "name": "测试总结", "source": "2338fd308bff7624.txt", "type": "text/plain", "size": 242}, {"uid": "78c03b2f36e8dc4e", "name": "test_completed", "source": "78c03b2f36e8dc4e.png", "type": "image/png", "size": 151995}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: turn off the 8 am alarm", "time": {"start": 1756119032523, "stop": 1756119058889, "duration": 26366}, "status": "passed", "steps": [{"name": "执行命令: turn off the 8 am alarm", "time": {"start": 1756119032523, "stop": 1756119058599, "duration": 26076}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119058599, "stop": 1756119058888, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "dcc4d33399710b60", "name": "测试总结", "source": "dcc4d33399710b60.txt", "type": "text/plain", "size": 255}, {"uid": "737acc91162c7859", "name": "test_completed", "source": "737acc91162c7859.png", "type": "image/png", "size": 149622}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "执行命令: get all the alarms", "time": {"start": 1756119058889, "stop": 1756119085494, "duration": 26605}, "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "time": {"start": 1756119058889, "stop": 1756119085274, "duration": 26385}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119085274, "stop": 1756119085493, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "23529e1843bf7b26", "name": "测试总结", "source": "23529e1843bf7b26.txt", "type": "text/plain", "size": 224}, {"uid": "1f872c60c1e292e8", "name": "test_completed", "source": "1f872c60c1e292e8.png", "type": "image/png", "size": 145459}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119085494, "stop": 1756119085498, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119085498, "stop": 1756119085738, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "431e3490fa032af2", "name": "测试总结", "source": "431e3490fa032af2.txt", "type": "text/plain", "size": 229}, {"uid": "e7c4303f84fe574e", "name": "test_completed", "source": "e7c4303f84fe574e.png", "type": "image/png", "size": 146509}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f1845d34add8245", "name": "stdout", "source": "f1845d34add8245.txt", "type": "text/plain", "size": 41172}], "parameters": [], "attachmentsCount": 11, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 14, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119085740, "stop": 1756119085740, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119085741, "stop": 1756119087074, "duration": 1333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_off_the_8_am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_off_the_8_am_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f2fc9ffe68ff546c.json", "parameterValues": []}