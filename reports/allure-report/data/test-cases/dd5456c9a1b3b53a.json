{"uid": "dd5456c9a1b3b53a", "name": "测试max ring volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_max_ring_volume.TestEllaMaxRingVolume#test_max_ring_volume", "historyId": "a54454fcb441a45b0e29dcbbf21679aa", "time": {"start": 1756124917456, "stop": 1756124943791, "duration": 26335}, "description": "max ring volume", "descriptionHtml": "<p>max ring volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756124904769, "stop": 1756124917455, "duration": 12686}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756124917455, "stop": 1756124917455, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "max ring volume", "status": "passed", "steps": [{"name": "执行命令: max ring volume", "time": {"start": 1756124917456, "stop": 1756124943533, "duration": 26077}, "status": "passed", "steps": [{"name": "执行命令: max ring volume", "time": {"start": 1756124917456, "stop": 1756124943261, "duration": 25805}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124943261, "stop": 1756124943532, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "7849266f5ef9b74c", "name": "测试总结", "source": "7849266f5ef9b74c.txt", "type": "text/plain", "size": 300}, {"uid": "c698b8fe553a15ad", "name": "test_completed", "source": "c698b8fe553a15ad.png", "type": "image/png", "size": 170485}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756124943534, "stop": 1756124943538, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证应用已打开", "time": {"start": 1756124943538, "stop": 1756124943538, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756124943538, "stop": 1756124943789, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "b36b1dad416f2b10", "name": "测试总结", "source": "b36b1dad416f2b10.txt", "type": "text/plain", "size": 300}, {"uid": "c6d360f59a489eec", "name": "test_completed", "source": "c6d360f59a489eec.png", "type": "image/png", "size": 170485}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f8c64eb7e6cfc29e", "name": "stdout", "source": "f8c64eb7e6cfc29e.txt", "type": "text/plain", "size": 13841}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756124943794, "stop": 1756124943794, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756124943797, "stop": 1756124945135, "duration": 1338}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_ring_volume"}, {"name": "subSuite", "value": "TestEllaMaxRingVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_ring_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "dd5456c9a1b3b53a.json", "parameterValues": []}