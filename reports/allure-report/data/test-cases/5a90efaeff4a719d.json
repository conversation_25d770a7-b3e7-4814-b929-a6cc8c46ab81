{"uid": "5a90efaeff4a719d", "name": "测试new year wishs能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_new_year_wishs.TestEllaNewYearWishs#test_new_year_wishs", "historyId": "b8a3cad490db8fccbe1d65628faf6743", "time": {"start": 1756134506264, "stop": 1756134535932, "duration": 29668}, "description": "new year wishs", "descriptionHtml": "<p>new year wishs</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756134493324, "stop": 1756134506263, "duration": 12939}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756134506263, "stop": 1756134506263, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "new year wishs", "status": "passed", "steps": [{"name": "执行命令: new year wishs", "time": {"start": 1756134506264, "stop": 1756134535674, "duration": 29410}, "status": "passed", "steps": [{"name": "执行命令: new year wishs", "time": {"start": 1756134506264, "stop": 1756134535407, "duration": 29143}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134535407, "stop": 1756134535674, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "f861ca6164803c23", "name": "测试总结", "source": "f861ca6164803c23.txt", "type": "text/plain", "size": 308}, {"uid": "d9b73a57e3152a0a", "name": "test_completed", "source": "d9b73a57e3152a0a.png", "type": "image/png", "size": 184348}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756134535674, "stop": 1756134535675, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756134535675, "stop": 1756134535931, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "3d9ff70256b77874", "name": "测试总结", "source": "3d9ff70256b77874.txt", "type": "text/plain", "size": 308}, {"uid": "fd057f981fb20b6a", "name": "test_completed", "source": "fd057f981fb20b6a.png", "type": "image/png", "size": 184362}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2036f63ea3f2f859", "name": "stdout", "source": "2036f63ea3f2f859.txt", "type": "text/plain", "size": 12780}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756134535933, "stop": 1756134535933, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756134535934, "stop": 1756134537437, "duration": 1503}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_new_year_wishs"}, {"name": "subSuite", "value": "TestEllaNewYearWishs"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_new_year_wishs"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5a90efaeff4a719d.json", "parameterValues": []}