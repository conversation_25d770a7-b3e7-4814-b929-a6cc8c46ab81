{"uid": "54395702e8fafb92", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings.TestEllaJumpAdaptiveBrightnessSettings#test_jump_to_adaptive_brightness_settings", "historyId": "c796c03cca51cea23bdc87f3f9d6fa95", "time": {"start": 1756133527259, "stop": 1756133562659, "duration": 35400}, "description": "验证jump to adaptive brightness settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to adaptive brightness settings指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to adaptive brightness settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings.TestEllaJumpAdaptiveBrightnessSettings object at 0x0000029205370190>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209689610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_adaptive_brightness_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to adaptive brightness settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_adaptive_brightness_settings.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756133514488, "stop": 1756133527256, "duration": 12768}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756133527257, "stop": 1756133527257, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证jump to adaptive brightness settings指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to adaptive brightness settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature.']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings.TestEllaJumpAdaptiveBrightnessSettings object at 0x0000029205370190>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029209689610>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_adaptive_brightness_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to adaptive brightness settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_adaptive_brightness_settings.py:33: AssertionError", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "time": {"start": 1756133527259, "stop": 1756133562654, "duration": 35395}, "status": "passed", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "time": {"start": 1756133527259, "stop": 1756133562342, "duration": 35083}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756133562342, "stop": 1756133562653, "duration": 311}, "status": "passed", "steps": [], "attachments": [{"uid": "8565c5c6292ba29d", "name": "测试总结", "source": "8565c5c6292ba29d.txt", "type": "text/plain", "size": 688}, {"uid": "9c7947c58a62a399", "name": "test_completed", "source": "9c7947c58a62a399.png", "type": "image/png", "size": 178445}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756133562654, "stop": 1756133562658, "duration": 4}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['jump to adaptive brightness settings', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Display & Brightness | Light Theme | Dark Theme | Schedule | Auto-switch between dark and light themes. The dark theme is more power-efficient. | Screen Brightness | Adaptive Brightness | Screen Preferences | Eye Care | High Brightness Mode | Increase screen brightness in strong light, with an increase in power consumption and temperature.']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_adaptive_brightness_settings.py\", line 33, in test_jump_to_adaptive_brightness_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "c1a9a288c5cdf6ee", "name": "stdout", "source": "c1a9a288c5cdf6ee.txt", "type": "text/plain", "size": 17174}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756133562664, "stop": 1756133562896, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "d13300993508d74c", "name": "失败截图-TestEllaJumpAdaptiveBrightnessSettings", "source": "d13300993508d74c.png", "type": "image/png", "size": 178411}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756133562897, "stop": 1756133564365, "duration": 1468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_adaptive_brightness_settings"}, {"name": "subSuite", "value": "TestEllaJumpAdaptiveBrightnessSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "54395702e8fafb92.json", "parameterValues": []}