{"uid": "5bf3fa4939157896", "name": "测试how is the weather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday#test_how_is_the_weather_today", "historyId": "3004e41c81a7ebd857f79d043aaf59df", "time": {"start": 1756119938064, "stop": 1756119970119, "duration": 32055}, "description": "how is the weather today", "descriptionHtml": "<p>how is the weather today</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday object at 0x00000292042A97D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206BE1610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_weather_today.py:39: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119925045, "stop": 1756119938061, "duration": 13016}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119938061, "stop": 1756119938061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "how is the weather today", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday object at 0x00000292042A97D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029206BE1610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_weather_today.py:39: AssertionError", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1756119938064, "stop": 1756119970095, "duration": 32031}, "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1756119938064, "stop": 1756119969845, "duration": 31781}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119969845, "stop": 1756119970095, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "70972ecf15d4dcd9", "name": "测试总结", "source": "70972ecf15d4dcd9.txt", "type": "text/plain", "size": 318}, {"uid": "4047a2b5890402e", "name": "test_completed", "source": "4047a2b5890402e.png", "type": "image/png", "size": 175426}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119970095, "stop": 1756119970114, "duration": 19}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_how_is_the_weather_today.py\", line 39, in test_how_is_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "9ca6a9b67ee09cbd", "name": "stdout", "source": "9ca6a9b67ee09cbd.txt", "type": "text/plain", "size": 14463}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119970130, "stop": 1756119970350, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "a21571e9b106fc8f", "name": "失败截图-TestEllaHowIsWeatherToday", "source": "a21571e9b106fc8f.png", "type": "image/png", "size": 175426}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756119970351, "stop": 1756119971713, "duration": 1362}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "5bf3fa4939157896.json", "parameterValues": []}