{"uid": "e521660fcfc758d3", "name": "测试set languages返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_languages.TestEllaSetLanguages#test_set_languages", "historyId": "00e9182de9c9d3297d90ff42d6771a57", "time": {"start": 1756136703140, "stop": 1756136737962, "duration": 34822}, "description": "验证set languages指令返回预期的不支持响应", "descriptionHtml": "<p>验证set languages指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set languages', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Languages\\xa0& Input | Languages | English (United States) | Keyboards | On-Screen Keyboard | Gboard, Emoji Keyboard, and Google Voice Typing | Secure Keyboard | Tools | Spell Checker | Gboard spell checker | Personal Dictionary | Add words to be used in apps such as Spell checker | Pointer Speed | Text-to-Speech Output']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_languages.TestEllaSetLanguages object at 0x0000029205705290>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A868B90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_languages(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set languages', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Languages\\xa0& Input | Languages | English (United States) | Keyboards | On-Screen Keyboard | Gboard, Emoji Keyboard, and Google Voice Typing | Secure Keyboard | Tools | Spell Checker | Gboard spell checker | Personal Dictionary | Add words to be used in apps such as Spell checker | Pointer Speed | Text-to-Speech Output']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_languages.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136690500, "stop": 1756136703138, "duration": 12638}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136703139, "stop": 1756136703139, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set languages指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set languages', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Languages\\xa0& Input | Languages | English (United States) | Keyboards | On-Screen Keyboard | Gboard, Emoji Keyboard, and Google Voice Typing | Secure Keyboard | Tools | Spell Checker | Gboard spell checker | Personal Dictionary | Add words to be used in apps such as Spell checker | Pointer Speed | Text-to-Speech Output']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_set_languages.TestEllaSetLanguages object at 0x0000029205705290>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002920A868B90>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_languages(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['set languages', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Languages\\xa0& Input | Languages | English (United States) | Keyboards | On-Screen Keyboard | Gboard, Emoji Keyboard, and Google Voice Typing | Secure Keyboard | Tools | Spell Checker | Gboard spell checker | Personal Dictionary | Add words to be used in apps such as Spell checker | Pointer Speed | Text-to-Speech Output']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_languages.py:33: AssertionError", "steps": [{"name": "执行命令: set languages", "time": {"start": 1756136703140, "stop": 1756136737958, "duration": 34818}, "status": "passed", "steps": [{"name": "执行命令: set languages", "time": {"start": 1756136703140, "stop": 1756136737701, "duration": 34561}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136737701, "stop": 1756136737958, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "5c04e5e204110354", "name": "测试总结", "source": "5c04e5e204110354.txt", "type": "text/plain", "size": 622}, {"uid": "852f154e04373a38", "name": "test_completed", "source": "852f154e04373a38.png", "type": "image/png", "size": 172522}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136737958, "stop": 1756136737961, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['set languages', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Languages\\xa0& Input | Languages | English (United States) | Keyboards | On-Screen Keyboard | Gboard, Emoji Keyboard, and Google Voice Typing | Secure Keyboard | Tools | Spell Checker | Gboard spell checker | Personal Dictionary | Add words to be used in apps such as Spell checker | Pointer Speed | Text-to-Speech Output']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_languages.py\", line 33, in test_set_languages\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "1f6224816ecc8b6d", "name": "stdout", "source": "1f6224816ecc8b6d.txt", "type": "text/plain", "size": 16269}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 4, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136737966, "stop": 1756136738203, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "fe7021f8dd7d1116", "name": "失败截图-TestEllaSetLanguages", "source": "fe7021f8dd7d1116.png", "type": "image/png", "size": 173630}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756136738204, "stop": 1756136739662, "duration": 1458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_languages"}, {"name": "subSuite", "value": "TestEllaSetLanguages"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_languages"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "e521660fcfc758d3.json", "parameterValues": []}