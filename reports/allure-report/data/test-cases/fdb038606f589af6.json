{"uid": "fdb038606f589af6", "name": "测试play the album", "fullName": "testcases.test_ella.unsupported_commands.test_play_the_album.TestEllaOpenPlayPoliticalNews#test_play_the_album", "historyId": "ab0169efb30d26689cbce230ff455598", "time": {"start": 1756135277283, "stop": 1756135322522, "duration": 45239}, "description": "测试play the album指令", "descriptionHtml": "<p>测试play the album指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756135264540, "stop": 1756135277280, "duration": 12740}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756135277280, "stop": 1756135277280, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "测试play the album指令", "status": "passed", "steps": [{"name": "执行命令: play the album", "time": {"start": 1756135277283, "stop": 1756135322267, "duration": 44984}, "status": "passed", "steps": [{"name": "执行命令: play the album", "time": {"start": 1756135277283, "stop": 1756135321976, "duration": 44693}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135321976, "stop": 1756135322267, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "f9d81ff229448886", "name": "测试总结", "source": "f9d81ff229448886.txt", "type": "text/plain", "size": 598}, {"uid": "a9bcb5663a68789", "name": "test_completed", "source": "a9bcb5663a68789.png", "type": "image/png", "size": 168115}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756135322268, "stop": 1756135322269, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证youtube已打开", "time": {"start": 1756135322269, "stop": 1756135322269, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756135322269, "stop": 1756135322521, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "698b0a4abbfe8f7a", "name": "测试总结", "source": "698b0a4abbfe8f7a.txt", "type": "text/plain", "size": 598}, {"uid": "853062bc3a36deb4", "name": "test_completed", "source": "853062bc3a36deb4.png", "type": "image/png", "size": 168115}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "ff1630a81e3c6f48", "name": "stdout", "source": "ff1630a81e3c6f48.txt", "type": "text/plain", "size": 15824}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 6, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756135322522, "stop": 1756135322522, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756135322523, "stop": 1756135324021, "duration": 1498}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_the_album"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_the_album"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fdb038606f589af6.json", "parameterValues": []}