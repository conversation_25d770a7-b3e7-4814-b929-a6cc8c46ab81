{"uid": "4729af2b7af7a64d", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango#test_switch_magic_voice_to_mango", "historyId": "9420fd606a04614c6f09bf36f2873f93", "time": {"start": 1756126583163, "stop": 1756126608646, "duration": 25483}, "description": "switch magic voice to Mango", "descriptionHtml": "<p>switch magic voice to Mango</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756126570134, "stop": 1756126583161, "duration": 13027}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756126583161, "stop": 1756126583161, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "switch magic voice to Mango", "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "time": {"start": 1756126583163, "stop": 1756126608406, "duration": 25243}, "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "time": {"start": 1756126583163, "stop": 1756126608122, "duration": 24959}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126608123, "stop": 1756126608405, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "a85afb57849fa710", "name": "测试总结", "source": "a85afb57849fa710.txt", "type": "text/plain", "size": 311}, {"uid": "4a4fae6f7d46653", "name": "test_completed", "source": "4a4fae6f7d46653.png", "type": "image/png", "size": 179153}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含在期望中", "time": {"start": 1756126608406, "stop": 1756126608408, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756126608408, "stop": 1756126608645, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "9e5d312d2f15eb53", "name": "测试总结", "source": "9e5d312d2f15eb53.txt", "type": "text/plain", "size": 311}, {"uid": "34d0bbf2f46e6200", "name": "test_completed", "source": "34d0bbf2f46e6200.png", "type": "image/png", "size": 179153}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "f131f10a9e9662a1", "name": "stdout", "source": "f131f10a9e9662a1.txt", "type": "text/plain", "size": 12825}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756126608646, "stop": 1756126608646, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756126608648, "stop": 1756126610026, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_mango"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceToMango"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4729af2b7af7a64d.json", "parameterValues": []}