{"uid": "ba9ab71b348d1e88", "name": "测试stop run能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_run.TestEllaStopRun#test_stop_run", "historyId": "30903d6e764eebda77a45c5af4464d00", "time": {"start": 1756121741485, "stop": 1756121767754, "duration": 26269}, "description": "stop run", "descriptionHtml": "<p>stop run</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756121728139, "stop": 1756121741481, "duration": 13342}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756121741482, "stop": 1756121741482, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "stop run", "status": "passed", "steps": [{"name": "执行命令: stop run", "time": {"start": 1756121741485, "stop": 1756121767504, "duration": 26019}, "status": "passed", "steps": [{"name": "执行命令: stop run", "time": {"start": 1756121741485, "stop": 1756121767187, "duration": 25702}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121767187, "stop": 1756121767503, "duration": 316}, "status": "passed", "steps": [], "attachments": [{"uid": "1c22b0b087379f3a", "name": "测试总结", "source": "1c22b0b087379f3a.txt", "type": "text/plain", "size": 282}, {"uid": "814fff4e3f1bf4d", "name": "test_completed", "source": "814fff4e3f1bf4d.png", "type": "image/png", "size": 167264}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756121767504, "stop": 1756121767508, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756121767508, "stop": 1756121767753, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "74c6f116c0affe7", "name": "测试总结", "source": "74c6f116c0affe7.txt", "type": "text/plain", "size": 282}, {"uid": "24184a7272124c15", "name": "test_completed", "source": "24184a7272124c15.png", "type": "image/png", "size": 167264}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "edfb7c83ae630684", "name": "stdout", "source": "edfb7c83ae630684.txt", "type": "text/plain", "size": 12400}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756121767756, "stop": 1756121767756, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756121767761, "stop": 1756121769175, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_run"}, {"name": "subSuite", "value": "TestEllaStopRun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_run"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ba9ab71b348d1e88.json", "parameterValues": []}