{"uid": "6d4b8e7daf7c7138", "name": "测试set folding screen zone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone.TestEllaSetFoldingScreenZone#test_set_folding_screen_zone", "historyId": "c04d9357fdaf44e6ee27f8a97ece6c5d", "time": {"start": 1756136563233, "stop": 1756136589355, "duration": 26122}, "description": "验证set folding screen zone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set folding screen zone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756136550496, "stop": 1756136563233, "duration": 12737}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756136563233, "stop": 1756136563233, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "验证set folding screen zone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1756136563234, "stop": 1756136589124, "duration": 25890}, "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1756136563234, "stop": 1756136588874, "duration": 25640}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136588874, "stop": 1756136589123, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "f53cbf16928faa1b", "name": "测试总结", "source": "f53cbf16928faa1b.txt", "type": "text/plain", "size": 348}, {"uid": "c3014b6142c4b5f7", "name": "test_completed", "source": "c3014b6142c4b5f7.png", "type": "image/png", "size": 174731}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756136589124, "stop": 1756136589125, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756136589125, "stop": 1756136589355, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "61420269e6596ee3", "name": "测试总结", "source": "61420269e6596ee3.txt", "type": "text/plain", "size": 348}, {"uid": "7ecd5fe95751decd", "name": "test_completed", "source": "7ecd5fe95751decd.png", "type": "image/png", "size": 174731}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "2d474e24eca07150", "name": "stdout", "source": "2d474e24eca07150.txt", "type": "text/plain", "size": 12639}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756136589356, "stop": 1756136589356, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756136589358, "stop": 1756136590833, "duration": 1475}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_folding_screen_zone"}, {"name": "subSuite", "value": "TestEllaSetFoldingScreenZone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6d4b8e7daf7c7138.json", "parameterValues": []}