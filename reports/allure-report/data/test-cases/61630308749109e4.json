{"uid": "61630308749109e4", "name": "测试take a selfie能正常执行", "fullName": "testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie#test_take_a_selfie", "historyId": "6c46a38570672e3c21f37ef82690d639", "time": {"start": 1756127070933, "stop": 1756127126076, "duration": 55143}, "description": "take a selfie", "descriptionHtml": "<p>take a selfie</p>\n", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie object at 0x0000029204BC7490>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208289610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_selfie(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=True\n            )\n    \n        with allure.step(f\"验证应用已打开\"):\n            assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_take_a_selfie.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756127057939, "stop": 1756127070932, "duration": 12993}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756127070933, "stop": 1756127070933, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "take a selfie", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie object at 0x0000029204BC7490>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000029208289610>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_selfie(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=True\n            )\n    \n        with allure.step(f\"验证应用已打开\"):\n            assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_take_a_selfie.py:35: AssertionError", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1756127070933, "stop": 1756127126074, "duration": 55141}, "status": "passed", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1756127070933, "stop": 1756127125801, "duration": 54868}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756127125801, "stop": 1756127126073, "duration": 272}, "status": "passed", "steps": [], "attachments": [{"uid": "7d8cbd001ab7bb51", "name": "测试总结", "source": "7d8cbd001ab7bb51.txt", "type": "text/plain", "size": 391}, {"uid": "49486dca3b88dffb", "name": "test_completed", "source": "49486dca3b88dffb.png", "type": "image/png", "size": 173652}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证应用已打开", "time": {"start": 1756127126074, "stop": 1756127126074, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "验证文件存在", "time": {"start": 1756127126074, "stop": 1756127126074, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_take_a_selfie.py\", line 35, in test_take_a_selfie\n    assert files_status, f\"文件不存在！\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "3845660cfbaef020", "name": "stdout", "source": "3845660cfbaef020.txt", "type": "text/plain", "size": 15840}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": true, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756127126083, "stop": 1756127126385, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "6aabf53d0859dbda", "name": "失败截图-TestEllaTakeSelfie", "source": "6aabf53d0859dbda.png", "type": "image/png", "size": 173651}], "parameters": [], "attachmentsCount": 1, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}, {"name": "ella_app::0", "time": {"start": 1756127126388, "stop": 1756127127770, "duration": 1382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_selfie"}, {"name": "subSuite", "value": "TestEllaTakeSelfie"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_selfie"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "61630308749109e4.json", "parameterValues": []}