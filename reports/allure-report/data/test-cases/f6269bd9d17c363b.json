{"uid": "f6269bd9d17c363b", "name": "测试close whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_close_whatsapp.TestEllaCloseWhatsapp#test_close_whatsapp", "historyId": "876e77318cece5d1079b726f0c97bc45", "time": {"start": 1756119518006, "stop": 1756119545026, "duration": 27020}, "description": "close whatsapp", "descriptionHtml": "<p>close whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756117114456, "stop": 1756117114458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app", "time": {"start": 1756119505482, "stop": 1756119518004, "duration": 12522}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756119518004, "stop": 1756119518004, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "testStage": {"description": "close whatsapp", "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1756119518006, "stop": 1756119544816, "duration": 26810}, "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1756119518006, "stop": 1756119544556, "duration": 26550}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119544556, "stop": 1756119544816, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "fc53b534c13f465c", "name": "测试总结", "source": "fc53b534c13f465c.txt", "type": "text/plain", "size": 263}, {"uid": "37ee48493ea566b3", "name": "test_completed", "source": "37ee48493ea566b3.png", "type": "image/png", "size": 164336}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 2, "hasContent": true}, {"name": "验证响应包含期望内容", "time": {"start": 1756119544816, "stop": 1756119544818, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1756119544818, "stop": 1756119545026, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "92d99f1ee6f61966", "name": "测试总结", "source": "92d99f1ee6f61966.txt", "type": "text/plain", "size": 263}, {"uid": "15d11659d6c45681", "name": "test_completed", "source": "15d11659d6c45681.png", "type": "image/png", "size": 164336}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": true}], "attachments": [{"uid": "def5649d7803a26f", "name": "stdout", "source": "def5649d7803a26f.txt", "type": "text/plain", "size": 12774}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 5, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756119545027, "stop": 1756119545027, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1756119545028, "stop": 1756119546365, "duration": 1337}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1756139260989, "stop": 1756139260991, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "attachmentStep": false, "stepsCount": 0, "hasContent": false}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_close_whatsapp"}, {"name": "subSuite", "value": "TestEllaCloseWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "78240-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_close_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f6269bd9d17c363b.json", "parameterValues": []}